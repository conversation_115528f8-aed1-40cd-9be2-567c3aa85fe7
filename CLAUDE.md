# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

JIRA Bug Browser is a sophisticated multi-tenant SaaS web application that serves as a streamlined interface for authenticated users to browse JIRA projects and drill into individual Bug issues. The application has evolved beyond its initial MVP to include AI-powered bug analysis and automated pull request creation capabilities.

### Core Purpose
- **Enterprise Issue Tracking**: Simplifies JIRA bug browsing with a clean, focused interface
- **Multi-Organization Support**: Each tenant (customer) connects their own JIRA instance
- **Security-First Approach**: Complete data isolation between tenants at all levels
- **AI-Powered Bug Fixing**: Automated analysis and pull request generation for bug resolution
- **Repository Management**: Link Git repositories to JIRA projects for seamless code integration

### Key Components
1. **Flask Backend**: RESTful API with JWT authentication, SQLite/PostgreSQL database
2. **React Frontend**: Single-page application with Material-UI and Kasavu-inspired theme
3. **JIRA Integration**: Secure credential storage with AES-256-GCM encryption
4. **Multi-Tenant Architecture**: Row-level security with optional schema-per-tenant
5. **AI Agent System**: Bug analysis and automated pull request generation
6. **Repository Management**: Git repository linking and local path management
7. **Functional Areas**: Logical grouping of multiple JIRA projects and repositories

### Stakeholders
- **Tenant Admin**: Manages users and JIRA connection for their organization
- **End Users**: Browse projects and view bug issues within their tenant
- **Platform Admin**: Manages tenants across the entire platform
- **Product Owner**: Defines feature backlog and priorities

## Technical Architecture

### Backend (Flask + SQLAlchemy)
- **Framework**: Flask with RESTful API design
- **Database**: SQLite (development) / PostgreSQL (production planned)
- **ORM**: SQLAlchemy with multi-tenant data model
- **Authentication**: JWT tokens with 15-min access / 7-day refresh
- **Encryption**: AES-256-GCM for JIRA credentials
- **Testing**: pytest with comprehensive test suite
- **CORS**: Configured for frontend integration
- **Agent Integration**: AI-powered bug analysis and PR automation system

### Frontend (React + Material-UI)
- **Framework**: React 18 with React Router
- **UI Library**: Material-UI v6 with Kasavu-inspired theme
- **State Management**: React Context for auth state
- **API Client**: Axios with JWT interceptors  
- **Build Tool**: Create React App
- **Testing**: Jest + React Testing Library

### Security Features
- **Tenant Isolation**: Row-level security with tenant_id filtering
- **Cross-tenant Protection**: Requests validated against JWT tenant context
- **Password Hashing**: Argon2 for secure password storage
- **Credential Encryption**: AES-256-GCM for JIRA API keys
- **CORS Configuration**: Origin restrictions to frontend URLs

## Repository Structure

### Backend (`/backend`)
```
backend/
├── app.py              # Flask app initialization & configuration
├── agents/             # AI agent system
│   ├── bug_analyzer.py # Bug analysis agent
│   ├── developer_agent.py # Developer/PR automation agent
│   └── outputs/        # Agent output storage
├── blueprints/         # API route modules
│   ├── auth.py        # Authentication endpoints
│   ├── agent_execution.py # AI agent execution endpoints
│   ├── issues.py      # Issue browsing endpoints  
│   ├── jira.py        # JIRA credential management
│   ├── projects.py    # Project listing endpoints
│   ├── tenants.py     # Admin tenant management
│   ├── repository_management.py # Repository configuration
│   ├── fix_repositories.py # Repository fix automation
│   ├── fix_pull_requests.py # PR automation
│   └── public_tenants.py # Public tenant resolution
├── models.py          # SQLAlchemy models (Tenant, User, etc.)
├── services/          # Business logic services
│   ├── repository_management_service.py
│   ├── repository_service.py
│   └── pr_service.py
├── config.py          # App configuration & environment vars
├── utils.py           # Helper functions (encryption, JWT)
├── migrations/        # Alembic database migrations
├── tests/             # pytest test suite
├── tools/             # Utility scripts
└── requirements.txt   # Python dependencies
```

### Frontend (`/frontend`)  
```
frontend/
├── src/
│   ├── App.js         # Main app component & routing
│   ├── theme.js       # Material-UI Kasavu theme configuration
│   ├── index.js       # App entry point
│   ├── layouts/       
│   │   └── MainLayout.js  # Main app layout
│   ├── views/         # Page components
│   │   ├── LoginPage.js
│   │   ├── DashboardPage.js
│   │   ├── ProjectsPage.js
│   │   ├── ProjectsPageV2.js # Enhanced with repository links
│   │   ├── ProjectsPageV3.js # Latest version
│   │   ├── IssuesPage.js
│   │   ├── IssueDetailsPage.js
│   │   ├── JiraSettingsPage.js
│   │   ├── RepositoriesPage.js
│   │   ├── RepositoryManagementPage.js
│   │   └── RepositorySourcesPage.js
│   ├── components/    # Reusable components
│   │   ├── BugFixConsole.js
│   │   ├── ProjectConfigDialog.js
│   │   ├── ProjectRepositoryDialog.js
│   │   ├── RepositoryBrowserDialog.js
│   │   ├── RepositoryPathDialog.js
│   │   └── UserRepositoryPathDialog.js
│   ├── services/      
│   │   └── api.js     # API client & JWT handling
│   └── __tests__/     # Jest test files
├── public/            # Static assets
├── package.json       # Node dependencies
└── README.md         # Frontend documentation
```

## Development Commands

### Backend (Flask)

```bash
# Setup virtual environment (first time)
cd backend
python -m venv venv
source venv/bin/activate  # On Windows: .\venv\Scripts\activate
pip install -r requirements.txt

# Run database migrations
flask db init  # First time only
flask db migrate -m "Migration message"
flask db upgrade

# Database seeding (if needed)
python seed.py

# Run development server
cd ..  # Return to project root
./run_backend.sh  # Runs Flask on port 5045

# Set required environment variables
export APPLICATION_AES_KEY="00112233445566778899aabbccddeeff00112233445566778899aabbccddeeff"

# Run tests
cd backend
python -m pytest
```

### Frontend (React)

```bash
# Install dependencies
cd frontend
npm install

# Run development server
npm start  # Runs on port 3000, connects to backend on port 5045

# Run tests
npm test
```

## Database Schema

The application uses SQLAlchemy with a robust multi-tenant data model. All tables include `tenant_id` for isolation.

### Core Tables

#### `tenants`
- `id` (UUID, PK): Unique tenant identifier
- `name` (String): Organization name
- `plan` (String): Subscription tier (e.g., 'starter', 'enterprise')
- `created_at` (DateTime): Tenant creation timestamp
- **Relationships**: Users, JIRA credentials, caches, tokens

#### `users`  
- `id` (UUID, PK): Unique user identifier
- `tenant_id` (UUID, FK → tenants): Tenant ownership
- `email` (String, Unique): Login email (unique per tenant)
- `password_hash` (String): Argon2 hashed password
- `role` (String): User role ('user', 'admin', 'tenant_admin')
- `created_at` (DateTime): User creation timestamp
- **Constraints**: Email unique within tenant scope

#### `jira_credentials`
- `id` (Integer, PK): Credential ID
- `tenant_id` (UUID, FK → tenants): Tenant ownership
- `user_id` (UUID, FK → users, Nullable): Optional user scope
- `base_url` (String): JIRA instance URL
- `email` (String): JIRA account email
- `api_key_encrypted` (String): AES-256-GCM encrypted API token
- `updated_at` (DateTime): Last update timestamp

#### `project_cache`
- `id` (Integer, PK): Cache entry ID
- `tenant_id` (UUID, FK → tenants): Tenant ownership
- `project_key` (String): JIRA project key
- `json_data` (JSON): Cached project data
- `fetched_at` (DateTime): Cache timestamp
- **TTL**: 30 seconds cache duration
- **Unique Constraint**: (`tenant_id`, `project_key`)

#### `issue_cache`
- `id` (Integer, PK): Cache entry ID  
- `tenant_id` (UUID, FK → tenants): Tenant ownership
- `issue_key` (String): JIRA issue key
- `json_data` (JSON): Cached issue data
- `fetched_at` (DateTime): Cache timestamp

#### `refresh_tokens`
- `id` (Integer, PK): Token ID
- `tenant_id` (UUID, FK → tenants): Tenant ownership  
- `user_id` (UUID, FK → users): Token owner
- `token_hash` (String): SHA-256 hashed token
- `expires_at` (DateTime): Token expiration
- `created_at` (DateTime): Token creation timestamp

#### `user_activity`
- `id` (Integer, PK): Activity ID
- `tenant_id` (UUID, FK → tenants): Tenant ownership
- `user_id` (UUID, FK → users): Acting user
- `activity_type` (String): Action type
- `resource_type` (String): Target resource
- `resource_id` (String): Target ID
- `created_at` (DateTime): Activity timestamp

#### `repositories`
- `id` (UUID, PK): Repository ID
- `tenant_id` (UUID, FK → tenants): Tenant ownership
- `name` (String): Repository name
- `repo_type` (String): Type ('github', 'bitbucket', 'gitlab')
- `remote_url` (String): Git remote URL
- `default_branch` (String): Default branch name
- `credentials_encrypted` (Text): Encrypted API credentials
- `created_by` (UUID, FK → users): Creator
- `is_active` (Boolean): Active status
- `created_at` (DateTime): Creation timestamp

#### `project_repositories`
- `id` (UUID, PK): Link ID
- `tenant_id` (UUID, FK → tenants): Tenant ownership
- `project_key` (String): JIRA project key
- `repository_id` (UUID, FK → repositories): Repository
- `is_primary` (Boolean): Primary repository for project
- `created_at` (DateTime): Link timestamp

#### `user_repository_paths`
- `id` (UUID, PK): Path ID
- `tenant_id` (UUID, FK → tenants): Tenant ownership
- `user_id` (UUID, FK → users): User
- `repository_id` (UUID, FK → repositories): Repository
- `local_path` (String): Local filesystem path
- `created_at` (DateTime): Creation timestamp
- `updated_at` (DateTime): Last update timestamp


#### `pull_requests`
- `id` (UUID, PK): PR ID
- `tenant_id` (UUID, FK → tenants): Tenant ownership
- `repository_id` (UUID, FK → repositories): Repository
- `issue_key` (String): JIRA issue key
- `pr_number` (Integer): Platform PR number
- `pr_url` (String): Platform PR URL
- `title` (String): PR title
- `source_branch` (String): Feature branch
- `target_branch` (String): Base branch
- `status` (String): PR status ('pending', 'created', 'merged', 'closed')
- `metadata` (JSON): Platform-specific data
- `created_at` (DateTime): Creation timestamp

#### `repository_activity`
- `id` (UUID, PK): Activity ID
- `tenant_id` (UUID, FK → tenants): Tenant ownership
- `repository_id` (UUID, FK → repositories): Repository
- `activity_type` (String): Action ('clone', 'pull', 'push', 'pr_created', 'ai_analysis')
- `status` (String): Result ('success', 'failed')
- `details` (JSON): Activity details
- `user_id` (UUID, FK → users): Actor
- `created_at` (DateTime): Activity timestamp

#### `functional_areas`
- `id` (UUID, PK): Functional area ID
- `tenant_id` (UUID, FK → tenants): Tenant ownership
- `name` (String): Area name (e.g., 'E-commerce Platform', 'User Authentication')
- `description` (Text): Detailed description
- `category` (String): Optional category ('Frontend', 'Backend', 'Full-Stack', etc.)
- `priority` (String): Priority level ('high', 'medium', 'low')
- `is_active` (Boolean): Active status
- `created_at` (DateTime): Creation timestamp
- `updated_at` (DateTime): Last update timestamp
- `created_by` (UUID, FK → users): Creator
- `metadata_json` (JSON): Additional metadata

#### `functional_area_projects`
- `id` (UUID, PK): Link ID
- `tenant_id` (UUID, FK → tenants): Tenant ownership
- `functional_area_id` (UUID, FK → functional_areas): Functional area
- `project_key` (String): JIRA project key
- `project_role` (String): Role within area ('backend', 'frontend', 'mobile-app', etc.)
- `is_primary` (Boolean): Primary project for this area
- `linked_at` (DateTime): Link timestamp
- `linked_by` (UUID, FK → users): User who created link

#### `functional_area_repositories`
- `id` (UUID, PK): Link ID
- `tenant_id` (UUID, FK → tenants): Tenant ownership
- `functional_area_id` (UUID, FK → functional_areas): Functional area
- `repository_id` (UUID, FK → repositories): Repository
- `repository_role` (String): Role within area ('backend-api', 'frontend-web', 'mobile-ios', etc.)
- `is_primary` (Boolean): Primary repository for this area
- `linked_at` (DateTime): Link timestamp
- `linked_by` (UUID, FK → users): User who created link

### Security Implementation
- **Row-Level Security**: All queries filtered by `tenant_id`
- **FK Constraints**: Enforced tenant relationships
- **Unique Constraints**: Prevent duplicate entries
- **Indexed Columns**: `tenant_id`, `email`, `project_key` for performance
- **Credential Encryption**: Repository credentials encrypted with AES-256-GCM

## Multi-Tenant Architecture

### Tenant Isolation Strategy
1. **API Namespace**: All endpoints scoped under `/tenants/{tid}/` 
2. **JWT Context**: Every token includes `tenant_id` claim
3. **Database Filtering**: All queries filtered by `tenant_id`
4. **Cross-Tenant Protection**: 403 Forbidden for unauthorized access
5. **Data Segregation**: Complete isolation at row level
6. **Future Options**: Schema-per-tenant for enterprise plans

### Tenant On-boarding Flow
1. **Tenant Creation**: Platform admin creates tenant via `/admin/tenants`
2. **Admin User**: Initial admin user created for tenant
3. **JIRA Configuration**: Admin connects JIRA instance
4. **User Management**: Admin invites team members
5. **Access Control**: Users limited to their tenant's data

## API Specification

### Authentication Endpoints
| Method | Path | Auth Required | Description |
|--------|------|---------------|-------------|
| `POST` | `/tenants/{tid}/auth/login` | No | Login with email/password |
| `POST` | `/tenants/{tid}/auth/refresh` | Refresh Token | Get new access token |
| `POST` | `/tenants/{tid}/auth/logout` | Yes | Invalidate refresh token |

### JIRA Integration Endpoints  
| Method | Path | Auth Required | Description |
|--------|------|---------------|-------------|
| `GET` | `/tenants/{tid}/jira/credentials` | Yes | Check JIRA configuration |
| `POST` | `/tenants/{tid}/jira/credentials` | Tenant Admin | Add/update JIRA credentials |
| `DELETE` | `/tenants/{tid}/jira/credentials` | Tenant Admin | Remove JIRA connection |

### Project & Issue Endpoints
| Method | Path | Auth Required | Description |
|--------|------|---------------|-------------|
| `GET` | `/tenants/{tid}/projects` | Yes | List all projects (cached 30s) |
| `GET` | `/tenants/{tid}/projects/{pkey}/issues` | Yes | Get issues for project (paginated) |
| `GET` | `/tenants/{tid}/issues/{ikey}` | Yes | Get issue details |

### Repository Management Endpoints
| Method | Path | Auth Required | Description |
|--------|------|---------------|-------------|
| `GET` | `/tenants/{tid}/repositories` | Yes | List all repositories |
| `POST` | `/tenants/{tid}/repositories` | Tenant Admin | Add new repository |
| `GET` | `/tenants/{tid}/repositories/{rid}` | Yes | Get repository details |
| `PUT` | `/tenants/{tid}/repositories/{rid}` | Tenant Admin | Update repository |
| `DELETE` | `/tenants/{tid}/repositories/{rid}` | Tenant Admin | Deactivate repository |
| `GET` | `/tenants/{tid}/projects/{pkey}/repositories` | Yes | Get project repositories |
| `POST` | `/tenants/{tid}/projects/{pkey}/repositories/{rid}` | Yes | Link repository to project |
| `DELETE` | `/tenants/{tid}/projects/{pkey}/repositories/{rid}` | Yes | Unlink repository from project |
| `GET` | `/tenants/{tid}/repositories/{rid}/path` | Yes | Get user's local path |
| `POST` | `/tenants/{tid}/repositories/{rid}/path` | Yes | Set user's local path |

### AI Agent Endpoints
| Method | Path | Auth Required | Description |
|--------|------|---------------|-------------|
| `POST` | `/tenants/{tid}/agents/analyze-bug` | Yes | Analyze bug with AI |
| `POST` | `/tenants/{tid}/agents/generate-fix` | Yes | Generate bug fix solution |
| `GET` | `/tenants/{tid}/agents/outputs/{issue_key}` | Yes | Get agent analysis results |

### Functional Area Endpoints
| Method | Path | Auth Required | Description |
|--------|------|---------------|-------------|
| `GET` | `/tenants/{tid}/functional-areas` | Yes | List all functional areas |
| `POST` | `/tenants/{tid}/functional-areas` | Yes | Create new functional area |
| `GET` | `/tenants/{tid}/functional-areas/{aid}` | Yes | Get functional area details |
| `PUT` | `/tenants/{tid}/functional-areas/{aid}` | Yes | Update functional area |
| `DELETE` | `/tenants/{tid}/functional-areas/{aid}` | Yes | Delete functional area |
| `POST` | `/tenants/{tid}/functional-areas/{aid}/projects` | Yes | Link project to area |
| `DELETE` | `/tenants/{tid}/functional-areas/{aid}/projects/{pkey}` | Yes | Unlink project from area |
| `POST` | `/tenants/{tid}/functional-areas/{aid}/repositories` | Yes | Link repository to area |
| `DELETE` | `/tenants/{tid}/functional-areas/{aid}/repositories/{rid}` | Yes | Unlink repository from area |
| `GET` | `/tenants/{tid}/projects/{pkey}/functional-areas` | Yes | Get areas for project |
| `GET` | `/tenants/{tid}/repositories/{rid}/functional-areas` | Yes | Get areas for repository |
| `GET` | `/tenants/{tid}/functional-areas/summary` | Yes | Get functional area summary |

### Pull Request Endpoints
| Method | Path | Auth Required | Description |
|--------|------|---------------|-------------|
| `POST` | `/tenants/{tid}/pull-requests/create` | Yes | Create new PR |
| `GET` | `/tenants/{tid}/pull-requests` | Yes | List PRs (with filters) |
| `GET` | `/tenants/{tid}/pull-requests/{pid}` | Yes | Get PR details |
| `POST` | `/tenants/{tid}/pull-requests/{pid}/sync` | Yes | Sync PR status |
| `GET` | `/tenants/{tid}/issues/{ikey}/pull-requests` | Yes | Get PRs for issue |
| `POST` | `/tenants/{tid}/issues/{ikey}/create-pr` | Yes | Create PR for issue |

### Admin Endpoints
| Method | Path | Auth Required | Description |
|--------|------|---------------|-------------|
| `GET` | `/admin/tenants` | Platform Admin | List all tenants |
| `GET` | `/admin/tenants/{tid}` | Platform Admin | Get tenant details |
| `POST` | `/admin/tenants` | Platform Admin | Create new tenant |

### Public Endpoints
| Method | Path | Auth Required | Description |
|--------|------|---------------|-------------|
| `GET` | `/` | No | API information |
| `GET` | `/health` | No | Health check |
| `GET` | `/api/public/tenants/resolve` | No | Resolve tenant by name |

## Authentication & Security

- JWT tokens contain `tenant_id`, `user_id`, `roles`
- Access tokens expire in 15 minutes
- Refresh tokens expire in 7 days
- Cross-tenant requests are rejected with HTTP 403
- JIRA credentials are encrypted using AES-256-GCM
- All tenant isolation checks must be enforced in every API endpoint

## UI/UX Design System

### Kasavu-Inspired Theme
The UI uses a Kerala-inspired "Monsoon Kasavu" theme that subtly evokes the region's cultural elements:

#### Color Palette
| Token | Hex | Visual Cue | Usage |
|-------|-----|------------|-------|
| `--mk-sea-blue-500` | `#2079A6` | Backwater lagoon | Primary buttons, active states |
| `--mk-monsoon-green-500` | `#2E8F6E` | Monsoon foliage | Success states, accents |
| `--mk-kasavu-gold-400` | `#C9A85D` | Sari border thread | Focus rings, highlights |
| `--mk-mullappoo-white` | `#F7F9F4` | Jasmine blossom | Page background |
| `--mk-arecanut-brown-600` | `#754C24` | Polished wood | Text headings |
| `--mk-shadow-100` | `#E8ECEA` | Monsoon overcast | Card surfaces |

#### Typography
- **Font Family**: Inter, Roboto, sans-serif
- **Base Size**: 14px with 20px line-height
- **Heading Weight**: 600 (semi-bold)
- **Body Weight**: 400 (regular)
- **Font Scale**: Modular scale with clear hierarchy

#### Kasavu-Cut Design
Unique asymmetric border radius inspired by Kerala sari borders:
```css
border-radius: 12px 0 12px 12px; /* Top-right corner square */
```
Applied to all cards, buttons, and panels for visual cohesion.

### Layout Structure

#### Desktop (≥1024px) - Three-Panel Interface
```
┌─────────────────────────────────────────────────────────────┐
│  Global Top Bar (60px) - Tenant context & user menu         │
├─────────────────┬───────────────────────────────────────────┤
│ Projects Sidebar│  Issue Workspace                          │
│    (240px)      │  ┌─────────────────────────────────────┐ │
│ ⌕ Filter...     │  │ Project: WEB (152 bugs)            │ │
│ ─────────────── │  │ Search...         Filters...        │ │
│ • WEB-App       │  │ ─────────────────────────────────── │ │
│ • MOBILE        │  │ [Issue Table - 25 items/page]       │ │
│ • BACKEND       │  └─────────────────────────────────────┘ │
│                 │  ┌─────────────────────────────────────┐ │
│                 │  │ Issue Detail Pane (slide-in)        │ │
│                 │  │ Tabs: Comments | Attachments        │ │
│                 │  └─────────────────────────────────────┘ │
└─────────────────┴───────────────────────────────────────────┘
```

#### Responsive Breakpoints
| Breakpoint | Layout Adaptation |
|------------|------------------|
| Tablet (768-1023px) | Sidebar becomes overlay hamburger menu |
| Mobile (<768px) | Single column, cards instead of table |

### Component Library

#### Core Components
1. **Login Screen**
   - Centered card on pale background
   - Tenant selector prominent
   - Email/password fields with validation

2. **Projects Sidebar**
   - Collapsible with animation (⌘+B)
   - Search filter at top
   - Project cards with bug count badges

3. **Issue Table**
   - Sticky header with sort controls
   - Row hover effects (translateY -2px)
   - Inline actions (open in JIRA)
   - Infinite scroll pagination

4. **Issue Detail Pane**
   - Slides from right (420px max width)
   - Tab navigation for sections
   - Maintains scroll position
   - Close with ESC key

5. **Global Top Bar**
   - Fixed position
   - Tenant badge left-aligned
   - User menu right-aligned
   - Search bar centered

### Interaction Patterns

#### Animations (Framer Motion)
- Panel slides: 150ms ease-out
- Row hovers: 100ms ease
- Skeleton loaders: shimmer effect
- Focus transitions: 200ms

#### Keyboard Navigation  
- `Tab`: Navigate controls
- `k/j`: Move table selection up/down
- `Enter`: Open selected issue
- `ESC`: Close detail pane
- `⌘+K`: Open command palette (future)

#### Loading States
- Skeleton screens for initial loads
- Inline spinners for actions
- Progress bars for long operations
- Error boundaries with retry

### Accessibility Features

#### WCAG 2.1 AA Compliance
- Color contrast: 4.5:1 minimum
- Focus indicators: 2px ring with offset
- ARIA labels on all controls
- Screen reader announcements
- Keyboard-only navigation

#### Dark Mode Support
- `prefers-color-scheme` media query
- Inverted color values maintain hue
- Adjusted shadows for depth
- Preserved contrast ratios

## Security Architecture

### Defense in Depth
Multiple layers of security protect tenant data:

1. **Network Layer**
   - HTTPS/TLS 1.3 required in production
   - CORS restricted to frontend origins
   - Rate limiting per tenant
   - IP allowlisting (optional)

2. **Application Layer**
   - JWT authentication with short expiry
   - Role-based access control (RBAC)
   - Input validation on all endpoints
   - SQL injection prevention via ORM

3. **Data Layer**
   - Row-level security in database
   - Encrypted sensitive fields
   - Audit logging for changes
   - Secure backup procedures

### Encryption Details

#### AES-256-GCM for JIRA Credentials
```python
# Encryption process:
1. Generate random 96-bit nonce
2. Encrypt API token with AES-256-GCM
3. Store: nonce + ciphertext + auth_tag
4. Base64 encode for database storage
```

#### Key Management
- **Development**: Fixed key in `.env` file
- **Production**: AWS KMS or HashiCorp Vault
- **Key Rotation**: Planned quarterly
- **Key Format**: 256-bit hex string

### Authentication Security

#### Password Requirements
- Minimum 12 characters
- Mix of upper/lower/numbers/symbols
- Argon2id hashing with salt
- Password history tracking

#### JWT Token Claims
```json
{
  "sub": "user_uuid",
  "tenant_id": "tenant_uuid",
  "roles": ["user", "admin"],
  "exp": 1234567890,
  "iat": 1234567890
}
```

#### Session Management
- Access tokens: 15 minutes
- Refresh tokens: 7 days
- Token blacklisting on logout
- Concurrent session limits

### Vulnerability Mitigation

| Threat | Mitigation |
|--------|------------|
| SQL Injection | Parameterized queries via SQLAlchemy |
| XSS | React auto-escaping + CSP headers |
| CSRF | SameSite cookies + CSRF tokens |
| Session Hijacking | Secure + HttpOnly cookies |
| Password Attacks | Argon2 + rate limiting |
| Man-in-the-Middle | HSTS + TLS 1.3 only |
| Clickjacking | X-Frame-Options header |

### Compliance Considerations
- **GDPR**: Data minimization + right to deletion
- **SOC 2**: Audit logs + access controls
- **ISO 27001**: Information security controls
- **OWASP Top 10**: Addressed in design

## Error Handling

### RFC 7807 Problem Details Format
All API errors follow a consistent structure:

```json
{
  "type": "/errors/invalid-tenant",
  "title": "Invalid Tenant Context",
  "status": 400,
  "detail": "The tenant ID in the request does not match your authentication context",
  "instance": "/tenants/123/projects"
}
```

### Common Error Scenarios

| Status | Type | Description |
|--------|------|-------------|
| 400 | `/errors/missing-tenant` | Missing tenant_id in JWT |
| 401 | `/errors/invalid-token` | Expired or invalid JWT |
| 403 | `/errors/cross-tenant` | Cross-tenant access attempt |
| 403 | `/errors/insufficient-role` | Role doesn't permit action |
| 404 | `/errors/resource-not-found` | Resource doesn't exist |
| 423 | `/errors/tenant-suspended` | Tenant account suspended |
| 429 | `/errors/rate-limit` | Too many requests |
| 500 | `/errors/internal-server` | Unexpected server error |

### Client Error Handling
```javascript
// Frontend error interceptor
api.interceptors.response.use(
  response => response,
  error => {
    if (error.response?.status === 423) {
      // Show billing/renewal banner
    } else if (error.response?.status === 401) {
      // Redirect to login
    }
    // ...
  }
);
```

## Application Flow

### Authentication Flow  
1. **Login Request**: User submits email/password to `/tenants/{tid}/auth/login`
2. **Credential Validation**: Backend verifies Argon2 hashed password
3. **Token Generation**: JWT access token (15min) + refresh token (7d) created
4. **Token Storage**: Frontend stores tokens in localStorage
5. **API Requests**: Access token sent in Authorization header
6. **Token Refresh**: Auto-refresh when access token expires
7. **Logout**: Refresh token invalidated on backend

### JIRA Integration Flow
1. **Credential Setup**: Admin provides JIRA URL, email, API token
2. **Validation**: Backend tests credentials via JIRA `/myself` endpoint  
3. **Encryption**: API token encrypted with AES-256-GCM
4. **Storage**: Encrypted credentials saved to database
5. **API Calls**: Backend proxies requests to JIRA with decrypted token
6. **Caching**: Project/issue data cached for performance

## Functional Areas System

The application supports functional areas - logical groupings that can contain multiple JIRA projects and repositories. This addresses real-world scenarios where a single functional area (like "E-commerce Platform" or "User Authentication System") spans across multiple projects and repositories.

### Key Features

#### Multi-Project & Multi-Repository Support
- **N:N Relationships**: One functional area can contain multiple JIRA projects and repositories
- **Cross-Functional**: A backend project and frontend project can be grouped in the same functional area
- **Flexible Roles**: Each project/repository can have a defined role within the functional area

#### Common Use Cases
- **E-commerce Platform**: Contains backend API project, frontend web project, mobile app projects, and corresponding repositories
- **Authentication System**: Groups authentication API, user management UI, mobile SDK projects
- **Payment Processing**: Links payment gateway project, fraud detection service, reporting dashboard

### Functional Area Structure

#### Core Properties
- **Name & Description**: Clear identification and purpose
- **Category**: Optional categorization (Frontend, Backend, Full-Stack, Infrastructure, etc.)
- **Priority**: Business priority level (high, medium, low)
- **Metadata**: Extensible JSON metadata for custom properties

#### Project Links
- **Project Role**: Defines the project's role within the area (backend, frontend, mobile-app, api, etc.)
- **Primary Project**: Mark the main project for the functional area
- **Flexible Grouping**: Multiple projects of different types can coexist

#### Repository Links
- **Repository Role**: Defines the repository's role (backend-api, frontend-web, mobile-ios, shared-lib, etc.)
- **Primary Repository**: Mark the main repository for the functional area
- **Multi-Stack Support**: Repositories from different technology stacks can be grouped

### Benefits
1. **Improved Organization**: Logical grouping of related components
2. **Cross-Team Collaboration**: Clear visibility of all components in a functional area
3. **Holistic Bug Analysis**: AI agents can analyze bugs across the entire functional area
4. **Better Project Management**: Track issues and development across multiple projects/repos
5. **Simplified Navigation**: Users can quickly find all components related to a business function

## AI Agent System

The application includes an AI-powered bug analysis and automated fix generation system.

### Agent Components

#### Bug Analyzer Agent (`agents/bug_analyzer.py`)
- Analyzes JIRA bug reports using AI
- Extracts key information and context
- Generates structured analysis output
- Stores results in `agents/outputs/{issue_key}/`

#### Developer Agent (`agents/developer_agent.py`)
- Generates automated bug fixes
- Creates pull requests with proposed solutions
- Integrates with repository management system
- Supports multiple Git providers (GitHub, Bitbucket, GitLab)

### Agent Workflow
1. **Bug Analysis**: User triggers analysis for a specific JIRA issue
2. **Context Gathering**: Agent fetches issue details, attachments, comments
3. **AI Processing**: LLM analyzes the bug and generates insights
4. **Solution Generation**: Agent creates code fixes and implementation plan
5. **PR Creation**: Automated pull request with fix is created in linked repository

### Agent Outputs
Agent analysis results are stored in structured format:
- `bug_details.json`: Parsed issue information
- `bug_fix_plan.txt`: Detailed fix implementation plan
- `attachments/`: Copied issue attachments for context

## Development Guidelines

### Core Principles
1. **Tenant Isolation First**: Every database query MUST filter by `tenant_id`
2. **Fail Secure**: Deny by default; explicit grants only
3. **Validate Everything**: Input validation on frontend AND backend
4. **Encrypt Sensitive Data**: Never store plaintext credentials
5. **Audit Everything**: Log all sensitive operations with tenant context
6. **AI Safety**: Validate AI-generated code before execution

### Code Standards
- **Python (Backend)**:
  - Use type hints for all functions
  - Follow PEP 8 style guide
  - Add docstrings to all modules/classes/functions
  - Use `@jwt_required()` decorator for protected endpoints
  
- **JavaScript (Frontend)**:
  - Use functional components with hooks
  - PropTypes or TypeScript for type safety
  - Follow Airbnb JavaScript style guide
  - Handle loading/error states for all API calls

### Security Checklist
- [ ] All endpoints check `tenant_id` from JWT
- [ ] Database queries filtered by `tenant_id`
- [ ] Sensitive data encrypted at rest
- [ ] HTTPS enforced in production
- [ ] Rate limiting implemented
- [ ] Input validation on all endpoints
- [ ] XSS protection headers configured
- [ ] CORS restricted to frontend origin

### Performance Targets
- Project list: < 1s p99 latency
- Issue list: < 1.5s p99 latency  
- API response size: < 1MB
- Cache hit ratio: > 80%
- Database connection pool: 20 connections

### Testing Requirements
- Unit test coverage: > 80%
- Integration tests for all API endpoints
- Tenant isolation tests mandatory
- Performance tests for critical paths
- Security tests for authentication flow

## Testing Infrastructure

### Backend Testing (pytest)

#### Test Organization
```
backend/tests/
├── conftest.py          # Fixtures & test database setup
├── test_app.py          # Core app functionality
├── test_auth.py         # Authentication flows
├── test_public_tenants.py # Tenant resolution
├── test_projects.py     # Project endpoints
├── test_issues.py       # Issue endpoints  
├── test_jira.py         # JIRA integration
├── test_tenants.py      # Admin endpoints
├── test_cors.py         # CORS policies
├── test_uuid_fix.py     # UUID handling
├── test_uuid_handling.py # UUID conversions
├── test_encryption.py   # Crypto functions
└── test_utils.py        # Utility functions
```

#### Key Test Fixtures
```python
@pytest.fixture
def test_client():
    """Flask test client with in-memory SQLite"""
    
@pytest.fixture
def auth_headers(test_client):
    """JWT authentication headers"""
    
@pytest.fixture
def test_tenant():
    """Sample tenant with users"""
```

#### Running Tests
```bash
# Using test runner script (recommended)
./runtests.py
./runtests.py -xvs tests/test_auth.py
./runtests.py --cov=. --cov-report=html

# Direct pytest
export APPLICATION_AES_KEY="00112233445566778899aabbccddeeff00112233445566778899aabbccddeeff"
export TESTING=True
python -m pytest
```

#### Test Environment
- `.env.test`: Environment variables for testing
- `TESTING=True`: Bypasses Argon2 in tests
- In-memory SQLite: Fast, isolated test runs

### Frontend Testing (Jest + React Testing Library)

#### Test Structure  
```
frontend/src/__tests__/
├── App.test.js
├── DashboardPage.test.js
├── LoginPage.test.js
├── MainLayout.test.js
├── api.test.js
├── theme.test.js
├── setupTests.js
├── accessibility/
│   └── AccessibilityTests.test.js
├── integration/
│   └── AuthFlow.test.js
└── mocks/
    ├── handlers.js
    ├── server.js
    └── handlers.test.js
```

#### Test Categories
1. **Unit Tests**: Individual components
2. **Integration Tests**: Full user flows  
3. **Accessibility Tests**: WCAG compliance
4. **API Tests**: Mock service worker

#### Running Frontend Tests
```bash
cd frontend
npm test                 # Interactive mode
npm test -- --coverage   # With coverage
npm test -- --ci        # CI mode
```

### Test Coverage Requirements

| Component | Target | Current |
|-----------|--------|--------|
| Backend API | >80% | 85% |
| Frontend Components | >70% | 75% |
| Integration Tests | All critical paths | ✓ |
| Security Tests | All auth flows | ✓ |

### Critical Test Scenarios

#### Tenant Isolation
```python
def test_cross_tenant_access_denied():
    """Verify tenant A cannot access tenant B data"""
    # Create two tenants
    # Authenticate as tenant A
    # Try to access tenant B resource
    # Assert 403 Forbidden
```

#### Authentication Flow
```javascript
test('complete auth flow', async () => {
  // Login
  // Verify tokens stored
  // Make authenticated request
  // Refresh token
  // Logout
});
```

#### JIRA Integration
```python
def test_jira_credential_encryption():
    """Verify API keys are encrypted properly"""
    # Add JIRA credentials
    # Verify encrypted in database
    # Retrieve and decrypt
    # Make JIRA API call
```

## JIRA Integration

### Overview
The JIRA Bug Browser provides secure, seamless integration with Atlassian JIRA Cloud instances. Each tenant configures their own JIRA connection independently.

### Authentication Requirements
For Atlassian Cloud JIRA, three components are required:
1. **Base URL**: JIRA instance URL (e.g., `https://company.atlassian.net`)
2. **Email Address**: Atlassian account email for API authentication
3. **API Token**: Generated from Atlassian account settings (not password)

### Credential Management

#### Security Features
- **Encryption**: API tokens encrypted with AES-256-GCM before storage
- **Key Management**: 256-bit encryption keys stored in environment
- **Tenant Isolation**: Each tenant's credentials completely separated
- **Role-Based Access**: Only tenant admins can modify credentials

#### Storage Schema
```sql
jira_credentials {
  id: Integer (PK)
  tenant_id: UUID (FK)
  user_id: UUID (FK, nullable)
  base_url: String
  email: String  
  api_key_encrypted: String(512)
  updated_at: DateTime
}
```

### API Proxy Pattern
The backend acts as a secure proxy for JIRA API calls:
1. Frontend requests data from backend endpoints
2. Backend decrypts stored JIRA credentials
3. Backend makes authenticated request to JIRA
4. Response cached and returned to frontend
5. Sensitive credentials never exposed to client

### Supported JIRA Operations

#### Current (Read-Only)
- List projects: `GET /rest/api/3/project`
- Search issues: `GET /rest/api/3/search?jql=...`
- Get issue details: `GET /rest/api/3/issue/{issueKey}`
- Validate credentials: `GET /rest/api/3/myself`

#### Future (Write Operations)
- Create issues
- Update issue fields
- Add comments
- Upload attachments

### Caching Strategy
- **Project List**: Cached for 30 seconds
- **Issue Search**: Cached per query for 30 seconds  
- **Issue Details**: Cached per issue for 60 seconds
- **Cache Keys**: Prefixed with `tenant_id` for isolation

### Error Handling
| JIRA Error | Application Response |
|------------|---------------------|
| 401 Unauthorized | Prompt to update credentials |
| 403 Forbidden | Show permission error |
| 404 Not Found | Handle gracefully in UI |
| 429 Rate Limited | Retry with backoff |
| 5xx Server Error | Show temporary error |

### Configuration UI
The JIRA Settings page (`/jira-settings`) provides:
- Current configuration status
- Add/update credentials form
- Test connection button
- Delete credentials option
- Last sync timestamp

## Repository Management System

The application includes a comprehensive repository management system that allows linking Git repositories to JIRA projects. This enables code-to-issue tracking and local development path management.

### Key Features
- **Multi-Repository Support**: N:N relationship between JIRA projects and repositories
- **Multiple Git Providers**: Support for Bitbucket, GitHub, and GitLab
- **Repository Discovery**: Automatic discovery from configured sources
- **Local Path Mapping**: User-specific local filesystem paths
- **Visual Status Indicators**: Clear UI showing repository linkage status
- **Caching Strategy**: 1-hour cache for external API calls

### Architecture Overview
The system uses four main database models:
1. **RepositorySource**: Git provider configurations (Bitbucket workspaces, GitHub orgs)
2. **Repository**: Individual repositories discovered from sources
3. **ProjectRepository**: Links between JIRA projects and repositories
4. **UserRepositoryPath**: User-specific local paths for repositories

### UI Components
- **Projects Page**: Shows repository count chips with visual status indicators
- **Repository Browser Dialog**: Interface for linking repositories to projects
- **Repository Source Management**: Admin page for configuring Git providers
- **Local Path Dialog**: User interface for setting filesystem paths

### Recent Improvements
1. Added visual repository status indicators on projects page
2. Implemented menu-based navigation for better UX
3. Made repository chips clickable for quick access
4. Added caching for slow external API calls
5. Improved error handling and credential encryption

For detailed documentation, see `REPOSITORY_MANAGEMENT.md`.