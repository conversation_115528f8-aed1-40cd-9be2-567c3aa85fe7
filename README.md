# JIRA Bug Browser Web Application

This project is a multi-tenant SaaS web application that lets an authenticated user browse JIRA projects and drill into individual Bug issues.

## Project Structure

- `backend/`: Contains the Flask (Python) backend application.
- `frontend/`: Contains the React (JavaScript) frontend application.
- `requirements/`: Contains detailed requirements documents.

## Backend (Flask)

- Main application logic is in `backend/app.py`.
- API endpoints are organized into blueprints in `backend/blueprints/`.
- Database models are defined in `backend/models.py`.
- Configuration is in `backend/config.py`.

### Backend Setup

1. Navigate to the `backend` directory: `cd backend`
2. Create a virtual environment: `python -m venv venv`
3. Activate the virtual environment: `source venv/bin/activate` (on macOS/Linux) or `.\venv\Scripts\activate` (on Windows)
4. Install dependencies: `pip install -r requirements.txt`
5. Initialize the database: `flask db init` (if first time), `flask db migrate -m "Initial migration"`, `flask db upgrade`
6. Run the development server: `cd .. && ./run_backend.sh` or directly: `flask run --port=5045`

## Frontend (React)

- Main React application is in `frontend/src/`.
- Views are in `frontend/src/views/`.
- Layouts are in `frontend/src/layouts/`.
- API services are in `frontend/src/services/`.

### Frontend Setup

1. Navigate to the `frontend` directory: `cd frontend`
2. Install dependencies: `npm install`
3. Run the development server: `npm start` (runs on port 5045)

### Frontend Testing

The frontend includes a comprehensive test suite with unit, integration, and accessibility tests:

1. Run tests: `npm test`
2. Run tests with coverage: `npm run test:coverage`

See the [Frontend Testing Guide](frontend/TESTING.md) for detailed information about the test suite.

## Key Features

- **Multi-tenant Architecture**: Every customer (tenant) supplies their own JIRA base URL and API keys
- **Tenant Isolation**: Data is siloed logically with row-level security
- **Authentication**: JWT-based authentication with tenant context
- **Responsive UI**: Works on desktop, tablet, and mobile devices

## Architecture

The application follows a multi-tenant architecture pattern:
- Frontend: React SPA with Material-UI components
- Backend: Flask API with tenant-scoped endpoints
- Database: SQLite/PostgreSQL with tenant isolation

## Documentation

- See `requirements/application_requirements.md` for detailed design specifications
- See `requirements/ui_requirement.md` for UI design guidelines
- See `CLAUDE.md` for Claude Code assistance

---

Based on design document: *Version 1.1 · May 14 2025*