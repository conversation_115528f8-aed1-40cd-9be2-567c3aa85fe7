# Repository Management System Documentation

## Overview

This document describes the repository management system implemented in the JIRA Bug Browser application. The system allows users to link Git repositories (from Bitbucket, GitHub, GitLab) to JIRA projects, enabling code-to-issue tracking and local development path management.

## Architecture

### Database Schema

The repository management system uses the following database models:

#### RepositorySource
- Stores configuration for repository sources (Bitbucket workspaces, GitHub organizations, GitLab groups)
- Fields:
  - `id`: UUID primary key
  - `tenant_id`: Foreign key to tenant
  - `source_type`: ENUM ('bitbucket', 'github', 'gitlab')
  - `name`: Display name for the source
  - `config`: JSON field with source-specific configuration
  - `credentials_encrypted`: Encrypted credentials (username/password or access tokens)
  - `is_active`: Boolean flag
  - `last_sync_at`: Timestamp of last repository sync
  - `created_at`, `updated_at`: Timestamps

#### Repository
- Represents individual repositories discovered from sources
- Fields:
  - `id`: UUID primary key
  - `tenant_id`: Foreign key to tenant
  - `source_id`: Foreign key to RepositorySource
  - `name`: Repository name (e.g., 'my-repo')
  - `display_name`: Human-friendly name
  - `repository_type`: Type of repository
  - `remote_url`: Git clone URL
  - `default_branch`: Default branch name
  - `description`: Repository description
  - `is_active`: Boolean flag
  - `last_sync_at`: Timestamp of last sync
  - `metadata`: JSON field for additional data

#### ProjectRepository
- N:N relationship between JIRA projects and repositories
- Fields:
  - `id`: UUID primary key
  - `tenant_id`: Foreign key to tenant
  - `project_key`: JIRA project key
  - `repository_id`: Foreign key to Repository
  - `is_primary`: Boolean flag for primary repository
  - `linked_by`: User who created the link
  - `linked_at`: Timestamp of link creation

#### UserRepositoryPath
- Stores user-specific local paths for repositories
- Fields:
  - `id`: UUID primary key
  - `tenant_id`: Foreign key to tenant
  - `user_id`: Foreign key to user
  - `project_repository_id`: Foreign key to ProjectRepository
  - `local_path`: Local filesystem path
  - `created_at`, `updated_at`: Timestamps

### API Endpoints

#### Repository Source Management
- `GET /tenants/{tid}/repository-sources` - List all sources
- `POST /tenants/{tid}/repository-sources` - Create a new source
- `GET /tenants/{tid}/repository-sources/{sid}` - Get source details
- `PUT /tenants/{tid}/repository-sources/{sid}` - Update source
- `DELETE /tenants/{tid}/repository-sources/{sid}` - Delete source
- `POST /tenants/{tid}/repository-sources/{sid}/sync` - Sync repositories from source

#### Repository Management
- `GET /tenants/{tid}/repositories` - List all repositories
- `POST /tenants/{tid}/repositories` - Create manual repository
- `PUT /tenants/{tid}/repositories/{rid}` - Update repository
- `DELETE /tenants/{tid}/repositories/{rid}` - Delete repository (soft or hard)
  - Query parameter `?force=true` for permanent deletion
- `GET /tenants/{tid}/projects/{pkey}/repositories` - Get repositories for a project
- `POST /tenants/{tid}/projects/{pkey}/repositories` - Link repository to project
- `DELETE /tenants/{tid}/projects/{pkey}/repositories/{rid}` - Unlink repository

#### User Path Management
- `POST /tenants/{tid}/repositories/{rid}/local-path` - Set user's local path
- `GET /tenants/{tid}/repositories/{rid}/local-path` - Get user's local path

### Services

#### RepositorySourceService
- Handles CRUD operations for repository sources
- Implements repository discovery from external APIs
- Manages credential encryption/decryption
- Implements caching strategy (1-hour cache for repository listings)

#### RepositoryManagementService
- Manages repository CRUD operations
- Handles project-repository relationships
- Manages user path mappings
- Implements soft and hard delete functionality
  - Soft delete: Marks repository as inactive, preserves data
  - Hard delete: Permanently removes repository and all relationships

## Frontend Components

### ProjectsPage
- Displays JIRA projects with repository status indicators
- Shows repository count chips for each project
- Provides menu access to repository management and local path configuration
- Clickable repository chips for quick access to repository management

### RepositorySourceManagementPage
- Admin interface for managing repository sources
- Form for adding new sources (Bitbucket, GitHub, GitLab)
- Table view of existing sources with sync status
- Sync button with caching information

### RepositoryBrowserDialog
- Modal dialog for linking repositories to projects
- Search and filter capabilities
- Repository selection with primary repository designation
- Checkbox interface for multi-selection

### UserRepositoryPathDialog
- Modal for setting local filesystem paths for repositories
- Path validation and saving

## Implementation Details

### Security
- Credentials are encrypted using AES-256-GCM encryption
- Tenant isolation is enforced at all levels
- Admin-only access for repository source management
- User-specific local paths are isolated by user ID

### Caching Strategy
- Repository listings from external APIs are cached for 1 hour
- Cache can be bypassed using the `force` parameter
- Cache metadata (last sync time) is displayed in the UI

### External API Integration

#### Bitbucket
- Uses Bitbucket Cloud API v2.0
- Requires workspace ID and app password
- Endpoint: `https://api.bitbucket.org/2.0/repositories/{workspace}`
- Pagination support with 100 items per page

#### GitHub
- Uses GitHub REST API v3
- Requires organization name and personal access token
- Endpoint: `https://api.github.com/orgs/{org}/repos`

#### GitLab
- Uses GitLab REST API v4
- Requires group ID and access token
- Endpoint: `https://gitlab.com/api/v4/groups/{group_id}/projects`

## User Workflows

### Adding a Repository Source (Admin)
1. Navigate to Repository Source Management page
2. Click "Add New Source"
3. Select source type (Bitbucket/GitHub/GitLab)
4. Enter credentials and configuration
5. Click "Create" to save the source
6. Click "Sync" to discover repositories

### Linking Repositories to Projects
1. From Projects page, hover over a project
2. Click the settings icon or repository chip
3. Select "Manage Repositories" from menu
4. In the dialog, select repositories to link
5. Optionally mark one as primary
6. Click "Link Selected Repositories"

### Setting Local Paths
1. From Projects page, hover over a project
2. Click the settings icon
3. Select "Set Local Path" from menu
4. Enter the local filesystem path
5. Click "Save"

### Deleting Repositories (Admin)
1. Navigate to Repository Management page
2. Find the repository to delete
3. Click the delete icon
4. Choose delete type:
   - Soft Delete: Repository marked as inactive, can be restored
   - Hard Delete: Permanently removes repository and all data
5. Confirm the deletion
6. For soft-deleted repos:
   - Repository appears grayed out in list
   - Can be reactivated by updating its status

## Recent Improvements

### Visual Repository Status
- Added repository count chips to projects page
- Color-coded indicators (green for linked, yellow warning for no repos)
- Made chips clickable for quick access

### Menu-Based Navigation
- Replaced direct dialog opening with dropdown menu
- Separated repository management from local path configuration
- Improved discoverability of features

### Performance Optimizations
- Implemented caching for external API calls
- Added force refresh capability
- Display cache age in UI

## Known Issues and Limitations

1. Bitbucket API calls can be slow (>1 minute for 50 repos)
2. Repository sync is currently manual (no automatic periodic sync)
3. Local paths are not validated against actual filesystem
4. No support for private/self-hosted Git servers

## Future Enhancements

1. Automatic periodic repository sync
2. Webhook support for real-time updates
3. Repository access validation
4. Support for additional Git providers
5. Bulk operations for repository management
6. Repository metadata caching
7. Integration with CI/CD pipelines

## Configuration

### Environment Variables
- `APPLICATION_AES_KEY`: 32-byte key for credential encryption
- `BITBUCKET_API_URL`: Override default Bitbucket API URL
- `GITHUB_API_URL`: Override default GitHub API URL
- `GITLAB_API_URL`: Override default GitLab API URL

### Required Permissions

#### Bitbucket
- Repository read access
- App password with repository read scope

#### GitHub
- Organization repository read access
- Personal access token with `repo` scope

#### GitLab
- Group/project read access
- Access token with `read_api` scope

## Testing

### Backend Tests
Located in `backend/tests/`:
- `test_repository_source_service.py`
- `test_repository_management.py`

Run with: `python -m pytest backend/tests/`

### Frontend Tests
- Component tests for repository UI elements
- Integration tests for API interactions

Run with: `cd frontend && npm test`

## API Examples

### Create Bitbucket Source
```json
POST /tenants/{tid}/repository-sources
{
  "name": "My Bitbucket Workspace",
  "source_type": "bitbucket",
  "config": {
    "workspace": "my-workspace"
  },
  "credentials": {
    "username": "<EMAIL>",
    "password": "app-password-here"
  }
}
```

### Link Repository to Project
```json
POST /tenants/{tid}/projects/PROJ-1/repositories
{
  "repository_id": "uuid-here",
  "is_primary": true
}
```

### Set User Local Path
```json
POST /tenants/{tid}/repositories/{rid}/local-path
{
  "local_path": "/Users/<USER>/projects/my-repo"
}
```