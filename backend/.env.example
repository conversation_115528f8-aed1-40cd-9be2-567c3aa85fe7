# Example environment variables
# Copy this file to .env for development or .env.test for testing

# Required for encryption of sensitive data (32-byte hex encoded, 64 hex characters)
# IMPORTANT: Generate a secure random key for production environments!
# Example command to generate a key: python -c "import os, binascii; print(binascii.hexlify(os.urandom(32)).decode())"
APPLICATION_AES_KEY=00112233445566778899aabbccddeeff00112233445566778899aabbccddeeff

# Flask configuration
FLASK_APP=app.py
FLASK_ENV=development  # Use 'production' in production environments

# Logging configuration
LOG_LEVEL=DEBUG  # Possible values: DEBUG, INFO, WARNING, ERROR, CRITICAL
SQLALCHEMY_ECHO=True  # Set to False in production

# Database configuration (uncomment and set for PostgreSQL)
# DATABASE_URL=postgresql://username:password@localhost:5432/jira_browser

# JWT configuration (set a strong secret key in production)
# JWT_SECRET_KEY=your-secret-key-here

# For testing
# TESTING=True