FLASK_APP=app.py
FLASK_ENV=development
# Example DATABASE_URL, replace with your actual connection string
# DATABASE_URL="postgresql://user:password@localhost:5432/jira_browser_dev"
# To use SQLite, either remove/comment the DATABASE_URL above (config.py will use a default SQLite path)
# or set it explicitly e.g.:
# DATABASE_URL="sqlite:///instance/jira_browser_dev.db"

# Generate strong random strings for these in a real .env file
SECRET_KEY="your_flask_secret_key_here_make_it_random_and_strong"
JWT_SECRET_KEY="your_jwt_secret_key_here_make_it_random_and_strong"
APPLICATION_AES_KEY="your_32_byte_hex_encoded_aes_key_here" # Added in previous step 