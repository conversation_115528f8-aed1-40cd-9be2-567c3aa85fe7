# JIRA Bug Browser - Backend

This is the Flask backend for the JIRA Bug Browser application.

## Overview

The JIRA Bug Browser is a multi-tenant SaaS application that allows organizations to browse and manage JIRA issues through a simplified interface. This backend provides the API for the frontend application.

The application follows a secure multi-tenant architecture with complete tenant isolation, ensuring that each organization's data remains private and separate from others. It uses JWT-based authentication with tenant context to maintain security boundaries.

## Features

- Multi-tenant architecture with complete tenant isolation
- JWT-based authentication with role-based access control
- JIRA integration for fetching projects and issues with caching
- RESTful API design following best practices
- PostgreSQL database support with SQLite fallback for development
- Secure credential storage with AES-256-GCM encryption
- Cross-Origin Resource Sharing (CORS) support for frontend integration
- Comprehensive test suite with pytest
- RFC 7807 compliant error responses

## API Endpoints

### Public Endpoints (No Authentication Required)

- `GET /` - Root endpoint with API information
- `GET /health` - Health check endpoint
- `GET /api/public/tenants/resolve?name={name}` - Resolve tenant by name

### Authentication Endpoints

- `POST /tenants/{tid}/auth/login` - Log in with email and password
- `POST /tenants/{tid}/auth/refresh` - Refresh access token
- `POST /tenants/{tid}/auth/logout` - Log out and invalidate refresh token

### Project and Issue Endpoints (Authentication Required)

- `GET /tenants/{tid}/projects` - List all projects
- `GET /tenants/{tid}/projects/{pkey}/issues` - List issues for a project
- `GET /tenants/{tid}/issues/{ikey}` - Get specific issue details

### JIRA Integration Endpoints (Authentication Required)

- `GET /tenants/{tid}/jira/credentials` - Get JIRA credentials status
- `POST /tenants/{tid}/jira/credentials` - Add/update JIRA credentials
- `DELETE /tenants/{tid}/jira/credentials` - Delete JIRA credentials

### Admin Endpoints (Admin Role Required)

- `GET /admin/tenants` - List all tenants
- `GET /admin/tenants/{tid}` - Get tenant details
- `POST /admin/tenants` - Create a new tenant

## Getting Started

### Prerequisites

- Python 3.8+
- pip
- PostgreSQL (optional, SQLite is used by default)

### Installation

1. Clone the repository and navigate to the backend directory:
```bash
cd backend
```

2. Create a virtual environment:
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. Install dependencies:
```bash
pip install -r requirements.txt
```

4. Set up environment variables (for development):
```bash
export FLASK_APP=app.py
export FLASK_ENV=development
export APPLICATION_AES_KEY="00112233445566778899aabbccddeeff00112233445566778899aabbccddeeff"
# Uncomment and set these for PostgreSQL
# export DATABASE_URL="postgresql://username:password@localhost:5432/jira_browser"
# export JWT_SECRET_KEY="your-secret-key"
```

5. Initialize the database:
```bash
flask db upgrade
```

6. (Optional) Seed the database with test data:
```bash
python seed.py
```

7. Run the application:
```bash
flask run --port=5045
```

## Testing

The backend includes a comprehensive test suite using pytest. Tests cover authentication, API endpoints, CORS, and security.

### Running Tests

We've simplified test execution with a test runner script that automatically loads environment variables from `.env.test`:

1. Run all tests using the test runner:
```bash
./runtests.py
```

2. Run specific test files:
```bash
./runtests.py tests/test_auth.py
```

3. Run with specific pytest options:
```bash
./runtests.py -xvs tests/test_uuid_fix.py
```

4. Run tests with coverage report:
```bash
./runtests.py --cov=. --cov-report=term-missing
```

### Manual Test Execution

If you prefer to run pytest directly:

1. Make sure environment variables are set (either export them or use dotenv):
```bash
# Either use the .env.test file (recommended)
python -c "from dotenv import load_dotenv; load_dotenv('.env.test')"

# Or export directly
export APPLICATION_AES_KEY="00112233445566778899aabbccddeeff00112233445566778899aabbccddeeff"
export TESTING=True  # Enables test mode for password verification
```

2. Then run pytest:
```bash
python -m pytest
```

### Test Organization

- `tests/conftest.py`: Test fixtures and setup
- `tests/test_app.py`: Core application tests
- `tests/test_auth.py`: Authentication tests
- `tests/test_uuid_fix.py`: UUID conversion and handling tests
- `tests/test_public_tenants.py`: Public tenant resolution tests
- `tests/test_projects.py`: Project endpoint tests
- `tests/test_issues.py`: Issue endpoint tests
- `tests/test_jira.py`: JIRA integration tests
- `tests/test_tenants.py`: Tenant administration tests
- `tests/test_cors.py`: CORS configuration tests
- `tests/test_basic.py`: Basic app functionality tests

### Testing Notes

#### Password Verification

For testing purposes, we've implemented a mechanism to bypass Argon2 password verification:

- When the `TESTING=True` environment variable is set, the system accepts 'password123' and 'admin123' as valid passwords regardless of the stored hash.
- This eliminates the need for complex mocking of the Argon2 library during tests.
- The test environment automatically sets this up via `.env.test`.

#### UUID Testing

The UUID tests ensure proper handling of UUID conversions and error cases:

- Tests for converting string UUIDs to UUID objects
- Tests for handling invalid UUID formats gracefully
- Tests for proper HTTP status codes (400 vs. 500) in error conditions

## Recent Fixes and Improvements

### Core Functionality

- **Root Endpoint**: Added a root endpoint to the Flask app that returns API information
- **Fixed URL Path Handling**: Fixed URL path handling for projects endpoint with proper route registration
- **Improved Authentication**: Implemented proper authentication enforcement on all protected endpoints
- **Error Handling**: Enhanced error responses with RFC 7807 Problem Details format

### CORS Support

- **Cross-Origin Configuration**: Added proper CORS headers to allow frontend-backend communication
- **Origin Restrictions**: Limited CORS access to frontend origins only (localhost:3000)
- **Method Support**: Added support for all necessary HTTP methods (GET, POST, PUT, DELETE, OPTIONS)
- **Custom Headers**: Configured allowed headers including Authorization for JWT tokens

### Testing

- **Comprehensive Test Suite**: Added extensive pytest test suite covering all aspects of the application
- **Authentication Tests**: Tests for login, logout, and token refresh functionality
- **API Endpoint Tests**: Tests for all API endpoints with both authenticated and unauthenticated requests
- **CORS Tests**: Verification of CORS headers and preflight request handling
- **Test Fixtures**: Reusable test fixtures for authentication and database setup
- **Test Password Verification**: Simplified password testing with environment-controlled verification bypassing
- **UUID Testing**: Dedicated test suite for UUID conversions and error handling 
- **Test Environment Setup**: Improved test environment with automatic configuration via .env.test
- **Test Runner Script**: Enhanced runtests.py script for easy test execution with proper environment variables

### UUID Handling

- **Database Interactions**: Fixed UUID handling in database interactions with proper type checking
- **API Parameters**: Improved handling of UUID values in API parameters
- **Test Data**: Enhanced test data generation with proper UUID handling
- **UUID Conversion**: Added robust UUID string-to-object conversion with proper error handling
- **Tenant Context**: Fixed UUID processing in tenant context for authentication and authorization
- **Error Classification**: Improved error responses for invalid UUIDs (400 Bad Request vs 500 Server Error)
- **Type Safety**: Added protection against passing UUID objects to UUID constructors with type checking

### Documentation

- **API Documentation**: Updated API documentation with endpoint details
- **Installation Guide**: Enhanced installation and configuration instructions
- **Test Documentation**: Added testing documentation with examples

## JIRA Integration

The JIRA Bug Browser includes a secure JIRA integration that allows users to connect their JIRA accounts and view their projects and issues directly within the application.

### Key Features

- **Secure Credential Storage**: JIRA API keys are encrypted using AES-256-GCM before storage, ensuring sensitive credentials are never stored in plaintext.
- **Atlassian Cloud Authentication**: Uses HTTP Basic Authentication with email and API token for secure JIRA API access.
- **Tenant-Specific Credentials**: Each tenant can configure their own JIRA credentials, ensuring complete isolation between organizations.
- **Validation**: Credentials are validated against the JIRA API before being stored to ensure they work properly.
- **Role-Based Access**: Only administrators or users within the same tenant can manage JIRA credentials.

### Authentication Requirements

For Atlassian Cloud JIRA instances, the following credentials are required:

1. **Base URL**: The URL of your JIRA instance (e.g., `https://your-domain.atlassian.net`)
2. **Email Address**: The email associated with your Atlassian account
3. **API Token**: An API token generated from your Atlassian account settings

The API token is encrypted before storage, and the email is stored alongside it to properly authenticate with the JIRA API.

### User Interface

The application provides a dedicated JIRA Settings page where users can:

1. View their current JIRA configuration
2. Add new JIRA credentials (base URL, email, and API token)
3. Update existing credentials
4. Delete credentials

### Security Considerations

- JIRA API keys are encrypted using AES-256-GCM encryption with a secure 256-bit key
- The encryption key is stored in an environment variable, not in the code
- Credentials can only be accessed by authorized users within the same tenant
- API responses never include the actual API key, only configuration status

### Encryption Key Setup

For AES-256-GCM encryption to work properly, you must set up the encryption key:

1. Generate a secure random key using the provided utility:
   ```bash
   python tools/generate_aes_key.py
   ```

2. Add the generated key to your `.env` file:
   ```
   APPLICATION_AES_KEY=your_generated_hex_key
   ```

3. For development and testing, a default key is provided in `.env.example` and `.env.test`,
   but you should **NEVER** use these keys in production.

The encryption key must be:
- 32 bytes (256 bits) represented as a 64-character hexadecimal string
- Kept secure and never committed to version control
- Different across environments (dev, test, production)

### Encryption Troubleshooting

If you encounter issues with encryption, we provide several tools to help diagnose and fix problems:

1. **Check AES Key Format**: Validates your current AES key configuration
   ```bash
   python tools/check_aes_key.py
   ```

2. **Environment Configuration Check**: Comprehensive validation of all environment variables
   ```bash
   python tools/environment_check.py
   ```

3. **Detailed Documentation**: For more information on the encryption system, see:
   ```bash
   cat tools/ENCRYPTION.md
   ```

Common errors like `non-hexadecimal number found in fromhex() arg at position 0` typically indicate an issue with the AES key format. Use the tools above to identify and fix the problem.

### API Endpoints

- `GET /tenants/{tid}/jira/credentials` - Get status of JIRA credentials (configured or not)
- `POST /tenants/{tid}/jira/credentials` - Add or update JIRA credentials (requires base_url, email, and api_key)
- `DELETE /tenants/{tid}/jira/credentials` - Delete JIRA credentials

### Request Examples

```json
// POST /tenants/{tid}/jira/credentials
{
  "base_url": "https://your-domain.atlassian.net",
  "email": "<EMAIL>",
  "api_key": "your-api-token"
}
```

## Logging and Debugging

The application includes comprehensive logging to help with debugging and troubleshooting. By default, the application logs at the INFO level, but this can be adjusted through environment variables.

### Log Configuration

Configure logging through environment variables:

```
# Set the log level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=DEBUG

# Enable SQL query logging (True/False)
SQLALCHEMY_ECHO=True
```

### Log Format

The standard log format includes:

```
YYYY-MM-DD HH:MM:SS [LEVEL] module:line_number - Message
```

For JIRA-related logs, messages are prefixed with `[JIRA]` for easier filtering.

### Log Files

In production, logs are written to both the console and a rotating file located at:

```
/path/to/app/logs/app.log
```

Log files rotate when they reach 10MB, with up to 5 backup files kept.

### Debugging JIRA Credentials

When troubleshooting JIRA credential issues, enable DEBUG level logging to see detailed information about:

1. Encryption key validation and retrieval
2. API key encryption and decryption processes
3. Request processing and parameter validation
4. Database interactions
5. Error handling and detailed exception information

## Future Improvements

- Integrate with additional issue tracking systems beyond JIRA
- Implement rate limiting for API endpoints
- Add OpenAPI/Swagger documentation
- Add performance metrics collection
- Support per-user JIRA credentials within the same tenant
- Add OAuth authentication for JIRA integration