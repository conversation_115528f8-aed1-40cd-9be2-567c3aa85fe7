import subprocess
import os
import re
import json
from typing import Dict, Any, List

from google.adk.agents import Agent, LlmAgent
from google.adk.tools import FunctionTool
from google.adk.tools.agent_tool import AgentTool

from .tools import tool_registry

MODAL_NAME = "gemini-2.5-pro-preview-03-25"


def execute_claude_code(instructions: str, repo_path: str) -> str:
    """
    Executes Claude AI code assistant for a repository.

    Args:
        instructions: The instructions to be passed to Claude AI.
        repo_path: The path to the repository.
    Returns:
        The output from <PERSON> AI as a string.
    """
    p = subprocess.Popen(
        [
            "claude",
            "--verbose",
            "--allowedTools",
            "Edit,MultiEdit,Write,Batch",
            "-p",
            "--output-format",
            "stream-json",
            instructions,
        ],
        cwd=repo_path,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True,
    )
    stdout, stderr = p.communicate()

    if p.returncode != 0:
        print(f"Error running Claude: {stderr}")
        return f"Error: {stderr}"

    return stdout


def sanitize_agent_name(name):
    """
    Converts a repository name to a valid agent name.
    Agent name must be a valid identifier: start with letter/underscore and contain only 
    letters, digits, underscores.
    """
    # Replace any non-alphanumeric characters with underscores
    sanitized = re.sub(r'\W', '_', name)
    
    # Ensure it starts with a letter or underscore
    if sanitized and not (sanitized[0].isalpha() or sanitized[0] == '_'):
        sanitized = 'repo_' + sanitized
        
    # If empty or all invalid characters, use a default name
    if not sanitized:
        sanitized = 'repository_agent'
        
    return sanitized


def create_repository_agent(repository_dict, jira_credentials=None):
    """
    Create a dynamic agent for a specific repository.
    
    Args:
        repository_dict: Dictionary containing repository information with at least the following keys:
            - 'name': Repository name
            - 'local_path': Path to the local repository (can be None)
            - 'source_type': Type of source repository (optional)
            - 'description': Repository description (optional)
            - 'enabled_tools': List of enabled tool names (optional)
            - 'tool_configs': Tool-specific configurations (optional)
        jira_credentials: Optional dict with JIRA credentials (JIRA_URL, JIRA_USER, JIRA_KEY)
    """
    # Get required fields with defaults
    repo_name = repository_dict.get('name', 'unknown')
    repo_path = repository_dict.get('local_path')
    repo_type = repository_dict.get('source_type', 'git')
    repo_desc = repository_dict.get('description', 'this project')
    
    # Get a valid agent name
    agent_name = sanitize_agent_name(repo_name)
    
    # Create a tool function for this repository
    def calude_code(instructions: str) -> str:
        """
            Executes Claude AI code assistant for a repository.

            Args:
                instructions: The instructions to be passed to Claude AI.
                repo_path: The path to the repository.
            Returns:
                The output from Claude AI as a string.
        """
        return execute_claude_code(
            instructions=instructions,
            repo_path=repo_path or f"/path/to/{repo_name}"
        )
    
    # Get custom instruction and description from repository configuration
    custom_instruction = repository_dict.get('agent_instruction')
    custom_description = repository_dict.get('agent_description')
    
    # Use custom instruction if available, otherwise use default
    instruction = custom_instruction if custom_instruction else f"You are an agent specialized in handling bugs in the {repo_name} repository."
    
    # Use custom description if available, otherwise use default
    description = custom_description if custom_description else f"""
        {repo_name} repository contains code for {repo_desc}.
        The repo is located at: {repo_path or f"/path/to/{repo_name}"}
        Source type: {repo_type}
        You are provided with a claude code tool which is an AI code assistant that can help you write code.
        """
    
    # Create tools list based on enabled tools
    tools = [calude_code]  # Always include the claude code tool
    
    # Get enabled tools and tool configurations from repository
    enabled_tools = repository_dict.get('enabled_tools', [])
    tool_configs = repository_dict.get('tool_configs', {})
    
    # Always include jira_bug_analyzer for repository agents (not visible in UI)
    if 'jira_bug_analyzer' not in enabled_tools:
        enabled_tools = enabled_tools + ['jira_bug_analyzer']
    
    # Merge tool configs with JIRA defaults if JIRA tool is enabled
    merged_tool_configs = tool_configs.copy()
    
    if 'jira_bug_analyzer' in enabled_tools and jira_credentials:
        # Merge JIRA credentials with tool configs, giving precedence to user-provided values
        merged_tool_configs['jira_bug_analyzer'] = {
            **jira_credentials,  # Default credentials
            **merged_tool_configs.get('jira_bug_analyzer', {})  # User overrides
        }
    
    # Create tool functions using the registry
    additional_tools = tool_registry.create_tool_functions(enabled_tools, merged_tool_configs)
    tools.extend(additional_tools)
    
    # Create the agent for this repository
    return LlmAgent(
        name=agent_name,
        instruction=instruction,
        model=MODAL_NAME,
        description=description,
        tools=tools,
    )


def create_dynamic_developer_agent(repositories, jira_credentials=None):
    """
    Create a developer agent with dynamic child agents based on selected repositories.
    
    Args:
        repositories: List of repository dictionaries or repository objects.
                     If objects are provided, they will be converted to dictionaries.
        jira_credentials: Optional dict with JIRA credentials (JIRA_URL, JIRA_USER, JIRA_KEY)
    """
    # Convert repositories to dictionaries if they're not already
    repo_dicts = []
    for repo in repositories:
        if isinstance(repo, dict):
            repo_dicts.append(repo)
        else:
            # Convert repository object to dictionary
            repo_dict = {
                'id': getattr(repo, 'id', None),
                'name': getattr(repo, 'name', 'unknown'),
                'full_name': getattr(repo, 'full_name', None) or getattr(repo, 'name', 'unknown'),
                'description': getattr(repo, 'description', None) or 'this project',
                'source_type': getattr(repo, 'source_type', 'git'),
                'local_path': getattr(repo, 'local_path', None),
                'agent_instruction': getattr(repo, 'agent_instruction', None),
                'agent_description': getattr(repo, 'agent_description', None),
                'enabled_tools': getattr(repo, 'enabled_tools', []),
                'tool_configs': getattr(repo, 'tool_configs', {})
            }
            repo_dicts.append(repo_dict)
    
    # Create agents for each selected repository
    child_agents = []
    print(f"Creating child agents for {len(repo_dicts)} repositories")
    
    for repo_dict in repo_dicts:
        print(f"Creating agent for repository: {repo_dict['name']}")
        try:
            child_agent = create_repository_agent(repo_dict, jira_credentials)
            print(f"Successfully created agent with name: {child_agent.name}")
            child_agents.append(child_agent)
        except Exception as e:
            print(f"Error creating agent for repository {repo_dict['name']}: {str(e)}")
            import traceback
            traceback.print_exc()
    
    # Create main agent tools
    main_tools = []
    
    # Always include JIRA bug analyzer for the main developer agent
    # Get the jira_bug_analyzer tool from the registry
    jira_tool = tool_registry.get('jira_bug_analyzer')
    if jira_tool and jira_credentials:
        print("Adding JIRA bug analyzer tool to main developer agent")
        # Create the tool function with merged credentials
        jira_config = jira_credentials.copy() if jira_credentials else {}
        jira_bug_analyzer_func = jira_tool.create_function(jira_config)
        main_tools.append(jira_bug_analyzer_func)
    else:
        print("Warning: JIRA bug analyzer tool not found or no credentials provided")

    # Create the main developer agent with dynamic child agents
    print(f"Creating main developer agent with {len(child_agents)} child agents and {len(main_tools)} tools")
    
    if not child_agents:
        print("Warning: No repository agents were created")
        # Return a minimal agent
        return LlmAgent(
            name="DeveloperAgent",
            model=MODAL_NAME,
            instruction="Analyze the provided bug.",
            description="Developer agent with no repository agents.",
            tools=main_tools,
        )
    
    developer_agent = LlmAgent(
        name="DeveloperAgent",
        model=MODAL_NAME,
        instruction="Analyze the provided bug using the tools and re-route to the appropriate agent.",
        description=f"""You are a developer agent that helps developers with their tasks.
        You are provided with multiple tools to help you analyze the bug and route it to the appropriate agent.
        Each sub agent is specialized in handling bugs specific to their repository.
        Selected repositories: {[repo['name'] for repo in repo_dicts]}
        """,
        sub_agents=child_agents,
        tools=main_tools,
    )
    
    print("Successfully created main developer agent")
    return developer_agent
