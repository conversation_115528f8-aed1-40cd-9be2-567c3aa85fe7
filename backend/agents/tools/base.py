"""Base class and registry for agent tools."""
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Callable
import inspect


class BaseTool(ABC):
    """Base class for all agent tools."""
    
    @property
    @abstractmethod
    def id(self) -> str:
        """Unique identifier for the tool."""
        pass
    
    @property
    @abstractmethod
    def name(self) -> str:
        """Human-readable name for the tool."""
        pass
    
    @property
    @abstractmethod
    def description(self) -> str:
        """Description of what the tool does."""
        pass
    
    @property
    def config_schema(self) -> Dict[str, Any]:
        """
        JSON schema for tool-specific configuration.
        Override this to define required configuration fields.
        """
        return {}
    
    @abstractmethod
    def create_function(self, config: Dict[str, Any]) -> Callable:
        """
        Create the actual function that will be used by the agent.
        
        Args:
            config: Tool-specific configuration from the database
            
        Returns:
            A callable function that can be used by the agent
        """
        pass
    
    def validate_config(self, config: Dict[str, Any]) -> bool:
        """
        Validate the configuration against the schema.
        Override for custom validation logic.
        """
        # Basic validation - check required fields
        schema = self.config_schema
        required_fields = schema.get('required', [])
        
        for field in required_fields:
            if field not in config:
                return False
        
        return True


class ToolRegistry:
    """Registry for managing available tools."""
    
    _instance = None
    _tools: Dict[str, BaseTool] = {}
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def register(self, tool: BaseTool):
        """Register a tool."""
        self._tools[tool.id] = tool
    
    def get(self, tool_id: str) -> Optional[BaseTool]:
        """Get a tool by ID."""
        return self._tools.get(tool_id)
    
    def list_all(self) -> List[Dict[str, Any]]:
        """List all available tools with their metadata."""
        return [
            {
                'id': tool.id,
                'name': tool.name,
                'description': tool.description,
                'config_schema': tool.config_schema
            }
            for tool in self._tools.values()
        ]
    
    def create_tool_functions(self, enabled_tools: List[str], tool_configs: Dict[str, Any]) -> List[Callable]:
        """
        Create tool functions based on enabled tools and their configurations.
        
        Args:
            enabled_tools: List of tool IDs that are enabled
            tool_configs: Dictionary mapping tool IDs to their configurations
            
        Returns:
            List of callable functions
        """
        functions = []
        
        for tool_id in enabled_tools:
            tool = self.get(tool_id)
            if tool:
                config = tool_configs.get(tool_id, {})
                if tool.validate_config(config):
                    func = tool.create_function(config)
                    functions.append(func)
                else:
                    print(f"Invalid configuration for tool {tool_id}")
        
        return functions


# Global registry instance
tool_registry = ToolRegistry()