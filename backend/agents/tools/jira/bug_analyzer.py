import requests
from requests.auth import HTT<PERSON><PERSON>asic<PERSON>uth
from google import genai
from google.genai import types as genai_types
from pathlib import Path
import json
import os
import mimetypes

from .type import BugAnalyzerConfig

BASE_PATH = os.path.dirname(os.path.abspath(__file__))
WORKSPACE_PATH = os.path.join(BASE_PATH, "../../../../workspace/bug_analyzer")


def get_ticket(ticket_id, config:BugAnalyzerConfig):
    jira_url = config.get("JIRA_URL", "https://jiffy-ai.atlassian.net")
    url = f"{jira_url}/rest/api/2/issue/{ticket_id}"
    
    attachments_folder = os.path.join(WORKSPACE_PATH, ticket_id, "attachments")
    os.makedirs(attachments_folder, exist_ok=True)
    
    jira_key = config.get("JIRA_KEY")
    if not jira_key:
        raise ValueError("JIRA_KEY is not set")
    
    jira_user = config.get("JIRA_USER")
    if not jira_user:
        raise ValueError("JIRA_USER is not set")

    auth = HTTPBasicAuth(jira_user, jira_key)
    headers = {"Accept": "application/json"}
    response = requests.request("GET", url, headers=headers, auth=auth)
    bug_details = response.json()

    bug_details_path = os.path.join(WORKSPACE_PATH, ticket_id, "bug_details.json")
    with open(bug_details_path, "w") as outfile:
        json.dump(bug_details, outfile)

    attachments = bug_details["fields"]["attachment"]
    for attachment in attachments:
        content_path = attachment["content"]
        content_repsonse = requests.request("GET", content_path, auth=auth)
        file_name = attachment["filename"]
        file_path = os.path.join(attachments_folder, file_name)
        with open(file_path, "wb") as file:
            file.write(content_repsonse.content)
    
    return bug_details, attachments_folder


def analyze_bug(ticket_id:str, config:BugAnalyzerConfig) -> str:
    bug_details, attachments_folder = get_ticket(ticket_id, config)
    
    genai_client = genai.Client(
        vertexai=True,
        project="apex-dev-377304",
        location="us-central1",
    )

    system_prompt = '''
        You are a bug analizer and planner system. You will be given a bug description and attachments related to the bug.
        Your job is to analyze the bug and provide a detailed plan to fix the bug.
        This ouput will be then given to AI code generator like augment code or cursor to generate the code to fix the bug.
    '''
    
    genai_config = genai_types.GenerateContentConfig(
        system_instruction=genai_types.Content(
            parts=[genai_types.Part(text=system_prompt)],
            role="modal",
        ),
        response_mime_type="application/json",
        temperature=0.4,
    )
    contents = []
    contents.append(f'{bug_details["fields"]["summary"]}\n {bug_details["fields"]["description"]}')
    attachments = os.listdir(attachments_folder)
    for attachment in attachments:
        file_path = os.path.join(attachments_folder, attachment)
        attachment_content = Path(file_path).read_bytes()
        mime_type, _ = mimetypes.guess_type(file_path)
        contents.append(genai_types.Part.from_bytes(data=attachment_content, mime_type=mime_type))
        
    response =  genai_client.models.generate_content(
        model="gemini-2.5-pro-preview-03-25",
        contents=contents,
        config=genai_config,
    )
    bug_fix_plan_path = os.path.join(WORKSPACE_PATH, ticket_id, "bug_fix_plan.txt")
    with open(bug_fix_plan_path, "w") as outfile:
        outfile.write(response.text)
    
    return response.text
