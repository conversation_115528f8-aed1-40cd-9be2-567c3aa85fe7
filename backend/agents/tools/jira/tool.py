"""JIRA Bug Analyzer tool implementation."""
from typing import Dict, Any, Callable

from ..base import BaseTool, tool_registry
from .bug_analyzer import analyze_bug


class JiraBugAnalyzerTool(BaseTool):
    """Tool for analyzing JIRA bugs and creating fix plans."""
    
    @property
    def id(self) -> str:
        return "jira_bug_analyzer"
    
    @property
    def name(self) -> str:
        return "JIRA Bug Analyzer"
    
    @property
    def description(self) -> str:
        return "Analyzes JIRA bugs and provides detailed plans to fix them (uses tenant's JIRA credentials by default)"
    
    @property
    def config_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "JIRA_KEY": {
                    "type": "string",
                    "title": "JIRA API Key (Optional)",
                    "description": "Override the tenant's JIRA API key (uses tenant's credentials if not provided)"
                },
                "JIRA_USER": {
                    "type": "string",
                    "title": "JIRA User Email (Optional)",
                    "description": "Override the tenant's JIRA email (uses tenant's credentials if not provided)"
                },
                "JIRA_URL": {
                    "type": "string",
                    "title": "JIRA Base URL (Optional)",
                    "description": "Override the tenant's JIRA URL (uses tenant's credentials if not provided)"
                }
            },
            "required": []  # No fields are required since we use tenant's credentials by default
        }
    
    def validate_config(self, config: Dict[str, Any]) -> bool:
        """
        Validate the configuration. Since all fields are optional
        (using tenant defaults), we just need to ensure we have
        the required fields either from config or they'll come from tenant.
        """
        # If config has at least one of the credential fields or is empty, it's valid
        # Empty config is valid because we'll use tenant's credentials
        return True
    
    def create_function(self, config: Dict[str, Any]) -> Callable:
        """Create the bug analyzer function with the given configuration."""
        import logging
        logger = logging.getLogger(__name__)
        logger.info(f"Creating JIRA bug analyzer function with config keys: {list(config.keys())}")
        
        def jira_bug_analyzer(ticket_id: str) -> str:
            """
            Analyzes the JIRA bug and returns the result
            
            Args:
                ticket_id: The JIRA ticket ID of the bug
            Returns:
                The result of the analysis
            """
            # Config will have tenant credentials merged in by developer_agent.py
            logger.info(f"Analyzing JIRA ticket: {ticket_id}")
            return analyze_bug(ticket_id, config)
        
        return jira_bug_analyzer


# Register the tool
tool_registry.register(JiraBugAnalyzerTool())