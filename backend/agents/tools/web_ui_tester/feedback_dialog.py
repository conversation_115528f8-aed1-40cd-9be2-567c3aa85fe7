#!/usr/bin/env python3
"""
Feedback Dialog - A PyQt-based dialog for collecting user feedback.

This module provides a dialog for collecting user feedback about code changes,
including success/failure and detailed comments when needed.

Usage:
    from feedback_dialog import show_feedback_dialog
    feedback = show_feedback_dialog()

Author: Augment Code
"""

import os
import sys
import subprocess
import traceback
from typing import Dict, Any

from PyQt5.QtWidgets import QApplication, QDialog, QVBoxLayout, QHBoxLayout
from PyQt5.QtWidgets import QTextEdit, QPushButton, QRadioButton, QGroupBox, QMessageBox
from PyQt5.QtCore import Qt


def get_active_display():
    """Get the active X display if available."""
    # First check if DISPLAY is already set
    display = os.environ.get("DISPLAY")
    if display:
        return display

    # Try to find an active display using who command
    try:
        who_output = subprocess.check_output(["who"]).decode()
        for line in who_output.split("\n"):
            if "(" in line and ")" in line:
                display = line.split("(")[1].split(")")[0]
                if display.startswith(":"):
                    return display
    except subprocess.CalledProcessError:
        pass

    # If no display found, return default
    return ":0"


class FeedbackDialog(QDialog):
    """Dialog for collecting user feedback about code changes."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("Feedback Collection")
        self.setMinimumWidth(500)
        self.setMinimumHeight(100)

        # Initialize feedback data
        self.success = False
        self.feedback_text = ""

        # Create layout
        self.layout = QVBoxLayout()

        # Add success question
        self.success_group = QGroupBox("Did the code changes work as expected?")
        success_layout = QHBoxLayout()

        self.yes_button = QRadioButton("Yes")
        self.no_button = QRadioButton("No")
        self.yes_button.toggled.connect(self.on_success_toggled)
        self.no_button.toggled.connect(self.on_success_toggled)

        success_layout.addWidget(self.yes_button)
        success_layout.addWidget(self.no_button)
        self.success_group.setLayout(success_layout)

        # Add feedback text area (initially hidden)
        self.feedback_group = QGroupBox("Additional feedback or comments:")
        self.feedback_group.setVisible(False)  # Initially hidden
        feedback_layout = QVBoxLayout()

        self.feedback_text_edit = QTextEdit()
        feedback_layout.addWidget(self.feedback_text_edit)
        self.feedback_group.setLayout(feedback_layout)

        # Add submit button
        self.submit_button = QPushButton("Submit Feedback")
        self.submit_button.clicked.connect(self.accept)

        # Add all widgets to main layout
        self.layout.addWidget(self.success_group)
        self.layout.addWidget(self.feedback_group)
        self.layout.addWidget(self.submit_button)

        self.setLayout(self.layout)

    def on_success_toggled(self, checked):
        """Handle radio button toggle events."""
        if checked:
            self.success = self.yes_button.isChecked()
            # Show feedback text area only when "No" is selected
            self.feedback_group.setVisible(not self.success)

            # Adjust dialog size based on visibility of feedback group
            if not self.success:
                self.setMinimumHeight(300)
            else:
                self.setMinimumHeight(110)
                self.resize(self.width(), 100)

    def get_feedback(self) -> Dict[str, Any]:
        """Get the feedback data collected from the dialog."""
        # Only get feedback text if "No" was selected
        if not self.success:
            self.feedback_text = self.feedback_text_edit.toPlainText()
        else:
            self.feedback_text = ""

        return {
            "success": self.success,
            "feedback": self.feedback_text
        }


def show_feedback_dialog() -> Dict[str, Any]:
    """
    Show a PyQt feedback dialog and return the collected feedback.
    This function is designed to be called from a separate process or MCP tool.

    Returns:
        Dict containing feedback data with keys:
        - success: bool indicating if the changes worked
        - feedback: str with detailed feedback comments (only if success is False)
    """
    try:
        # Ensure we have a valid display for X11
        display = get_active_display()
        os.environ["DISPLAY"] = display

        # Create QApplication instance
        # Note: We need to keep this variable even though it's not directly used
        # because the QApplication instance must exist for the dialog to work
        # pylint: disable=unused-variable
        app = QApplication.instance() or QApplication(sys.argv)  # noqa

        # Create the feedback dialog
        dialog = FeedbackDialog()

        # Show the dialog and get the result
        result = dialog.exec_()

        # Get feedback data if dialog was accepted
        if result == QDialog.Accepted:
            return dialog.get_feedback()
        else:
            # If dialog was cancelled, create default feedback
            return {
                "success": False,
                "feedback": "Dialog was cancelled"
            }
    except Exception as e:
        try:
            if QApplication.instance():
                QMessageBox.critical(None, "Error", f"Could not show feedback dialog: {str(e)}")
        except Exception:
            # Ignore any errors when trying to show the error message
            pass

        # Return a default response
        return {
            "success": True,  # Assume success to avoid blocking the process
            "feedback": f"Error showing feedback dialog: {str(e)}"
        }


if __name__ == "__main__":
    # Test the dialog if this file is run directly
    app = QApplication(sys.argv)
    feedback = show_feedback_dialog()
    print(f"Feedback: {feedback}")
