import os
import json
import glob
import mimetypes

from google import genai
from google.genai import types as genai_types
from pathlib import Path



def get_json_content(file_path):
    with open(file_path, "r") as file:
        content = json.load(file)
    return json.dumps(content)



system_prompt = '''
    You are an advanced bug analysis and debugging system.
    As a critical component of our bug fix pipeline, you are activated when a bug fix implementation fails during testing.
    You will receive comprehensive data including:
    1. Detailed bug description
    2. Console logs from the failed test
    3. Video recording of the test execution
    4. User actions leading to the bug
    5. Network requests made during the test
    6. Relevant code snippets and file contents

    Your primary objectives are to:
    1. Thoroughly analyze all provided information
    2. Identify the root cause of why the attempted fix was unsuccessful
    3. Provide a detailed explanation of the issue
    4. Suggest potential solutions or areas for further investigation
    5. Highlight any patterns or recurring issues that may be contributing to the bug

    Please provide your analysis in a clear, structured format, using markdown for better readability when appropriate.
'''


def analyze_logs(session_output_dir, output_dir):
    config = genai_types.GenerateContentConfig(
        system_instruction=genai_types.Content(
            parts=[genai_types.Part(text=system_prompt)],
            role="modal",
        ),
        response_mime_type="application/json",
        temperature=0.4,
    )
    contents = []
    console_log_path = os.path.join(session_output_dir, "console_logs.json")
    if os.path.exists(console_log_path):
        contents.extend([
            "Browser console logs",
            get_json_content(console_log_path)
        ])
    network_request_path = os.path.join(session_output_dir, "requests.json")
    if os.path.exists(network_request_path):
        contents.extend([
            "Network requests",
            get_json_content(network_request_path)
        ])
    user_actions_path = os.path.join(session_output_dir, "user_actions.json")
    if os.path.exists(user_actions_path):
        contents.extend([
            "User actions",
            get_json_content(user_actions_path)
        ])
    feedback_path = os.path.join(session_output_dir, "feedback.json")
    if os.path.exists(feedback_path):
        contents.extend([
            "Test execution feedback",
            get_json_content(feedback_path)
        ])


    video_files = glob.glob(os.path.join(session_output_dir, "*.webm"))
    if video_files:
        video_file = video_files[0]
        video_file_content = Path(video_file).read_bytes()
        mime_type, _ = mimetypes.guess_type(video_file)
        contents.extend([
            "Test execution video",
            genai_types.Part.from_bytes(data=video_file_content, mime_type=mime_type)
        ])

    genai_client = genai.Client(
        vertexai=True,
        project="apex-dev-377304",
        location="us-central1",
    )
    response =  genai_client.models.generate_content(
        model="gemini-2.5-pro-preview-03-25",
        contents=contents,
        config=config,
    )
    
    log_analysis_path = os.path.join(output_dir, "log_analysis.txt")
    with open(log_analysis_path, "w") as outfile:
        outfile.write(response.text)
    
    return response.text