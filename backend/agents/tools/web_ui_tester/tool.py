import os
import subprocess
import uuid
import asyncio


from .webpage_monitor import WebpageMonitor
from .log_analyzer import analyze_logs
from .type import WebUITesterConfig

BASE_PATH = os.path.dirname(os.path.abspath(__file__))
WORKSPACE_PATH = os.path.join(BASE_PATH, "../../../../workspace/web_ui_tester")

def get_active_display():
    """Get the active X display if available."""
    # xhost +local:
    # First check if DISPLAY is already set
    display = os.environ.get("DISPLAY")
    if display:
        return display

    # Try to find an active display using who command
    try:
        who_output = subprocess.check_output(["who"]).decode()
        for line in who_output.split("\n"):
            if "(" in line and ")" in line:
                display = line.split("(")[1].split(")")[0]
                if display.startswith(":"):
                    return display
    except subprocess.CalledProcessError:
        pass

    # If no display found, return default
    return ":0"


async def run_monitor(url:str, output_dir:str):
    monitor = WebpageMonitor(
        url=url,
        output_dir=output_dir,
        headless=False,
        record_video=True,
        track_user_actions=True,
        collect_feedback=True,
    )

    return  await monitor.run()


def web_ui_tester(config:WebUITesterConfig) -> str:
    """
    Tests the web UI and returns the result

    Args:
        ticket_id: The jira ticket id of the bug
    Returns:
        The result of the analysis
    """
    output_dir = os.path.join(WORKSPACE_PATH, str(uuid.uuid4()))
    os.makedirs(output_dir, exist_ok=True)

    # Get the active display
    display = get_active_display()

    # Set the DISPLAY environment variable
    os.environ["DISPLAY"] = display
    # os.environ["DISPLAY"] = ":1"

    # Note: We could try multiple displays if needed
    # For now, we'll just use the detected display

    # Create and run the monitor
    url = config.get("URL")
    if not url:
        raise ValueError("URL is not configured")

    run_info, session_dir = asyncio.run(run_monitor(url, output_dir))
    if run_info["success"]:
        return "Code fix tested successfully"
    else:
        return analyze_logs(session_dir, output_dir)
