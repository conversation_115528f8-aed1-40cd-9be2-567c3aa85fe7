"""Web UI Tester tool implementation."""
from typing import Dict, Any, Callable

from ..base import BaseTool, tool_registry
from .tool import web_ui_tester


class WebUITesterTool(BaseTool):
    """Tool for testing web UI and analyzing issues."""
    
    @property
    def id(self) -> str:
        return "web_ui_tester"
    
    @property
    def name(self) -> str:
        return "Web UI Tester"
    
    @property
    def description(self) -> str:
        return "Tests web UI and analyzes logs for issues"
    
    @property
    def config_schema(self) -> Dict[str, Any]:
        return {
            "type": "object",
            "properties": {
                "URL": {
                    "type": "string",
                    "title": "Test URL",
                    "description": "The URL to test",
                    "format": "uri"
                },
                "HEADLESS": {
                    "type": "boolean",
                    "title": "Headless Mode",
                    "description": "Run browser in headless mode",
                    "default": False
                },
                "RECORD_VIDEO": {
                    "type": "boolean",
                    "title": "Record Video",
                    "description": "Record video of the test session",
                    "default": True
                },
                "TIMEOUT": {
                    "type": "integer",
                    "title": "Timeout (seconds)",
                    "description": "Maximum time to wait for page load",
                    "default": 30,
                    "minimum": 5,
                    "maximum": 300
                }
            },
            "required": ["URL"]
        }
    
    def create_function(self, config: Dict[str, Any]) -> Callable:
        """Create the web UI tester function with the given configuration."""
        def web_ui_test() -> str:
            """
            Tests the web UI and returns the result
            
            Returns:
                The result of the test
            """
            return web_ui_tester(config)
        
        return web_ui_test


# Register the tool
tool_registry.register(WebUITesterTool())