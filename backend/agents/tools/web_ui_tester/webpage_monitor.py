#!/usr/bin/env python3
"""
Webpage Monitor - A tool to capture API requests, console logs, screen recordings, and user actions.

This script uses <PERSON><PERSON> to monitor a webpage and capture:
1. All network requests and responses
2. <PERSON>sole logs
3. Screen recording
4. User actions (clicks, inputs, navigation)

Usage:
    python webpage_monitor.py --url https://example.com [options]

Author: Augment Code
"""

import os
import json
import time
import sys
import asyncio
import argparse
import multiprocessing
from datetime import datetime
from typing import Dict, List, Any

from playwright.async_api import async_playwright, Request, Response, ConsoleMessage, Page
from .feedback_dialog import show_feedback_dialog




class WebpageMonitor:
    """Monitor a webpage for network requests, console logs, user actions, and create screen recordings."""

    def __init__(
        self,
        url: str,
        output_dir: str = "output",
        headless: bool = False,
        record_video: bool = True,
        track_user_actions: bool = True,
        collect_feedback: bool = False,
    ):
        """
        Initialize the WebpageMonitor.

        Args:
            url: The URL to monitor
            output_dir: Directory to save output files
            headless: Whether to run the browser in headless mode
            record_video: Whether to record video
            track_user_actions: Whether to track user actions
            collect_feedback: Whether to show a feedback dialog after monitoring
        """
        self.url = url
        self.output_dir = output_dir
        self.headless = headless
        self.record_video = record_video
        self.track_user_actions = track_user_actions
        self.collect_feedback = collect_feedback
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Create output directory
        self.session_dir = os.path.join(output_dir, f"session_{self.timestamp}")
        os.makedirs(self.session_dir, exist_ok=True)

        # Initialize data structures
        self.requests: List[Dict[str, Any]] = []
        self.console_logs: List[Dict[str, str]] = []
        self.user_actions: List[Dict[str, Any]] = []
        self.current_url = url

    async def on_request(self, request: Request) -> None:
        """Capture request information."""
        if request.resource_type not in ["fetch", "xhr"]:
            return

        request_data = {
            "url": request.url,
            "method": request.method,
            "headers": request.headers,
            "timestamp": time.time(),
            "resourceType": request.resource_type,
            "postData": request.post_data,
        }
        self.requests.append(request_data)

    async def on_response(self, response: Response) -> None:
        """Capture response information."""
        if response.request.resource_type not in ["fetch", "xhr"]:
            return

        # Find the matching request
        for req in self.requests:
            if req["url"] == response.url:
                req["response"] = {
                    "status": response.status,
                    "statusText": response.status_text,
                    "headers": response.headers,
                    "timestamp": time.time(),
                }
                # Try to get the response body for API calls
                if (
                    "json" in response.headers.get("content-type", "").lower()
                    or "application/javascript" in response.headers.get("content-type", "").lower()
                    or "text" in response.headers.get("content-type", "").lower()
                ):
                    try:
                        req["response"]["body"] = await response.text()
                    except Exception as e:
                        req["response"]["body"] = f"Error getting response body: {str(e)}"
                break

    async def on_console(self, msg: ConsoleMessage) -> None:
        """Capture console messages."""
        log_entry = {
            "type": msg.type,
            "text": msg.text,
            "timestamp": time.time(),
        }
        self.console_logs.append(log_entry)

    async def on_click(self, event: Dict[str, Any]) -> None:
        """Capture click events."""
        if not self.track_user_actions:
            return

        action = {
            "type": "click",
            "timestamp": time.time(),
            "x": event.get("x", 0),
            "y": event.get("y", 0),
            "button": event.get("button", "left"),
            "target": event.get("target", {})
        }

        self.user_actions.append(action)

    async def on_input(self, event: Dict[str, Any]) -> None:
        """Capture input events."""
        if not self.track_user_actions:
            return

        # Don't log passwords
        input_type = event.get("inputType", "")
        is_password = event.get("isPassword", False)
        value = "********" if is_password else event.get("value", "")

        action = {
            "type": "input",
            "timestamp": time.time(),
            "inputType": input_type,
            "value": value,
            "target": event.get("target", {})
        }

        self.user_actions.append(action)

    async def on_navigation(self, url: str) -> None:
        """Capture navigation events."""
        if not self.track_user_actions or url == self.current_url:
            return

        action = {
            "type": "navigation",
            "timestamp": time.time(),
            "from": self.current_url,
            "to": url
        }

        self.current_url = url
        self.user_actions.append(action)
    
    async def capture_user_actions(self, page:Page):
        # Check for URL changes
        current_url = page.url
        if current_url != self.current_url:
            await self.on_navigation(current_url)

        # Poll for user actions from the page
        try:
            actions = await page.evaluate("""() => {
                const actions = window.userActions || [];
                window.userActions = [];
                return actions;
            }""")
        except Exception:
            actions = []

        # Process the actions
        for action in actions:
            if action["type"] == "click":
                await self.on_click(action["data"])
            elif action["type"] == "input":
                await self.on_input(action["data"])

    async def run(self) -> tuple[Dict[str, Any], str]:
        """
        Run the monitoring session until the browser window is closed.
        """
        async with async_playwright() as playwright:

            # Launch browser
            browser = await playwright.chromium.launch(
                headless=self.headless,
                args=['--disable-web-security', '--disable-features=IsolateOrigins,site-per-process']
            )

            # Get screen dimensions for viewport
            try:
                import screeninfo
                screen = screeninfo.get_monitors()[0]
                screen_width = screen.width
                screen_height = screen.height - 100
            except Exception:
                # Fallback to default dimensions if screen info is not available
                screen_width = 1920
                screen_height = 1080 - 100

            # Create a new context with video recording if enabled
            context_options = {}
            if self.record_video:
                context_options["record_video_size"] = {"width": screen_width, "height": screen_height}
                context_options["record_video_dir"] = self.session_dir

            # Create a new browser context
            context = await browser.new_context(**context_options)

            # Create a new page with the specified viewport size
            page = await context.new_page()
            await page.set_viewport_size({"width": screen_width, "height": screen_height})

            # Register event handlers
            page.on("request", self.on_request)
            page.on("response", self.on_response)
            page.on("console", self.on_console)

            # Set up user action tracking if enabled
            if self.track_user_actions:
                # Inject JavaScript to track user actions
                await page.add_init_script("""
                    window.userActions = [];

                    // Track clicks
                    document.addEventListener('click', function(e) {
                        const target = e.target;
                        const targetInfo = {
                            tagName: target.tagName,
                            id: target.id,
                            className: target.className,
                            textContent: target.textContent ? target.textContent.substring(0, 50) : '',
                            href: target.href || '',
                            value: target.value || '',
                            type: target.type || ''
                        };

                        window._playwright_click_event = {
                            x: e.clientX,
                            y: e.clientY,
                            button: e.button === 0 ? 'left' : e.button === 1 ? 'middle' : 'right',
                            target: targetInfo
                        };
                    });

                    // Track input
                    document.addEventListener('input', function(e) {
                        const target = e.target;
                        const isPassword = target.type === 'password';
                        const targetInfo = {
                            tagName: target.tagName,
                            id: target.id,
                            className: target.className,
                            name: target.name || '',
                            type: target.type || ''
                        };

                        window._playwright_input_event = {
                            inputType: e.inputType || 'input',
                            isPassword: isPassword,
                            value: target.value || '',
                            target: targetInfo
                        };
                    });
                """)

                # Set up interval to check for user actions
                await page.add_init_script("""
                    setInterval(() => {
                        if (window._playwright_click_event) {
                            window.userActions.push({
                                type: 'click',
                                data: window._playwright_click_event,
                                timestamp: Date.now()
                            });
                            window._playwright_click_event = null;
                        }

                        if (window._playwright_input_event) {
                            window.userActions.push({
                                type: 'input',
                                data: window._playwright_input_event,
                                timestamp: Date.now()
                            });
                            window._playwright_input_event = null;
                        }
                    }, 100);
                """)

            try:
                # Navigate to the URL
                await page.goto(self.url, timeout=120 * 1000)

                # Create a flag to track if the browser is closed
                browser_closed = False

                # Define a callback for when the browser is closed
                def on_browser_closed():
                    nonlocal browser_closed
                    browser_closed = True

                # Register the callback
                browser.on("disconnected", on_browser_closed)

                # Wait for the browser to be closed
                while not browser_closed:
                    # Check every second if the browser is still open
                    try:
                        # This will throw an exception if the browser is closed
                        if browser.is_connected():
                            # Check for user actions if tracking is enabled
                            if self.track_user_actions:
                                await self.capture_user_actions(page)

                            # Wait for a short time before checking again
                            await page.wait_for_timeout(100)  # Wait for 100ms
                        else:
                            break
                    except Exception as e:
                        # Browser was closed or error occurred
                        break

            finally:
                # Save the collected data
                await self._save_data()

                # Make sure the browser is closed
                try:
                    if browser.is_connected():
                        await browser.close()
                except Exception:
                    pass

            # Show feedback dialog after browser is closed if enabled
            if self.collect_feedback:
                return await self._show_feedback_dialog(), self.session_dir
            else:
                return {
                    "success": True,
                    "feedback": ""
                }, self.session_dir

    async def _show_feedback_dialog(self) -> Dict:
        """Show a dialog to get user feedback about the code changes using PyQt."""
        # We need to run the PyQt dialog in a separate process since we're in an async context
        # and PyQt requires the QApplication to be in the main thread

        try:
            
            feedback = show_feedback_dialog()

            # Save the feedback
            with open(os.path.join(self.session_dir, "feedback.json"), "w") as f:
                json.dump(feedback, f, indent=2)

            return feedback
        except Exception as e:
            # Create a default feedback response
            feedback = {
                "success": True,  # Assume success to avoid blocking the process
                "feedback": f"Error collecting feedback: {str(e)}"
            }

            # Save the error feedback
            with open(os.path.join(self.session_dir, "feedback_error.json"), "w") as f:
                json.dump({"error": str(e)}, f, indent=2)

            return feedback

    async def _save_data(self) -> None:
        """Save the collected data to files."""
        # Save network requests
        with open(os.path.join(self.session_dir, "requests.json"), "w") as f:
            json.dump(self.requests, f, indent=2)

        # Save console logs
        with open(os.path.join(self.session_dir, "console_logs.json"), "w") as f:
            json.dump(self.console_logs, f, indent=2)

        # Save user actions if tracking was enabled
        if self.track_user_actions:
            with open(os.path.join(self.session_dir, "user_actions.json"), "w") as f:
                json.dump(self.user_actions, f, indent=2)

        # Create a summary file
        summary = {
            "url": self.url,
            "timestamp": self.timestamp,
            "duration": time.time() - (self.requests[0]["timestamp"] if self.requests else time.time()),
            "requestCount": len(self.requests),
            "consoleLogCount": len(self.console_logs),
            "userActionCount": len(self.user_actions) if self.track_user_actions else 0,
        }

        with open(os.path.join(self.session_dir, "summary.json"), "w") as f:
            json.dump(summary, f, indent=2)


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Monitor a webpage for network requests, console logs, user actions, and create screen recordings.")
    parser.add_argument("--url", required=True, help="URL to monitor")
    parser.add_argument("--output-dir", default="output", help="Directory to save output files")
    parser.add_argument("--headless", action="store_true", default=False, help="Run in headless mode")
    parser.add_argument("--no-video", action="store_true", help="Disable video recording")
    parser.add_argument("--no-user-actions", action="store_true", help="Disable user action tracking")
    parser.add_argument("--xhr-only", action="store_true", help="Capture only XHR/Fetch requests")
    parser.add_argument("--collect-feedback", action="store_true", help="Show a feedback dialog after monitoring")

    return parser.parse_args()


async def main_async():
    """Async main entry point."""
    args = parse_args()

    monitor = WebpageMonitor(
        url=args.url,
        output_dir=args.output_dir,
        headless=args.headless,
        record_video=not args.no_video,
        track_user_actions=not args.no_user_actions,
        collect_feedback=args.collect_feedback,
    )

    feedback, session_dir = await monitor.run()
    return feedback, session_dir


def main():
    """Main entry point that runs the async main function."""
    feedback, session_dir = asyncio.run(main_async())
    return feedback, session_dir


if __name__ == "__main__":
    main()
