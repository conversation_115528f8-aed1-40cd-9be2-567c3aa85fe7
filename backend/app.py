from flask import Flask, jsonify
from flask_cors import CORS

from config import Config
from extensions import db, migrate, jwt, socketio


def create_app(config_class=Config):
    app = Flask(__name__)
    app.config.from_object(config_class)

    # Initialize logging
    config_class.init_app(app)
    app.logger.debug("Initializing Flask application")

    # Enable CORS with full configuration
    app.logger.debug("Configuring CORS")
    CORS(app, resources={r"/*": {
        "origins": ["http://localhost:3000", "http://127.0.0.1:3000"],
        "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
        "allow_headers": ["Content-Type", "Authorization", "X-Requested-With"]
    }})

    # Initialize Flask extensions
    app.logger.debug("Initializing SQLAlchemy")
    db.init_app(app)
    app.logger.debug("Initializing Flask-Migrate")
    migrate.init_app(app, db)
    app.logger.debug("Initializing JWT Manager")
    jwt.init_app(app)
    app.logger.debug("Flask extensions initialized")

    # Register blueprints
    app.logger.debug("Registering blueprints")

    app.logger.debug("Registering auth blueprint")
    from blueprints.auth import auth_bp
    app.register_blueprint(auth_bp, url_prefix='/tenants/<uuid:tenant_id>/auth')

    app.logger.debug("Registering tenants blueprint")
    from blueprints.tenants import tenants_bp
    app.register_blueprint(tenants_bp, url_prefix='/admin/tenants') # For platform admin operations

    app.logger.debug("Registering JIRA blueprint")
    from blueprints.jira import jira_bp
    app.register_blueprint(jira_bp, url_prefix='/tenants/<uuid:tenant_id>/jira')

    app.logger.debug("Registering projects blueprint")
    from blueprints.projects import projects_bp
    app.register_blueprint(projects_bp, url_prefix='/tenants/<uuid:tenant_id>/projects')

    app.logger.debug("Registering issues blueprint")
    from blueprints.issues import issues_bp
    # Mounting issues_bp at /tenants/<uuid:tenant_id>/ to match the API spec paths:
    # GET /tenants/{tid}/projects/{pkey}/issues -> route inside issues_bp: /projects/<project_key>/issues
    # GET /tenants/{tid}/issues/{ikey} -> route inside issues_bp: /issues/<issue_key> (needs adjustment in issues.py)
    # For now, let's adjust issues_bp registration or its routes. The API spec is:
    # GET /tenants/{tid}/projects/{pkey}/issues
    # GET /tenants/{tid}/issues/{ikey}
    # This suggests issues related to a project are under projects, and general issue lookup is under issues.
    # To keep current blueprint structure, we mount issues_bp at /tenants/<uuid:tenant_id>/
    # and its internal routes are /projects/<project_key>/issues and /<issue_key>
    app.register_blueprint(issues_bp, url_prefix='/tenants/<uuid:tenant_id>')

    app.logger.debug("Registering user activity blueprint")
    from blueprints.user_activity import user_activity_bp
    app.register_blueprint(user_activity_bp, url_prefix='/tenants/<uuid:tenant_id>/user-activity')

    # Register repositories blueprint
    # app.logger.debug("Registering repositories blueprint")
    # from blueprints.repositories import repos_bp
    # app.register_blueprint(repos_bp, url_prefix='/tenants/<uuid:tenant_id>/repositories')

    app.logger.debug("Registering pull requests blueprint")
    from blueprints.pull_requests import pr_bp
    app.register_blueprint(pr_bp, url_prefix='/tenants/<uuid:tenant_id>/pull-requests')

    app.logger.debug("Registering public tenants blueprint")
    from blueprints.public_tenants import public_tenants_bp
    app.register_blueprint(public_tenants_bp, url_prefix='/api/public/tenants')

    app.logger.debug("Registering repository source management blueprint")
    from blueprints.repository_source_management import repository_source_bp
    app.register_blueprint(repository_source_bp, url_prefix='/tenants/<uuid:tenant_id>')

    # Comment out repository management to avoid conflicts with repository source management
    # app.logger.debug("Registering repository management blueprint")
    # from blueprints.repository_management import repository_management_bp
    # app.register_blueprint(repository_management_bp, url_prefix='/tenants/<uuid:tenant_id>')

    # Debug endpoint for repository troubleshooting
    app.logger.debug("Registering debug repository blueprint")
    from blueprints.debug_repository import debug_repo_bp
    app.register_blueprint(debug_repo_bp, url_prefix='/tenants/<uuid:tenant_id>')

    app.logger.debug("Registering user repository paths blueprint")
    from blueprints.user_repository_paths import user_repo_paths_bp
    app.register_blueprint(user_repo_paths_bp, url_prefix='/tenants/<uuid:tenant_id>')

    app.logger.debug("Registering agent execution blueprint")
    from blueprints.agent_execution import agent_execution_bp
    app.register_blueprint(agent_execution_bp, url_prefix='/tenants/<uuid:tenant_id>/agents')

    app.logger.debug("Registering functional areas blueprint")
    from blueprints.functional_areas import functional_areas_bp
    app.register_blueprint(functional_areas_bp)

    app.logger.debug("All blueprints registered")

    # Root route
    @app.route('/')
    def root():
        return jsonify({
            "name": "JIRA Bug Browser API",
            "version": "1.0.0",
            "description": "API for multi-tenant JIRA Bug Browser application",
            "documentation": "/api-docs",
            "health": "/health",
            "endpoints": {
                "auth": "/tenants/{tenant_id}/auth",
                "projects": "/tenants/{tenant_id}/projects",
                "issues": "/tenants/{tenant_id}/issues",
                "jira": "/tenants/{tenant_id}/jira",
                "repositories": "/tenants/{tenant_id}/repositories",
                "pull_requests": "/tenants/{tenant_id}/pull-requests",
                "repository_management": "/tenants/{tenant_id}/repositories",
                "functional_areas": "/tenants/{tenant_id}/functional-areas",
                "admin": "/admin/tenants",
                "public": "/api/public/tenants"
            }
        }), 200

    # Basic route for testing
    @app.route('/health')
    def health_check():
        return jsonify({"status": "healthy"}), 200

    # Error Handlers (as per RFC 7807 Problem Details)
    app.logger.debug("Registering error handlers")

    @app.errorhandler(400)
    def bad_request_error(error):
        app.logger.error(f"400 Bad Request: {str(error)}")
        return jsonify(title="Bad Request", status=400, detail=str(error)), 400

    @app.errorhandler(401)
    def unauthorized_error(error):
        app.logger.error(f"401 Unauthorized: {str(error)}")
        return jsonify(title="Unauthorized", status=401, detail=str(error)), 401

    @app.errorhandler(403)
    def forbidden_error(error):
        app.logger.error(f"403 Forbidden: {str(error)}")
        return jsonify(title="Forbidden", status=403, detail=str(error)), 403

    @app.errorhandler(404)
    def not_found_error(error):
        app.logger.error(f"404 Not Found: {str(error)}")
        return jsonify(title="Not Found", status=404, detail=str(error)), 404

    @app.errorhandler(423)
    def locked_error(error):
        app.logger.error(f"423 Locked: {str(error)}")
        return jsonify(title="Locked", status=423, detail=str(error.description if hasattr(error, 'description') else "Tenant suspended or resource locked.")), 423

    @app.errorhandler(500)
    def internal_server_error(error):
        app.logger.error(f"500 Internal Server Error: {str(error)}")
        return jsonify(title="Internal Server Error", status=500, detail=str(error)), 500

    app.logger.debug("Error handlers registered")

    # Initialize SocketIO
    app.logger.debug("Initializing SocketIO")
    socketio.init_app(
        app,
        cors_allowed_origins="*",     # For development only
        async_mode='threading',       # Force threading mode
        ping_timeout=60,              # Longer ping timeout
        ping_interval=25,             # Longer ping interval
        manage_session=False,         # Let Flask manage sessions
    )

    # Initialize SocketIO service (will be imported by other modules)
    app.logger.debug("Initializing SocketIO service")
    # The service is initialized when imported

    app.logger.debug("Flask application initialization complete")
    return app

if __name__ == '__main__':
    app = create_app()
    # Use socketio.run instead of app.run for proper WebSocket support
    # Add necessary parameters to fix Werkzeug errors
    socketio.run(
        app,
        debug=True,
        port=5045,
        host='0.0.0.0',  # Listen on all interfaces
        log_output=True,  # Enable logging
        allow_unsafe_werkzeug=True  # Allow for development
    )