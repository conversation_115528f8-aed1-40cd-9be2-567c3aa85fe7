import os
import json
import logging
import asyncio
import threading
import time
import traceback
from flask import Blueprint, jsonify, request
from flask_jwt_extended import jwt_required, get_jwt
from flask_socketio import emit
from models import ProjectRepository, Repository, IssueCache, JiraCredential
import uuid
from app import create_app
from extensions import db, socketio
from utils import decrypt_api_key
from agents.developer_agent import create_dynamic_developer_agent
from datetime import datetime, timezone
from queue import Queue
import sys
from io import StringIO

from google.adk.runners import Runner
from google.adk.sessions import InMemorySessionService
from google.genai import types as genai_types

from services.socketio_service import socketio_service

agent_execution_bp = Blueprint("agent_execution_bp", __name__)
logger = logging.getLogger(__name__)

# Track active executions
active_executions = {}


@agent_execution_bp.route("/execute-bug-fix", methods=["POST"])
@jwt_required()
def execute_bug_fix(tenant_id):
    """Execute agent for bug fixing with selected repositories"""
    jwt_data = get_jwt()
    if jwt_data.get("tenant_id") != str(tenant_id):
        return jsonify(title="Forbidden", status=403, detail="Access forbidden"), 403

    # Store the JWT data for this session
    user_id = jwt_data.get("sub")

    data = request.get_json()
    issue_key = data.get("issue_key")
    repository_ids = data.get("repository_ids", [])

    if not issue_key:
        return (
            jsonify(title="Bad Request", status=400, detail="Issue key is required"),
            400,
        )

    if not repository_ids:
        return (
            jsonify(
                title="Bad Request",
                status=400,
                detail="At least one repository must be selected",
            ),
            400,
        )

    try:
        # Get issue details from cache
        issue = IssueCache.query.filter_by(
            tenant_id=tenant_id, issue_key=issue_key
        ).first()

        if not issue:
            return jsonify(title="Not Found", status=404, detail="Issue not found"), 404

        # Get selected repositories
        # Convert repository IDs to UUIDs if they're strings
        repo_uuids = []
        for repo_id in repository_ids:
            if isinstance(repo_id, str):
                try:
                    repo_uuids.append(uuid.UUID(repo_id))
                except ValueError:
                    return (
                        jsonify(
                            title="Bad Request",
                            status=400,
                            detail=f"Invalid repository ID: {repo_id}",
                        ),
                        400,
                    )
            else:
                repo_uuids.append(repo_id)

        # Use joinedload to eager-load the source relationship
        from sqlalchemy.orm import joinedload

        # Ensure tenant_id is a UUID object
        tenant_uuid = tenant_id
        if isinstance(tenant_id, str):
            try:
                tenant_uuid = uuid.UUID(tenant_id)
            except ValueError:
                return (
                    jsonify(
                        title="Bad Request",
                        status=400,
                        detail=f"Invalid tenant ID format",
                    ),
                    400,
                )

        repositories = (
            Repository.query.options(joinedload(Repository.source))
            .filter(Repository.id.in_(repo_uuids), Repository.tenant_id == tenant_uuid)
            .all()
        )

        if len(repositories) != len(repository_ids):
            return (
                jsonify(
                    title="Bad Request",
                    status=400,
                    detail="Some repositories not found",
                ),
                400,
            )

        # Create execution session
        execution_id = f"{issue_key}_{datetime.now(timezone.utc).timestamp()}"
        namespace_id = f"agent_{execution_id}"

        # Fetch JIRA credentials for the tenant
        jira_credentials = None
        jira_cred = JiraCredential.query.filter_by(tenant_id=tenant_uuid).first()
        if jira_cred:
            try:
                jira_credentials = {
                    'JIRA_URL': jira_cred.base_url,
                    'JIRA_USER': jira_cred.email,
                    'JIRA_KEY': decrypt_api_key(jira_cred.api_key_encrypted)
                }
                logger.info("Loaded JIRA credentials from tenant configuration")
            except Exception as e:
                logger.warning(f"Could not decrypt JIRA credentials: {e}")
                # Fall back to environment variables if available
                if os.environ.get('JIRA_KEY') and os.environ.get('JIRA_USER'):
                    jira_credentials = {
                        'JIRA_KEY': os.environ.get('JIRA_KEY'),
                        'JIRA_USER': os.environ.get('JIRA_USER'),
                        'JIRA_URL': os.environ.get('JIRA_URL', 'https://jiffy-ai.atlassian.net')
                    }
                    logger.info("Using JIRA credentials from environment variables")

        # Fetch user repository paths and repository configurations here
        # to pass them to the run_agent_execution function
        from models import UserDirectRepositoryPath, RepositoryConfig

        # Convert user_id to UUID if it's a string
        user_uuid = user_id
        if user_id and isinstance(user_id, str):
            try:
                user_uuid = uuid.UUID(user_id)
            except ValueError:
                logger.warning(f"Invalid user ID format: {user_id}")
                user_uuid = None

        # Fetch all relevant user repository paths
        user_repo_paths = {}
        if user_uuid:
            # First try to get paths for this specific user
            user_paths = UserDirectRepositoryPath.query.filter(
                UserDirectRepositoryPath.repository_id.in_(repo_uuids),
                UserDirectRepositoryPath.user_id == user_uuid,
                UserDirectRepositoryPath.is_valid == True,
            ).all()

            # If we don't have paths for this user, get any valid paths
            if not user_paths:
                user_paths = UserDirectRepositoryPath.query.filter(
                    UserDirectRepositoryPath.repository_id.in_(repo_uuids),
                    UserDirectRepositoryPath.is_valid == True,
                ).all()

            # Create a dictionary mapping repository_id to local_path
            for path in user_paths:
                user_repo_paths[str(path.repository_id)] = path.local_path

        # Fetch all relevant repository configurations
        repo_configs = {}
        repo_configs_data = RepositoryConfig.query.filter(
            RepositoryConfig.tenant_id == tenant_uuid,
            RepositoryConfig.repo_name.in_([repo.name for repo in repositories]),
        ).all()

        # Create a dictionary mapping repo_name to config
        for config in repo_configs_data:
            repo_configs[config.repo_name] = {
                "local_path": config.local_path,
                "repo_type": config.repo_type,
            }

        # Start async execution in background thread
        socketio.start_background_task(
            target=run_agent_execution,
            execution_id=execution_id,
            issue_key=issue_key,
            repositories=repositories,
            namespace_id=namespace_id,
            tenant_id=tenant_id,
            user_id=user_id,
            user_repo_paths=user_repo_paths,
            repo_configs=repo_configs,
            jira_credentials=jira_credentials,
        )

        socketio.emit(
            "message",
            {
                "type": "system",
                "content": "Started background task for agent execution",
                "timestamp": datetime.now(timezone.utc).isoformat(),
            },
            namespace="/",
        )

        return jsonify(
            {
                "execution_id": execution_id,
                "namespace_id": namespace_id,
                "status": "started",
            }
        )

    except Exception as e:
        logger.error(f"Error starting agent execution: {str(e)}")
        return jsonify(title="Internal Server Error", status=500, detail=str(e)), 500


def run_agent_execution(
    execution_id,
    issue_key,
    repositories,
    namespace_id,
    tenant_id,
    user_id=None,
    user_repo_paths=None,
    repo_configs=None,
    jira_credentials=None,
):
    """Run agent execution in background thread"""

    def send_message(message, message_type="system"):
        """Helper to send messages to the client"""
        socketio.emit(
            "agent_output",
            {
                "type": message_type,
                "content": message,
                "timestamp": datetime.now(timezone.utc).isoformat(),
            },
            namespace="/",
        )

    try:
        # Greeting message
        send_message("Agent execution started - initializing...")

        # Initialize dictionaries if not provided
        if user_repo_paths is None:
            user_repo_paths = {}
        if repo_configs is None:
            repo_configs = {}

        # Enrich repositories with local paths from provided data
        enriched_repositories = []
        for repo in repositories:
            enriched_repo = {
                "id": str(repo.id),
                "name": repo.name,
                "full_name": repo.full_name,
                "description": repo.description,
                "source_type": (
                    repo.source.source_type if repo.source else "git"
                ),  # Get source_type from relationship
                "local_path": None,
            }

            # Try to get local path from user_repo_paths
            repo_id_str = str(repo.id)
            if repo_id_str in user_repo_paths:
                enriched_repo["local_path"] = user_repo_paths[repo_id_str]
            else:
                # Try to get from repo_configs
                if repo.name in repo_configs:
                    config = repo_configs[repo.name]
                    if config.get("local_path"):
                        enriched_repo["local_path"] = config["local_path"]

                    # If we don't have a source_type from the source relationship,
                    # use the repo_type from repo_configs as a fallback
                    if enriched_repo["source_type"] == "git" and config.get(
                        "repo_type"
                    ):
                        enriched_repo["source_type"] = config["repo_type"]

            enriched_repositories.append(enriched_repo)

        # Execute the main developer agent using direct ADK import
        send_message(f"Starting bug fix for issue {issue_key}")

        send_message("Creating dynamic developer agent with selected repositories...")

        # Use JIRA credentials passed from main thread
        if jira_credentials:
            send_message("Using JIRA credentials from tenant configuration")
        else:
            send_message("No JIRA credentials available - JIRA tools may not work properly", "warning")
        
        developer_agent = create_dynamic_developer_agent(enriched_repositories, jira_credentials)

        # Run the agent with the issue key
        send_message(f"Running developer agent for issue {issue_key}")

        result = ""
        USER_ID = "user_1"
        SESSION_ID = str(uuid.uuid4())
        APP_NAME = "developer_agent"

        # Create a persistent session service that will be used throughout this execution
        session_service = InMemorySessionService()
        # Create session in the session service
        session_service.create_session_sync(
            app_name=APP_NAME, user_id=USER_ID, session_id=SESSION_ID
        )
        os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = (
            "/home/<USER>/pemFiles/vertex_ai_keyfile.json"
        )
        os.environ["JIRA_KEY"] = (
            "ATATT3xFfGF0sb1lqJFqU75oUfyAVbs9xH6DpKsiY8NVN0CxHgzul3-pt4eJMQPPME0T1K8beZRg2gBb_9yEnXQgqKKPxdm0lFx_UBs6DNo9cjxFIUQUXAtwJ1Uf3FwlwmJfL7TC5z-Y9wmMF8U1HWXBANmr4-jmo8PmfiZGjuVEwfQ1g_nm6SA=8380D94E"
        )
        os.environ["JIRA_USER"] = "<EMAIL>"
        os.environ["GOOGLE_GENAI_USE_VERTEXAI"] = "TRUE"
        os.environ["GOOGLE_CLOUD_PROJECT"] = "apex-dev-377304"
        os.environ["GOOGLE_CLOUD_LOCATION"] = "us-central1"

        # Create the runner with our session service
        agent_runner = Runner(
            agent=developer_agent, app_name=APP_NAME, session_service=session_service
        )
        send_message("Agent runner created successfully")

        # Create the content for the agent
        content = genai_types.Content(
            role="user", parts=[genai_types.Part(text=f"Fix bug {issue_key}")]
        )

        send_message("Starting agent execution, waiting for responses...", "system")

        for event in agent_runner.run(
            user_id=USER_ID, session_id=SESSION_ID, new_message=content
        ):
            if (
                event.content
                and hasattr(event.content, "parts")
                and len(event.content.parts) > 0
            ):
                message_text = str(event.content.parts[0].text)

                send_message(message_text, "agent")

            if event.is_final_response():
                result = message_text
                send_message("Received final response from agent", "system")
                break

        send_message("Agent execution completed", "success")
        send_message(f"Result: {result}", "system")
        socketio.emit(
            "agent_completed",
            {
                "type": "system",
                "content": result,
                "timestamp": datetime.now(timezone.utc).isoformat(),
            },
            namespace="/",
        )

    except Exception as e:
        import traceback

        error_traceback = traceback.format_exc()
        logger.error(f"Error during agent execution: {str(e)}")
        logger.error(f"Traceback: {error_traceback}")

        send_message(f"Agent execution failed {str(e)}", "error")

    finally:
        # Send a final message
        try:
            send_message("Agent execution complete - cleaning up resources", "system")
        except Exception as e:
            logger.error(f"Error in cleanup: {str(e)}")

        # Log completion
        print(f"Finished agent execution for namespace {namespace_id}")


@agent_execution_bp.route("/execution-status/<execution_id>", methods=["GET"])
@jwt_required()
def get_execution_status(tenant_id, execution_id):
    """Get the status of an agent execution"""
    jwt_data = get_jwt()
    if jwt_data.get("tenant_id") != str(tenant_id):
        return jsonify(title="Forbidden", status=403, detail="Access forbidden"), 403

    execution = active_executions.get(execution_id)
    if not execution:
        return jsonify(title="Not Found", status=404, detail="Execution not found"), 404

    # Make the execution data serializable
    serializable_execution = socketio_service.make_serializable(execution)

    # Add timestamp for debugging
    serializable_execution["_fetched_at"] = datetime.now(timezone.utc).isoformat()

    return jsonify(serializable_execution)
