"""Debug endpoint to check repository sources and their cached data."""

from flask import Blueprint, jsonify
from flask_jwt_extended import jwt_required, get_jwt
from models import RepositorySource, Repository
from utils import validate_uuid
import logging

logger = logging.getLogger(__name__)
debug_repo_bp = Blueprint('debug_repository', __name__)


@debug_repo_bp.route('/repository-debug', methods=['GET'])
@jwt_required()
def debug_repositories(tenant_id):
    """Debug endpoint to see all repository data."""
    try:
        tenant_id = validate_uuid(tenant_id)
        claims = get_jwt()
        token_tenant_id = validate_uuid(claims.get('tenant_id'))
        
        # Verify tenant access
        if str(tenant_id) != str(token_tenant_id):
            return jsonify({'error': 'Unauthorized tenant access'}), 403
        
        # Get all sources
        sources = RepositorySource.query.filter_by(
            tenant_id=tenant_id,
            is_active=True
        ).all()
        
        # Get all repositories
        repositories = Repository.query.filter_by(
            tenant_id=tenant_id
        ).all()
        
        # Debug info
        debug_info = {
            'sources_count': len(sources),
            'repositories_count': len(repositories),
            'sources': [{
                'id': str(source.id),
                'name': source.name,
                'type': source.source_type,
                'last_sync': source.last_sync_at.isoformat() if source.last_sync_at else None,
                'discovered_count': len(source.discovered_repositories),
                'discovered_repos': [{
                    'id': str(repo.id),
                    'name': repo.name,
                    'external_id': repo.external_id
                } for repo in source.discovered_repositories[:5]]  # First 5 repos
            } for source in sources],
            'standalone_repos': [{
                'id': str(repo.id),
                'name': repo.name,
                'source_id': str(repo.source_id),
                'tenant_id': str(repo.tenant_id)
            } for repo in repositories[:10]]  # First 10 repos
        }
        
        return jsonify(debug_info), 200
        
    except Exception as e:
        logger.error(f"Debug error: {e}")
        return jsonify({'error': str(e)}), 500