"""Fix pull requests blueprint JWT issue."""
import re

with open('pull_requests.py', 'r') as f:
    content = f.read()

# Import get_jwt
content = re.sub(
    r'from flask_jwt_extended import jwt_required, get_jwt_identity',
    'from flask_jwt_extended import jwt_required, get_jwt_identity, get_jwt',
    content
)

# Replace jwt_data = get_jwt_identity() pattern
content = re.sub(
    r'jwt_data = get_jwt_identity\(\)\n        if str\(jwt_data\[\'tenant_id\'\]\) != str\(tenant_id\):',
    'jwt_claims = get_jwt()\n        if str(jwt_claims.get(\'tenant_id\')) != str(tenant_id):',
    content
)

# Replace jwt_data = get_jwt_identity() with jwt_claims = get_jwt()
content = re.sub(
    r'jwt_data = get_jwt_identity\(\)',
    'jwt_claims = get_jwt()',
    content
)

# Replace jwt_data['tenant_id'] with jwt_claims.get('tenant_id')
content = re.sub(
    r'jwt_data\[\'tenant_id\'\]',
    'jwt_claims.get(\'tenant_id\')',
    content
)

# Replace jwt_data['roles'] with jwt_claims.get('roles', [])
content = re.sub(
    r'jwt_data\[\'roles\'\]',
    'jwt_claims.get(\'roles\', [])',
    content
)

# Replace jwt_data.get('user_id') or jwt_data usage
content = re.sub(
    r'user_id = jwt_data\.get\(\'user_id\'\) or jwt_data',
    'user_id = get_jwt_identity()',
    content
)

with open('pull_requests.py', 'w') as f:
    f.write(content)