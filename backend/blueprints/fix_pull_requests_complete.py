"""Fix remaining JWT references in pull requests blueprint."""
import re

with open('pull_requests.py', 'r') as f:
    content = f.read()

# Replace remaining jwt_data references with jwt_claims
content = re.sub(r'jwt_data', 'jwt_claims', content)

# Fix user_id references - should use get_jwt_identity()
content = re.sub(
    r"user_id=jwt_claims\['user_id'\]",
    "user_id=get_jwt_identity()",
    content
)

with open('pull_requests.py', 'w') as f:
    f.write(content)