"""
Blueprint for functional area management endpoints.
Handles CRUD operations for functional areas and their relationships.
"""

from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt_identity, get_jwt
from services.functional_area_service import FunctionalAreaService
from models import FunctionalAreaProject, FunctionalAreaRepository
from extensions import db
import uuid

functional_areas_bp = Blueprint('functional_areas', __name__)


def check_tenant_access(tenant_id):
    """Check if the current user has access to the specified tenant."""
    jwt_data = get_jwt()
    
    # Convert tenant_id to UUID object
    try:
        tenant_uuid = tenant_id if isinstance(tenant_id, uuid.UUID) else uuid.UUID(tenant_id)
    except ValueError:
        current_app.logger.error(f"Invalid UUID format for tenant_id: '{tenant_id}'")
        return False
    
    # Verify user has permission for this tenant
    if jwt_data.get('tenant_id') != str(tenant_uuid):
        current_app.logger.warning(f"Unauthorized access attempt to functional areas for tenant '{tenant_id}'")
        return False
    
    return True


@functional_areas_bp.route('/tenants/<tenant_id>/functional-areas', methods=['GET'])
@jwt_required()
def get_functional_areas(tenant_id):
    """Get all functional areas for a tenant."""
    try:
        # Verify tenant access
        if not check_tenant_access(tenant_id):
            return jsonify({'error': 'Access denied to this tenant'}), 403
        
        areas = FunctionalAreaService.get_functional_areas(tenant_id)
        
        return jsonify({
            'functional_areas': [{
                'id': str(area.id),
                'name': area.name,
                'description': area.description,
                'category': area.category,
                'priority': area.priority,
                'is_active': area.is_active,
                'created_at': area.created_at.isoformat() if area.created_at else None,
                'updated_at': area.updated_at.isoformat() if area.updated_at else None,
                'metadata': area.metadata_json,
                'project_count': len(area.project_links),
                'repository_count': len(area.repository_links),
                'projects': [{
                    'project_key': link.project_key,
                    'project_role': link.project_role,
                    'is_primary': link.is_primary
                } for link in area.project_links],
                'repositories': [{
                    'repository_id': str(link.repository_id),
                    'repository_name': link.repository.name,
                    'repository_role': link.repository_role,
                    'is_primary': link.is_primary
                } for link in area.repository_links]
            } for area in areas]
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting functional areas: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500


@functional_areas_bp.route('/tenants/<tenant_id>/functional-areas', methods=['POST'])
@jwt_required()
def create_functional_area(tenant_id):
    """Create a new functional area."""
    try:
        # Verify tenant access
        if not check_tenant_access(tenant_id):
            return jsonify({'error': 'Access denied to this tenant'}), 403
        
        data = request.get_json()
        
        # Validate required fields
        if not data or not data.get('name'):
            return jsonify({'error': 'Name is required'}), 400
        
        # Get current user
        user_id = get_jwt_identity()
        
        # Create functional area
        area = FunctionalAreaService.create_functional_area(tenant_id, user_id, data)
        
        # Link selected projects if provided
        selected_projects = data.get('selectedProjects', [])
        if selected_projects:
            for project in selected_projects:
                # Handle both cases: project object with 'key' field or just project key string
                project_key = project.get('key') if isinstance(project, dict) else project
                FunctionalAreaService.link_project_to_area(
                    tenant_id, 
                    str(area.id), 
                    project_key,
                    user_id
                )
        
        # Link selected repositories if provided
        selected_repositories = data.get('selectedRepositories', [])
        if selected_repositories:
            for repository in selected_repositories:
                # Handle both cases: repository object with 'id' field or just repository id string
                repository_id = repository.get('id') if isinstance(repository, dict) else repository
                FunctionalAreaService.link_repository_to_area(
                    tenant_id, 
                    str(area.id), 
                    repository_id,
                    user_id
                )
        
        return jsonify({
            'functional_area': {
                'id': str(area.id),
                'name': area.name,
                'description': area.description,
                'category': area.category,
                'priority': area.priority,
                'created_at': area.created_at.isoformat() if area.created_at else None,
                'metadata': area.metadata_json
            }
        }), 201
        
    except Exception as e:
        current_app.logger.error(f"Error creating functional area: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500


@functional_areas_bp.route('/tenants/<tenant_id>/functional-areas/<area_id>', methods=['GET'])
@jwt_required()
def get_functional_area(tenant_id, area_id):
    """Get a specific functional area with all its details."""
    try:
        # Verify tenant access
        if not check_tenant_access(tenant_id):
            return jsonify({'error': 'Access denied to this tenant'}), 403
        
        area = FunctionalAreaService.get_functional_area(tenant_id, area_id)
        
        if not area:
            return jsonify({'error': 'Functional area not found'}), 404
        
        return jsonify({
            'functional_area': {
                'id': str(area.id),
                'name': area.name,
                'description': area.description,
                'category': area.category,
                'priority': area.priority,
                'is_active': area.is_active,
                'created_at': area.created_at.isoformat() if area.created_at else None,
                'updated_at': area.updated_at.isoformat() if area.updated_at else None,
                'metadata': area.metadata_json,
                'projects': FunctionalAreaService.get_projects_in_area(tenant_id, area_id),
                'repositories': FunctionalAreaService.get_repositories_in_area(tenant_id, area_id)
            }
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting functional area: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500


@functional_areas_bp.route('/tenants/<tenant_id>/functional-areas/<area_id>', methods=['PUT'])
@jwt_required()
def update_functional_area(tenant_id, area_id):
    """Update a functional area."""
    try:
        # Verify tenant access
        if not check_tenant_access(tenant_id):
            return jsonify({'error': 'Access denied to this tenant'}), 403
        
        data = request.get_json()
        
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        # Get current user for linking operations
        user_id = get_jwt_identity()
        
        area = FunctionalAreaService.update_functional_area(tenant_id, area_id, data)
        
        if not area:
            return jsonify({'error': 'Functional area not found'}), 404
        
        # Handle project links if provided
        if 'selectedProjects' in data:
            # Remove existing project links
            existing_project_links = FunctionalAreaProject.query.filter_by(
                tenant_id=tenant_id,
                functional_area_id=area_id
            ).all()
            for link in existing_project_links:
                db.session.delete(link)
            db.session.commit()
            
            # Add new project links
            selected_projects = data.get('selectedProjects', [])
            for project in selected_projects:
                project_key = project.get('key') if isinstance(project, dict) else project
                FunctionalAreaService.link_project_to_area(
                    tenant_id, 
                    area_id, 
                    project_key,
                    user_id
                )
        
        # Handle repository links if provided
        if 'selectedRepositories' in data:
            # Remove existing repository links
            existing_repo_links = FunctionalAreaRepository.query.filter_by(
                tenant_id=tenant_id,
                functional_area_id=area_id
            ).all()
            for link in existing_repo_links:
                db.session.delete(link)
            db.session.commit()
            
            # Add new repository links
            selected_repositories = data.get('selectedRepositories', [])
            for repository in selected_repositories:
                repository_id = repository.get('id') if isinstance(repository, dict) else repository
                FunctionalAreaService.link_repository_to_area(
                    tenant_id, 
                    area_id, 
                    repository_id,
                    user_id
                )
        
        return jsonify({
            'functional_area': {
                'id': str(area.id),
                'name': area.name,
                'description': area.description,
                'category': area.category,
                'priority': area.priority,
                'updated_at': area.updated_at.isoformat() if area.updated_at else None,
                'metadata': area.metadata_json
            }
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error updating functional area: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500


@functional_areas_bp.route('/tenants/<tenant_id>/functional-areas/<area_id>', methods=['DELETE'])
@jwt_required()
def delete_functional_area(tenant_id, area_id):
    """Delete (deactivate) a functional area."""
    try:
        # Verify tenant access
        if not check_tenant_access(tenant_id):
            return jsonify({'error': 'Access denied to this tenant'}), 403
        
        success = FunctionalAreaService.delete_functional_area(tenant_id, area_id)
        
        if not success:
            return jsonify({'error': 'Functional area not found'}), 404
        
        return jsonify({'message': 'Functional area deleted successfully'}), 200
        
    except Exception as e:
        current_app.logger.error(f"Error deleting functional area: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500


@functional_areas_bp.route('/tenants/<tenant_id>/functional-areas/<area_id>/projects', methods=['POST'])
@jwt_required()
def link_project_to_area(tenant_id, area_id):
    """Link a JIRA project to a functional area."""
    try:
        # Verify tenant access
        if not check_tenant_access(tenant_id):
            return jsonify({'error': 'Access denied to this tenant'}), 403
        
        data = request.get_json()
        
        if not data or not data.get('project_key'):
            return jsonify({'error': 'project_key is required'}), 400
        
        # Get current user
        user_id = get_jwt_identity()
        
        success = FunctionalAreaService.link_project_to_area(
            tenant_id, 
            area_id, 
            data['project_key'],
            user_id,
            data.get('project_role'),
            data.get('is_primary', False)
        )
        
        if not success:
            return jsonify({'error': 'Failed to link project - area not found or project already linked'}), 400
        
        return jsonify({'message': 'Project linked successfully'}), 201
        
    except Exception as e:
        current_app.logger.error(f"Error linking project to area: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500


@functional_areas_bp.route('/tenants/<tenant_id>/functional-areas/<area_id>/projects/<project_key>', methods=['DELETE'])
@jwt_required()
def unlink_project_from_area(tenant_id, area_id, project_key):
    """Unlink a JIRA project from a functional area."""
    try:
        # Verify tenant access
        if not check_tenant_access(tenant_id):
            return jsonify({'error': 'Access denied to this tenant'}), 403
        
        success = FunctionalAreaService.unlink_project_from_area(tenant_id, area_id, project_key)
        
        if not success:
            return jsonify({'error': 'Project link not found'}), 404
        
        return jsonify({'message': 'Project unlinked successfully'}), 200
        
    except Exception as e:
        current_app.logger.error(f"Error unlinking project from area: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500


@functional_areas_bp.route('/tenants/<tenant_id>/functional-areas/<area_id>/repositories', methods=['POST'])
@jwt_required()
def link_repository_to_area(tenant_id, area_id):
    """Link a repository to a functional area."""
    try:
        # Verify tenant access
        if not check_tenant_access(tenant_id):
            return jsonify({'error': 'Access denied to this tenant'}), 403
        
        data = request.get_json()
        
        if not data or not data.get('repository_id'):
            return jsonify({'error': 'repository_id is required'}), 400
        
        # Get current user
        user_id = get_jwt_identity()
        
        success = FunctionalAreaService.link_repository_to_area(
            tenant_id, 
            area_id, 
            data['repository_id'],
            user_id,
            data.get('repository_role'),
            data.get('is_primary', False)
        )
        
        if not success:
            return jsonify({'error': 'Failed to link repository - area/repository not found or already linked'}), 400
        
        return jsonify({'message': 'Repository linked successfully'}), 201
        
    except Exception as e:
        current_app.logger.error(f"Error linking repository to area: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500


@functional_areas_bp.route('/tenants/<tenant_id>/functional-areas/<area_id>/repositories/<repository_id>', methods=['DELETE'])
@jwt_required()
def unlink_repository_from_area(tenant_id, area_id, repository_id):
    """Unlink a repository from a functional area."""
    try:
        # Verify tenant access
        if not check_tenant_access(tenant_id):
            return jsonify({'error': 'Access denied to this tenant'}), 403
        
        success = FunctionalAreaService.unlink_repository_from_area(tenant_id, area_id, repository_id)
        
        if not success:
            return jsonify({'error': 'Repository link not found'}), 404
        
        return jsonify({'message': 'Repository unlinked successfully'}), 200
        
    except Exception as e:
        current_app.logger.error(f"Error unlinking repository from area: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500


@functional_areas_bp.route('/tenants/<tenant_id>/projects/<project_key>/functional-areas', methods=['GET'])
@jwt_required()
def get_functional_areas_for_project(tenant_id, project_key):
    """Get all functional areas that contain a specific project."""
    try:
        # Verify tenant access
        if not check_tenant_access(tenant_id):
            return jsonify({'error': 'Access denied to this tenant'}), 403
        
        areas = FunctionalAreaService.get_functional_areas_for_project(tenant_id, project_key)
        
        return jsonify({
            'functional_areas': [{
                'id': str(area.id),
                'name': area.name,
                'description': area.description,
                'category': area.category,
                'priority': area.priority
            } for area in areas]
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting functional areas for project: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500


@functional_areas_bp.route('/tenants/<tenant_id>/repositories/<repository_id>/functional-areas', methods=['GET'])
@jwt_required()
def get_functional_areas_for_repository(tenant_id, repository_id):
    """Get all functional areas that contain a specific repository."""
    try:
        # Verify tenant access
        if not check_tenant_access(tenant_id):
            return jsonify({'error': 'Access denied to this tenant'}), 403
        
        areas = FunctionalAreaService.get_functional_areas_for_repository(tenant_id, repository_id)
        
        return jsonify({
            'functional_areas': [{
                'id': str(area.id),
                'name': area.name,
                'description': area.description,
                'category': area.category,
                'priority': area.priority
            } for area in areas]
        }), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting functional areas for repository: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500


@functional_areas_bp.route('/tenants/<tenant_id>/functional-areas/summary', methods=['GET'])
@jwt_required()
def get_functional_area_summary(tenant_id):
    """Get summary of functional areas for dashboard."""
    try:
        # Verify tenant access
        if not check_tenant_access(tenant_id):
            return jsonify({'error': 'Access denied to this tenant'}), 403
        
        summary = FunctionalAreaService.get_functional_area_summary(tenant_id)
        
        return jsonify(summary), 200
        
    except Exception as e:
        current_app.logger.error(f"Error getting functional area summary: {str(e)}")
        return jsonify({'error': 'Internal server error'}), 500