import os
from flask import Blueprint, jsonify, request
from flask_jwt_extended import jwt_required, get_jwt
from models import IssueCache, JiraCredential, ProjectCache
from app import db
from utils import get_jira_issues, get_jira_issue_details, decrypt_data

issues_bp = Blueprint('issues_bp', __name__)

# GET /tenants/{tid}/projects/{pkey}/issues?type=Bug&page=...
@issues_bp.route('/projects/<string:project_key>/issues', methods=['GET'])
@jwt_required()
def list_issues(tenant_id, project_key):
    # Tenant isolation check
    jwt_data = get_jwt()
    if jwt_data.get('tenant_id') != str(tenant_id):
        return jsonify(title="Forbidden", status=403, detail="Access to this tenant's issues is forbidden"), 403

    # Process parameters
    page = request.args.get('page', 1, type=int)
    issue_type = request.args.get('type', 'Bug')
    search_query = request.args.get('search', '', type=str)

    if issue_type != 'Bug':
        return jsonify(title="Bad Request", status=400, detail="Currently only 'Bug' issue type is supported"), 400
    
    try:
        # Check if there's a recent cached entry (within 30 seconds)
        import datetime
        import logging
        from sqlalchemy import and_, func
        
        logger = logging.getLogger(__name__)
        logger.debug(f"Fetching issues for project {project_key} in tenant {tenant_id}")
        
        thirty_seconds_ago = datetime.datetime.utcnow() - datetime.timedelta(seconds=30)
        
        project = ProjectCache.query.filter(
            and_(
                ProjectCache.tenant_id == tenant_id,
                ProjectCache.project_key == project_key,
                ProjectCache.fetched_at > thirty_seconds_ago
            )
        ).first()
        
        logger.debug(f"Project cache found: {project is not None}")
        
        # Get JIRA credentials
        credential = JiraCredential.query.filter_by(tenant_id=tenant_id).first_or_404(description="JIRA not configured")
        logger.debug(f"Found JIRA credentials for tenant {tenant_id}")
        
        # If no recent cache, fetch from JIRA
        # For testing with dummy encrypted keys, handle the decryption safely
        try:
            if credential.api_key_encrypted == 'dummy-encrypted-key' and os.environ.get('TESTING') == 'True':
                api_key = 'test-api-key'
            elif ':' in credential.api_key_encrypted:
                # Format: "nonce:ciphertext" (either base64 or hex encoded)
                try:
                    # Safely split the values
                    parts = credential.api_key_encrypted.split(':')
                    if len(parts) != 2:
                        logger.error(f"Invalid encrypted key format: expected 'nonce:ciphertext', got {credential.api_key_encrypted[:10]}...")
                        return jsonify(title="Configuration Error", status=500, 
                                    detail="API key is not properly encrypted. Please update your JIRA credentials."), 500
                    
                    # Try base64 first (which is how jira.py stores it)
                    try:
                        import base64
                        nonce = base64.b64decode(parts[0])
                        ciphertext = base64.b64decode(parts[1])
                        logger.debug("Successfully decoded encrypted key using base64 format")
                    except Exception as base64_error:
                        logger.debug(f"Failed to decode as base64, trying hex format: {str(base64_error)}")
                        # Fall back to hex format
                        try:
                            nonce = bytes.fromhex(parts[0])
                            ciphertext = bytes.fromhex(parts[1])
                            logger.debug("Successfully decoded encrypted key using hex format")
                        except ValueError as hex_error:
                            logger.error(f"Failed to decode as hex format: {str(hex_error)}")
                            return jsonify(title="Configuration Error", status=500, 
                                        detail="API key format is invalid. Please update your JIRA credentials."), 500
                    
                    api_key = decrypt_data(nonce, ciphertext)
                except ValueError as e:
                    logger.error(f"Error decoding encrypted API key: {str(e)}")
                    # Instead of using an insecure fallback, return a proper error to prompt the user to fix their credentials
                    return jsonify(title="Configuration Error", status=500, 
                                detail="Your JIRA API key could not be decrypted. Please update your JIRA credentials in settings."), 500
            else:
                # For development/testing with non-encrypted keys
                api_key = credential.api_key_encrypted
                
            logger.debug(f"Successfully processed API key")
        except Exception as e:
            logger.error(f"Error processing API key: {str(e)}")
            return jsonify(title="Configuration Error", status=500, 
                        detail=f"Error processing JIRA API key: {str(e)}"), 500
        
        # Fetch issues from JIRA API
        logger.debug(f"Fetching issues from JIRA API for project {project_key}, search: {search_query}")
        issues_data = get_jira_issues(credential.base_url, api_key, project_key, 
                                    user_email=credential.email, page=page, search_query=search_query)
        
        # Check if there was an error from JIRA
        if 'error' in issues_data:
            error_message = issues_data['error']
            logger.warning(f"Error from JIRA API: {error_message}")
            
            # Handle different types of errors with specific status codes and messages
            if "not found" in error_message.lower() or "does not exist" in error_message.lower():
                status_code = 404
                title = "Not Found"
            elif "permission" in error_message.lower() or "access" in error_message.lower():
                status_code = 403
                title = "Permission Denied"
            elif "authentication" in error_message.lower() or "credentials" in error_message.lower() or "expired" in error_message.lower():
                status_code = 401
                title = "Authentication Error"
            else:
                status_code = 400
                title = "JIRA API Error"
            
            return jsonify({
                'title': title,
                'status': status_code,
                'detail': error_message,
                'issues': [],
                'total': 0,
                'page': page
            }), status_code
        
        # Add project info from cache if available
        if project:
            issues_data['project'] = {
                'key': project_key,
                'name': project.json_data.get('name', project_key)
            }
        else:
            issues_data['project'] = {
                'key': project_key,
                'name': project_key  # Fallback if no project info available
            }
            
        logger.debug(f"Successfully fetched {len(issues_data.get('issues', []))} issues for project {project_key}")
        return jsonify(issues_data)
    except Exception as e:
        import traceback
        logging.error(f"Error in list_issues: {str(e)}")
        logging.error(traceback.format_exc())
        return jsonify(title="Internal Server Error", status=500, 
                      detail=f"Error fetching issues: {str(e)}"), 500

# GET /tenants/{tid}/issues/{ikey}
@issues_bp.route('/issues/<string:issue_key>', methods=['GET'])
@jwt_required()
def get_issue_details(tenant_id, issue_key):
    # Tenant isolation check
    jwt_data = get_jwt()
    if jwt_data.get('tenant_id') != str(tenant_id):
        return jsonify(title="Forbidden", status=403, detail="Access to this tenant's issue is forbidden"), 403

    try:
        # Check if there's a recent cached entry (within 30 seconds)
        import datetime
        import logging
        from sqlalchemy import and_
        
        logger = logging.getLogger(__name__)
        logger.debug(f"Fetching details for issue {issue_key} in tenant {tenant_id}")
        
        thirty_seconds_ago = datetime.datetime.utcnow() - datetime.timedelta(seconds=30)
        
        cached_issue = IssueCache.query.filter(
            and_(
                IssueCache.tenant_id == tenant_id,
                IssueCache.issue_key == issue_key,
                IssueCache.fetched_at > thirty_seconds_ago
            )
        ).first()
        
        if cached_issue:
            logger.debug(f"Using cached issue data for {issue_key}")
            return jsonify(cached_issue.json_data)
        
        # No cache, fetch from JIRA (in this case, we'll use mock data)
        credential = JiraCredential.query.filter_by(tenant_id=tenant_id).first_or_404(description="JIRA not configured")
        logger.debug(f"Found JIRA credentials for tenant {tenant_id}")
        
        # Handle decryption safely
        try:
            if credential.api_key_encrypted == 'dummy-encrypted-key' and os.environ.get('TESTING') == 'True':
                api_key = 'test-api-key'
            elif ':' in credential.api_key_encrypted:
                # Format: "nonce:ciphertext" (either base64 or hex encoded)
                try:
                    # Safely split the values
                    parts = credential.api_key_encrypted.split(':')
                    if len(parts) != 2:
                        logger.error(f"Invalid encrypted key format: expected 'nonce:ciphertext', got {credential.api_key_encrypted[:10]}...")
                        return jsonify(title="Configuration Error", status=500, 
                                    detail="API key is not properly encrypted. Please update your JIRA credentials."), 500
                    
                    # Try base64 first (which is how jira.py stores it)
                    try:
                        import base64
                        nonce = base64.b64decode(parts[0])
                        ciphertext = base64.b64decode(parts[1])
                        logger.debug("Successfully decoded encrypted key using base64 format")
                    except Exception as base64_error:
                        logger.debug(f"Failed to decode as base64, trying hex format: {str(base64_error)}")
                        # Fall back to hex format
                        try:
                            nonce = bytes.fromhex(parts[0])
                            ciphertext = bytes.fromhex(parts[1])
                            logger.debug("Successfully decoded encrypted key using hex format")
                        except ValueError as hex_error:
                            logger.error(f"Failed to decode as hex format: {str(hex_error)}")
                            return jsonify(title="Configuration Error", status=500, 
                                        detail="API key format is invalid. Please update your JIRA credentials."), 500
                    
                    api_key = decrypt_data(nonce, ciphertext)
                except ValueError as e:
                    logger.error(f"Error decoding encrypted API key: {str(e)}")
                    # Instead of using an insecure fallback, return a proper error to prompt the user to fix their credentials
                    return jsonify(title="Configuration Error", status=500, 
                                detail="Your JIRA API key could not be decrypted. Please update your JIRA credentials in settings."), 500
            else:
                # For development/testing with non-encrypted keys
                api_key = credential.api_key_encrypted
                
            logger.debug(f"Successfully processed API key")
        except Exception as e:
            logger.error(f"Error processing API key: {str(e)}")
            return jsonify(title="Configuration Error", status=500, 
                        detail=f"Error processing JIRA API key: {str(e)}"), 500
        
        # Get issue details from JIRA API
        logger.debug(f"Fetching issue details from JIRA API for {issue_key}")
        issue_details = get_jira_issue_details(credential.base_url, api_key, issue_key, 
                                            user_email=credential.email)
        
        # Check for error status first
        if issue_details and 'status' in issue_details:
            if issue_details.get('status') == 'not_found':
                logger.warning(f"Issue {issue_key} not found in JIRA")
                return jsonify(title="Not Found", status=404, 
                            detail=issue_details.get('error', f"Issue {issue_key} not found in JIRA")), 404
            elif issue_details.get('status') == 'auth_error':
                logger.error(f"Authentication error for issue {issue_key}")
                return jsonify(title="Authentication Error", status=401, 
                            detail=issue_details.get('error', "JIRA authentication failed. Please update your credentials.")), 401
            elif issue_details.get('status') == 'permission_error':
                logger.error(f"Permission error for issue {issue_key}")
                return jsonify(title="Permission Denied", status=403, 
                            detail=issue_details.get('error', f"You don't have permission to access issue {issue_key}")), 403
            elif issue_details.get('status') == 'api_error':
                logger.error(f"JIRA API error for issue {issue_key}")
                return jsonify(title="JIRA API Error", status=400, 
                            detail=issue_details.get('error', "Error communicating with JIRA API")), 400
            elif issue_details.get('error'):
                logger.error(f"Other error for issue {issue_key}: {issue_details.get('error')}")
                return jsonify(title="Error", status=500, 
                            detail=issue_details.get('error', "An error occurred while retrieving the issue")), 500
        
        # Process valid issue details
        if issue_details and 'status' not in issue_details:
            # Extract project key from issue key (e.g., "ABC-123" -> "ABC")
            project_key = issue_key.split('-')[0] if '-' in issue_key else None
            
            # Cache the result in the database
            try:
                existing_cache = IssueCache.query.filter_by(
                    tenant_id=tenant_id,
                    issue_key=issue_key
                ).first()
                
                if existing_cache:
                    # Update existing cache
                    existing_cache.json_data = issue_details
                    existing_cache.fetched_at = datetime.datetime.utcnow()
                    existing_cache.project_key = project_key
                else:
                    # Create new cache entry
                    new_cache = IssueCache(
                        tenant_id=tenant_id,
                        issue_key=issue_key,
                        project_key=project_key,
                        json_data=issue_details,
                        fetched_at=datetime.datetime.utcnow()
                    )
                    db.session.add(new_cache)
                
                db.session.commit()
                logger.debug(f"Cached issue data for {issue_key}")
                
            except Exception as cache_error:
                logger.error(f"Error caching issue data: {str(cache_error)}")
                # Continue even if caching fails
            
            # Return the successfully fetched and cached issue details
            logger.debug(f"Successfully fetched details for issue {issue_key}")
            return jsonify(issue_details)
        else:
            # This is a fallback for cases where no error status and no valid details
            logger.error(f"No issue details returned from JIRA API for {issue_key}")
            return jsonify(title="Not Found", status=404, 
                        detail=f"Issue {issue_key} not found in JIRA"), 404
    except Exception as e:
        import traceback
        logging.error(f"Error in get_issue_details: {str(e)}")
        logging.error(traceback.format_exc())
        return jsonify(title="Internal Server Error", status=500, 
                      detail=f"Error fetching issue details: {str(e)}"), 500 