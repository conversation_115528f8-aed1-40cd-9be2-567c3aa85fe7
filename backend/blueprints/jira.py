from flask import Blueprint, request, jsonify, current_app
from flask_jwt_extended import jwt_required, get_jwt, get_jwt_identity
from app import db
from models import JiraCredential, User
from utils import encrypt_data, decrypt_data, verify_jira_credentials
import uuid
import base64

jira_bp = Blueprint('jira_bp', __name__)

# POST /tenants/{tid}/jira/credentials
@jira_bp.route('/credentials', methods=['POST'])
@jwt_required()
def manage_jira_credentials(tenant_id):
    """Create or update JIRA credentials for a tenant.
    
    Requires either admin role or ownership of the tenant to update credentials.
    Credentials are validated against JIRA API before being stored.
    API keys are encrypted using AES-256-GCM before storage in the database.
    """
    current_app.logger.debug(f"[JIRA] Received credential management request for tenant '{tenant_id}'")
    
    # Check for proper Authorization header
    auth_header = request.headers.get('Authorization', '')
    if not auth_header:
        current_app.logger.error("[JIRA] No Authorization header provided")
        return jsonify(title="Unauthorized", status=401, detail="Missing Authorization header"), 401
    
    if not auth_header.startswith('Bearer '):
        current_app.logger.error(f"[JIRA] Invalid Authorization header format: {auth_header[:10]}...")
        return jsonify(title="Unauthorized", status=401, detail="Invalid Authorization header format"), 401
    
    try:
        jwt_data = get_jwt()
        current_user_id = get_jwt_identity()
        
        # Log JWT information (without exposing sensitive data)
        current_app.logger.debug(f"[JIRA] JWT user ID: {current_user_id}")
        current_app.logger.debug(f"[JIRA] JWT tenant ID: {jwt_data.get('tenant_id')}")
        current_app.logger.debug(f"[JIRA] JWT roles: {jwt_data.get('roles', [])}")
        
        # Convert tenant_id to UUID object, check if it's already a UUID object first
        try:
            tenant_uuid = tenant_id if isinstance(tenant_id, uuid.UUID) else uuid.UUID(tenant_id)
            current_app.logger.debug(f"[JIRA] Parsed tenant_id as UUID: {tenant_uuid}")
        except ValueError:
            current_app.logger.error(f"[JIRA] Invalid UUID format for tenant_id: '{tenant_id}'")
            return jsonify(title="Bad Request", status=400, detail="Invalid tenant ID format."), 400
        
        # Verify user has permission for this tenant
        is_admin = 'admin' in jwt_data.get('roles', [])
        tenant_matches = jwt_data.get('tenant_id') == str(tenant_uuid)
        
        current_app.logger.debug(f"[JIRA] User is admin: {is_admin}")
        current_app.logger.debug(f"[JIRA] Tenant match: {tenant_matches}")
        
        if not (is_admin or tenant_matches):
            current_app.logger.warning(f"[JIRA] Unauthorized access attempt to manage JIRA credentials for tenant '{tenant_id}'")
            return jsonify(title="Forbidden", status=403, detail="Admin access required or must be your tenant."), 403
    
    except Exception as e:
        current_app.logger.error(f"[JIRA] Authentication error: {str(e)}")
        current_app.logger.debug("[JIRA] JWT verification failed", exc_info=True)
        return jsonify(title="Unauthorized", status=401, detail="Authentication failed. Please login again."), 401
    
    # Get request data
    current_app.logger.debug(f"[JIRA] Processing credential management request for tenant {tenant_id}")
    data = request.get_json()
    
    if not data:
        current_app.logger.warning(f"[JIRA] No data provided in request")
        return jsonify(title="Bad Request", status=400, detail="No data provided."), 400
    
    # Log request data structure (without logging the actual API key)
    debug_data = {k: '***REDACTED***' if k == 'api_key' else v for k, v in data.items()}
    current_app.logger.debug(f"[JIRA] Request data structure: {debug_data}")
    
    base_url = data.get('base_url')
    api_key = data.get('api_key')
    email = data.get('email')  # Get email from request data
    
    # Debug fields
    current_app.logger.debug(f"[JIRA] base_url provided: {bool(base_url)}")
    current_app.logger.debug(f"[JIRA] api_key provided: {bool(api_key)}")
    current_app.logger.debug(f"[JIRA] email provided: {bool(email)}")
    
    if not base_url or not api_key:
        missing_fields = []
        if not base_url:
            missing_fields.append("base_url")
        if not api_key:
            missing_fields.append("api_key")
        current_app.logger.warning(f"[JIRA] Missing required fields for JIRA credentials: {', '.join(missing_fields)}")
        return jsonify(title="Bad Request", status=400, detail="Base URL and API key are required."), 400
        
    # For Atlassian Cloud, email is required for authentication
    if not email:
        current_app.logger.warning(f"[JIRA] Email is required for Atlassian Cloud authentication")
        return jsonify(title="Bad Request", status=400, detail="Email associated with the API key is required for Atlassian Cloud."), 400
    
    # Normalize base URL (remove trailing slash)
    if base_url.endswith('/'):
        base_url = base_url[:-1]
    
    # Verify JIRA credentials are valid
    current_app.logger.info(f"[JIRA] Validating JIRA credentials for base URL: {base_url}")
    try:
        if not verify_jira_credentials(base_url, api_key, email):
            current_app.logger.warning(f"[JIRA] Invalid JIRA credentials provided for {base_url}")
            return jsonify(title="Bad Request", status=400, detail="Invalid JIRA credentials or URL."), 400
    except Exception as e:
        current_app.logger.error(f"[JIRA] Error validating JIRA credentials: {str(e)}")
        return jsonify(title="Bad Request", status=400, detail=f"Error validating JIRA credentials: {str(e)}"), 400
    
    # Encrypt API key
    try:
        current_app.logger.debug("[JIRA] Starting encryption process for API key")
        
        # Make sure the API key is a valid string
        if not isinstance(api_key, str):
            current_app.logger.debug(f"[JIRA] Converting API key from {type(api_key)} to string")
            api_key = str(api_key)
        
        # Debug API key length (don't log the actual key)
        current_app.logger.debug(f"[JIRA] API key length: {len(api_key)} characters")
            
        current_app.logger.debug("[JIRA] Calling encrypt_data function")
        # Get AES key for encryption
        from utils import _get_aes_key
        try:
            aes_key = _get_aes_key()
            current_app.logger.debug(f"[JIRA] AES key retrieved, length: {len(aes_key)} bytes")
        except Exception as key_error:
            current_app.logger.error(f"[JIRA] Error retrieving AES key: {str(key_error)}")
            raise ValueError(f"AES key error: {str(key_error)}")
        
        # Perform encryption
        nonce, ciphertext = encrypt_data(api_key)
        current_app.logger.debug(f"[JIRA] Encryption successful, nonce length: {len(nonce)}, ciphertext length: {len(ciphertext)}")
        
        # Convert binary to base64 for storage
        current_app.logger.debug("[JIRA] Converting binary data to base64")
        nonce_b64 = base64.b64encode(nonce).decode('utf-8')
        ciphertext_b64 = base64.b64encode(ciphertext).decode('utf-8')
        
        # Check base64 encoded lengths
        current_app.logger.debug(f"[JIRA] Base64 encoded lengths - nonce: {len(nonce_b64)}, ciphertext: {len(ciphertext_b64)}")
        
        # Combine for storage (can be separated later)
        encrypted_data = f"{nonce_b64}:{ciphertext_b64}"
        current_app.logger.debug(f"[JIRA] Combined encrypted data length: {len(encrypted_data)}")
        
        current_app.logger.info(f"[JIRA] Successfully encrypted API key")
    except ValueError as e:
        # Handle specific encryption key issues
        current_app.logger.error(f"[JIRA] Error with encryption key: {str(e)}")
        current_app.logger.debug(f"[JIRA] Encryption error traceback", exc_info=True)
        return jsonify(
            title="Server Error", 
            status=500, 
            detail="Server configuration error: encryption key issue. Please contact admin."
        ), 500
    except Exception as e:
        current_app.logger.error(f"[JIRA] Error encrypting API key: {str(e)}")
        current_app.logger.debug(f"[JIRA] Encryption error traceback", exc_info=True)
        return jsonify(
            title="Server Error", 
            status=500, 
            detail="Error encrypting API key. Please try again later."
        ), 500
    
    try:
        current_app.logger.debug(f"[JIRA] Starting database operations for saving credentials")
        
        # Get user ID as UUID
        current_app.logger.debug(f"[JIRA] Converting user ID to UUID: {current_user_id}")
        user_uuid = uuid.UUID(current_user_id)
        current_app.logger.debug(f"[JIRA] User UUID: {user_uuid}")
        
        # Store or update credential
        current_app.logger.debug(f"[JIRA] Checking if credentials already exist for tenant {tenant_uuid}")
        credential = JiraCredential.query.filter_by(tenant_id=tenant_uuid).first()
        
        if credential:
            current_app.logger.debug(f"[JIRA] Updating existing credentials (ID: {credential.id})")
            current_app.logger.debug(f"[JIRA] Previous base_url: {credential.base_url}")
            current_app.logger.debug(f"[JIRA] Previous user_id: {credential.user_id}")
            
            credential.base_url = base_url
            credential.api_key_encrypted = encrypted_data
            credential.email = email  # Store the email for future API calls
            credential.user_id = user_uuid  # Update to latest user who modified it
            
            current_app.logger.debug(f"[JIRA] Updated values - base_url: {base_url}, email: {email}, user_id: {user_uuid}")
            current_app.logger.info(f"[JIRA] Updated credentials for tenant {tenant_id}")
        else:
            current_app.logger.debug(f"[JIRA] Creating new credentials record")
            credential = JiraCredential(
                tenant_id=tenant_uuid,
                user_id=user_uuid,
                base_url=base_url,
                api_key_encrypted=encrypted_data,
                email=email  # Store the email for future API calls
            )
            current_app.logger.debug(f"[JIRA] New credential object created with base_url: {base_url}")
            db.session.add(credential)
            current_app.logger.debug(f"[JIRA] Added new credential to session")
            current_app.logger.info(f"[JIRA] Created new credentials for tenant {tenant_id}")
        
        current_app.logger.debug(f"[JIRA] Committing changes to database")
        db.session.commit()
        current_app.logger.debug(f"[JIRA] Database commit successful")
        
        current_app.logger.info(f"[JIRA] Credentials saved successfully for tenant {tenant_id}")
        return jsonify(
            success=True, 
            message="JIRA credentials saved successfully",
            base_url=base_url,
            tenant_id=str(tenant_uuid)
        ), 201
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"[JIRA] Database error when saving credentials: {str(e)}")
        current_app.logger.debug(f"[JIRA] Database error traceback", exc_info=True)
        return jsonify(title="Server Error", status=500, detail=f"Error saving credentials: {str(e)}"), 500

# GET /tenants/{tid}/jira/credentials
@jira_bp.route('/credentials', methods=['GET'])
@jwt_required()
def get_jira_credential_status(tenant_id):
    """Get JIRA credential status for a tenant.
    
    Returns whether credentials are configured and basic information,
    but never returns the actual API key.
    """
    jwt_data = get_jwt()
    
    # Convert tenant_id to UUID object, check if it's already a UUID object first
    try:
        tenant_uuid = tenant_id if isinstance(tenant_id, uuid.UUID) else uuid.UUID(tenant_id)
    except ValueError:
        current_app.logger.error(f"[JIRA] Invalid UUID format for tenant_id: '{tenant_id}'")
        return jsonify(title="Bad Request", status=400, detail="Invalid tenant ID format."), 400
    
    # Verify user has permission for this tenant
    is_admin = 'admin' in jwt_data.get('roles', [])
    tenant_matches = jwt_data.get('tenant_id') == str(tenant_uuid)
    
    if not (is_admin or tenant_matches):
        current_app.logger.warning(f"[JIRA] Unauthorized access attempt to view JIRA credentials for tenant '{tenant_id}'")
        return jsonify(title="Forbidden", status=403, detail="Admin access required or must be your tenant."), 403
    
    try:
        credential = JiraCredential.query.filter_by(tenant_id=tenant_uuid).first()
        
        if not credential:
            return jsonify(
                configured=False,
                message="JIRA credentials not configured for this tenant"
            ), 200
        
        # Get the user who configured the credentials
        user = User.query.get(credential.user_id) if credential.user_id else None
        
        # Verify credential format - don't attempt to decrypt but validate it looks right
        encrypted_data = credential.api_key_encrypted
        has_valid_format = ':' in encrypted_data and len(encrypted_data.split(':')) == 2
        
        # Potential extra validation: try to base64 decode the parts
        if has_valid_format:
            try:
                nonce_b64, ciphertext_b64 = encrypted_data.split(':')
                base64.b64decode(nonce_b64)
                base64.b64decode(ciphertext_b64)
            except Exception:
                has_valid_format = False
                current_app.logger.warning(f"[JIRA] Stored credential has invalid format for tenant '{tenant_id}'")
        
        return jsonify(
            configured=True,
            valid_format=has_valid_format,
            base_url=credential.base_url,
            last_updated=credential.updated_at.isoformat() if credential.updated_at else None,
            configured_by=user.email if user else None
        ), 200
    except Exception as e:
        current_app.logger.error(f"[JIRA] Error fetching credential status: {str(e)}")
        return jsonify(title="Server Error", status=500, detail="Error fetching credential status."), 500

# DELETE /tenants/{tid}/jira/credentials
@jira_bp.route('/credentials', methods=['DELETE'])
@jwt_required()
def delete_jira_credentials(tenant_id):
    """Delete JIRA credentials for a tenant.
    
    Requires admin role or ownership of the tenant.
    """
    jwt_data = get_jwt()
    
    # Convert tenant_id to UUID object, check if it's already a UUID object first
    try:
        tenant_uuid = tenant_id if isinstance(tenant_id, uuid.UUID) else uuid.UUID(tenant_id)
    except ValueError:
        current_app.logger.error(f"[JIRA] Invalid UUID format for tenant_id: '{tenant_id}'")
        return jsonify(title="Bad Request", status=400, detail="Invalid tenant ID format."), 400
    
    # Verify user has permission for this tenant
    is_admin = 'admin' in jwt_data.get('roles', [])
    tenant_matches = jwt_data.get('tenant_id') == str(tenant_uuid)
    
    if not (is_admin or tenant_matches):
        current_app.logger.warning(f"[JIRA] Unauthorized deletion attempt for JIRA credentials of tenant '{tenant_id}'")
        return jsonify(title="Forbidden", status=403, detail="Admin access required or must be your tenant."), 403
    
    try:
        # Find and delete credential
        credential = JiraCredential.query.filter_by(tenant_id=tenant_uuid).first()
        
        if not credential:
            return jsonify(message="No credentials found to delete"), 404
        
        db.session.delete(credential)
        db.session.commit()
        
        current_app.logger.info(f"[JIRA] Deleted credentials for tenant {tenant_id}")
        return jsonify(success=True, message="JIRA credentials deleted successfully"), 200
    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"[JIRA] Error deleting credentials: {str(e)}")
        return jsonify(title="Server Error", status=500, detail=f"Error deleting credentials: {str(e)}"), 500 