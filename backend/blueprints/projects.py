from flask import Blueprint, jsonify, request, current_app
from flask_jwt_extended import jwt_required, get_jwt
from models import ProjectCache, JiraCredential
from app import db
from utils import get_jira_projects
from utils import decrypt_data
import uuid
from datetime import datetime, timedelta

projects_bp = Blueprint('projects_bp', __name__)

# GET /tenants/{tid}/projects
@projects_bp.route('', methods=['GET'])
@jwt_required()
def list_projects(tenant_id):
    jwt_data = get_jwt()
    
    # Convert tenant_id to UUID object
    try:
        tenant_uuid = tenant_id if isinstance(tenant_id, uuid.UUID) else uuid.UUID(tenant_id)
    except ValueError:
        current_app.logger.error(f"Invalid UUID format for tenant_id: '{tenant_id}'")
        return jsonify(title="Bad Request", status=400, detail="Invalid tenant ID format."), 400
    
    # Verify user has permission for this tenant
    if jwt_data.get('tenant_id') != str(tenant_uuid):
        current_app.logger.warning(f"Unauthorized access attempt to projects for tenant '{tenant_id}'")
        return jsonify(title="Forbidden", status=403, detail="Access to this tenant's projects is forbidden"), 403

    try:
        # Check if we have credentials for this tenant
        credential = JiraCredential.query.filter_by(tenant_id=tenant_uuid).first()
        if not credential:
            current_app.logger.warning(f"No JIRA credentials found for tenant '{tenant_id}'")
            return jsonify(
                projects=[],
                configured=False,
                message="JIRA not configured for this tenant"
            ), 200
        
        # Check if we have a valid cache
        cache_ttl = 30  # seconds
        cache_valid_after = datetime.utcnow() - timedelta(seconds=cache_ttl)
        
        cache_entries = ProjectCache.query.filter(
            ProjectCache.tenant_id == tenant_uuid,
            ProjectCache.fetched_at >= cache_valid_after
        ).all()
        
        if cache_entries:
            current_app.logger.info(f"Returning {len(cache_entries)} cached projects for tenant '{tenant_id}'")
            projects_data = [entry.json_data for entry in cache_entries]
            return jsonify(
                projects=projects_data,
                configured=True,
                cached=True,
                count=len(projects_data)
            ), 200
        
        # No valid cache, fetch from JIRA
        current_app.logger.info(f"Fetching projects from JIRA for tenant '{tenant_id}'")
        
        # Decrypt the API key
        try:
            encrypted_parts = credential.api_key_encrypted.split(':')
            if len(encrypted_parts) != 2:
                current_app.logger.error(f"Invalid encrypted data format for tenant '{tenant_id}'")
                return jsonify(
                    projects=[],
                    configured=True,
                    error="Invalid credential format"
                ), 500
            
            # Try base64 first (which is how jira.py stores it)
            try:
                import base64
                nonce_b64, ciphertext_b64 = encrypted_parts
                nonce = base64.b64decode(nonce_b64)
                ciphertext = base64.b64decode(ciphertext_b64)
                current_app.logger.debug("Successfully decoded encrypted key using base64 format")
            except Exception as base64_error:
                current_app.logger.debug(f"Failed to decode as base64, trying hex format: {str(base64_error)}")
                # Fall back to hex format
                try:
                    nonce = bytes.fromhex(encrypted_parts[0])
                    ciphertext = bytes.fromhex(encrypted_parts[1])
                    current_app.logger.debug("Successfully decoded encrypted key using hex format")
                except ValueError as hex_error:
                    current_app.logger.error(f"Failed to decode as hex format: {str(hex_error)}")
                    return jsonify(
                        projects=[],
                        configured=True,
                        error="API key format is invalid. Please update your JIRA credentials."
                    ), 500
            
            api_key = decrypt_data(nonce, ciphertext)
        except Exception as e:
            current_app.logger.error(f"Error decrypting API key: {str(e)}")
            return jsonify(
                projects=[],
                configured=True,
                error="Error decrypting credentials"
            ), 500
        
        # Fetch projects from JIRA using the email for authentication
        projects = get_jira_projects(credential.base_url, api_key, credential.email)
        
        # Store in cache
        current_time = datetime.utcnow()
        for project in projects:
            cache_entry = ProjectCache.query.filter_by(
                tenant_id=tenant_uuid,
                project_key=project.get('key')
            ).first()
            
            if cache_entry:
                cache_entry.json_data = project
                cache_entry.fetched_at = current_time
            else:
                cache_entry = ProjectCache(
                    tenant_id=tenant_uuid,
                    project_key=project.get('key'),
                    json_data=project,
                    fetched_at=current_time
                )
                db.session.add(cache_entry)
        
        db.session.commit()
        
        return jsonify(
            projects=projects,
            configured=True,
            cached=False,
            count=len(projects)
        ), 200
    
    except Exception as e:
        current_app.logger.error(f"Error fetching projects: {str(e)}")
        return jsonify(
            title="Server Error", 
            status=500, 
            detail=f"Error fetching projects: {str(e)}"
        ), 500 