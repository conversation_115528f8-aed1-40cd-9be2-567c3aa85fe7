from flask import Blueprint, request, jsonify, current_app
from models import Tenant

public_tenants_bp = Blueprint('public_tenants_bp', __name__)

@public_tenants_bp.route('/resolve', methods=['GET'])
def resolve_tenant_by_name():
    tenant_name = request.args.get('name')
    current_app.logger.info(f"[PublicTenants] Attempting to resolve tenant by name: '{tenant_name}'")

    if not tenant_name:
        current_app.logger.warning("[PublicTenants] Tenant name/slug not provided in query parameters.")
        return jsonify(title="Bad Request", status=400, detail="Tenant name/slug is required as a query parameter."), 400

    tenant = Tenant.query.filter(Tenant.name.ilike(tenant_name)).first() # Case-insensitive match

    if tenant:
        current_app.logger.info(f"[PublicTenants] Tenant '{tenant_name}' found with ID: {tenant.id}")
        return jsonify({
            "id": str(tenant.id),
            "name": tenant.name,
            "plan": tenant.plan 
            # Add any other public details if needed
        }), 200
    else:
        current_app.logger.warning(f"[PublicTenants] Tenant with name/slug '{tenant_name}' not found.")
        return jsonify(title="Not Found", status=404, detail=f"Tenant with name/slug '{tenant_name}' not found."), 404 