import uuid
"""Pull request management API endpoints."""
from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity, get_jwt
import logging

from services.pr_service import PullRequestService
from app import db

logger = logging.getLogger(__name__)

pr_bp = Blueprint('pull_requests', __name__)
pr_service = PullRequestService()


@pr_bp.route('/create', methods=['POST'])
@jwt_required()
def create_pull_request(tenant_id):
    """Create a new pull request."""
    try:
        # Verify tenant context
        jwt_claims = get_jwt()
        if str(jwt_claims.get('tenant_id')) != str(tenant_id):
            return jsonify({'error': 'Tenant context mismatch'}), 403
        
        # Get request data
        data = request.get_json()
        
        # Validate required fields
        required_fields = ['repo_config_id', 'issue_key']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'Missing required field: {field}'}), 400
        
        # Create pull request
        pull_request = pr_service.create_pull_request(
            tenant_id=uuid.UUID(tenant_id) if isinstance(tenant_id, str) else tenant_id,
            user_id=get_jwt_identity(),
            pr_data=data
        )
        
        return jsonify({
            'id': str(pull_request.id),
            'issue_key': pull_request.issue_key,
            'pr_number': pull_request.pr_number,
            'pr_url': pull_request.pr_url,
            'title': pull_request.title,
            'source_branch': pull_request.source_branch,
            'target_branch': pull_request.target_branch,
            'status': pull_request.status,
            'created_at': pull_request.created_at.isoformat()
        }), 201
        
    except ValueError as e:
        return jsonify({'error': str(e)}), 400
    except Exception as e:
        logger.error(f"Failed to create pull request: {str(e)}")
        return jsonify({'error': str(e)}), 500


@pr_bp.route('', methods=['GET'])
@jwt_required()
def list_pull_requests(tenant_id):
    """List pull requests with optional filters."""
    try:
        # Verify tenant context
        jwt_claims = get_jwt()
        if str(jwt_claims.get('tenant_id')) != str(tenant_id):
            return jsonify({'error': 'Tenant context mismatch'}), 403
        
        # Get filters from query params
        filters = {}
        if 'repo_config_id' in request.args:
            filters['repo_config_id'] = request.args['repo_config_id']
        if 'issue_key' in request.args:
            filters['issue_key'] = request.args['issue_key']
        if 'status' in request.args:
            filters['status'] = request.args['status']
        if 'created_by' in request.args:
            filters['created_by'] = request.args['created_by']
        
        # Get pull requests
        pull_requests = pr_service.get_pull_requests(tenant_id, filters)
        
        # Serialize response
        result = []
        for pr in pull_requests:
            result.append({
                'id': str(pr.id),
                'repo_config_id': str(pr.repo_config_id),
                'issue_key': pr.issue_key,
                'pr_number': pr.pr_number,
                'pr_url': pr.pr_url,
                'title': pr.title,
                'source_branch': pr.source_branch,
                'target_branch': pr.target_branch,
                'status': pr.status,
                'created_at': pr.created_at.isoformat(),
                'created_by': str(pr.created_by) if pr.created_by else None
            })
        
        return jsonify({'pull_requests': result}), 200
        
    except Exception as e:
        logger.error(f"Failed to list pull requests: {str(e)}")
        return jsonify({'error': str(e)}), 500


@pr_bp.route('/<uuid:pr_id>', methods=['GET'])
@jwt_required()
def get_pull_request(tenant_id, pr_id):
    """Get pull request details."""
    try:
        # Verify tenant context
        jwt_claims = get_jwt()
        if str(jwt_claims.get('tenant_id')) != str(tenant_id):
            return jsonify({'error': 'Tenant context mismatch'}), 403
        
        # Get pull request
        pull_requests = pr_service.get_pull_requests(tenant_id, {'id': str(pr_id)})
        
        if not pull_requests:
            return jsonify({'error': 'Pull request not found'}), 404
        
        pr = pull_requests[0]
        
        return jsonify({
            'id': str(pr.id),
            'repo_config_id': str(pr.repo_config_id),
            'issue_key': pr.issue_key,
            'pr_number': pr.pr_number,
            'pr_url': pr.pr_url,
            'title': pr.title,
            'description': pr.description,
            'source_branch': pr.source_branch,
            'target_branch': pr.target_branch,
            'status': pr.status,
            'pr_metadata': pr.pr_metadata,
            'created_at': pr.created_at.isoformat(),
            'created_by': str(pr.created_by) if pr.created_by else None
        }), 200
        
    except Exception as e:
        logger.error(f"Failed to get pull request: {str(e)}")
        return jsonify({'error': str(e)}), 500


@pr_bp.route('/<uuid:pr_id>/sync', methods=['POST'])
@jwt_required()
def sync_pull_request_status(tenant_id, pr_id):
    """Sync pull request status with platform."""
    try:
        # Verify tenant context
        jwt_claims = get_jwt()
        if str(jwt_claims.get('tenant_id')) != str(tenant_id):
            return jsonify({'error': 'Tenant context mismatch'}), 403
        
        # Sync PR status
        pull_request = pr_service.sync_pull_request_status(
            tenant_id=uuid.UUID(tenant_id) if isinstance(tenant_id, str) else tenant_id,
            pr_id=str(pr_id)
        )
        
        return jsonify({
            'id': str(pull_request.id),
            'pr_number': pull_request.pr_number,
            'status': pull_request.status,
            'pr_metadata': pull_request.pr_metadata
        }), 200
        
    except ValueError as e:
        return jsonify({'error': str(e)}), 400
    except Exception as e:
        logger.error(f"Failed to sync pull request status: {str(e)}")
        return jsonify({'error': str(e)}), 500


# Issue-specific endpoints

@pr_bp.route('/issues/<string:issue_key>/pull-requests', methods=['GET'])
@jwt_required()
def get_issue_pull_requests(tenant_id, issue_key):
    """Get all pull requests for a specific issue."""
    try:
        # Verify tenant context
        jwt_claims = get_jwt()
        if str(jwt_claims.get('tenant_id')) != str(tenant_id):
            return jsonify({'error': 'Tenant context mismatch'}), 403
        
        # Get pull requests for issue
        pull_requests = pr_service.get_pull_requests(tenant_id, {'issue_key': issue_key})
        
        # Serialize response
        result = []
        for pr in pull_requests:
            result.append({
                'id': str(pr.id),
                'repo_config_id': str(pr.repo_config_id),
                'pr_number': pr.pr_number,
                'pr_url': pr.pr_url,
                'title': pr.title,
                'status': pr.status,
                'created_at': pr.created_at.isoformat()
            })
        
        return jsonify({'pull_requests': result}), 200
        
    except Exception as e:
        logger.error(f"Failed to get issue pull requests: {str(e)}")
        return jsonify({'error': str(e)}), 500


@pr_bp.route('/issues/<string:issue_key>/create-pr', methods=['POST'])
@jwt_required()
def create_issue_pull_request(tenant_id, issue_key):
    """Create a pull request for a specific issue."""
    try:
        # Verify tenant context
        jwt_claims = get_jwt()
        if str(jwt_claims.get('tenant_id')) != str(tenant_id):
            return jsonify({'error': 'Tenant context mismatch'}), 403
        
        # Get request data
        data = request.get_json()
        data['issue_key'] = issue_key  # Ensure issue key matches URL
        
        # Validate required fields
        if 'repo_config_id' not in data:
            return jsonify({'error': 'Missing required field: repo_config_id'}), 400
        
        # Create pull request
        pull_request = pr_service.create_pull_request(
            tenant_id=uuid.UUID(tenant_id) if isinstance(tenant_id, str) else tenant_id,
            user_id=get_jwt_identity(),
            pr_data=data
        )
        
        return jsonify({
            'id': str(pull_request.id),
            'issue_key': pull_request.issue_key,
            'pr_number': pull_request.pr_number,
            'pr_url': pull_request.pr_url,
            'title': pull_request.title,
            'source_branch': pull_request.source_branch,
            'target_branch': pull_request.target_branch,
            'status': pull_request.status,
            'created_at': pull_request.created_at.isoformat()
        }), 201
        
    except ValueError as e:
        return jsonify({'error': str(e)}), 400
    except Exception as e:
        logger.error(f"Failed to create issue pull request: {str(e)}")
        return jsonify({'error': str(e)}), 500