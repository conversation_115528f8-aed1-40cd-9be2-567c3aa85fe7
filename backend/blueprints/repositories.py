import uuid
"""Repository management API endpoints."""
from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity, get_jwt
import logging

from models import User
from services.repository_service import RepositoryService
from services.pr_service import PullRequestService
from app import db

logger = logging.getLogger(__name__)

repos_bp = Blueprint('repositories', __name__)
repo_service = RepositoryService()
pr_service = PullRequestService()


@repos_bp.route('', methods=['GET'])
@jwt_required()
def list_repositories(tenant_id):
    """List all repositories for a tenant."""
    try:
        # Verify tenant context
        jwt_claims = get_jwt()
        if str(jwt_claims.get('tenant_id')) != str(tenant_id):
            return jsonify({'error': 'Tenant context mismatch'}), 403
        
        # Get repositories
        repositories = repo_service.get_repositories(tenant_id)
        
        # Serialize response
        result = []
        for repo in repositories:
            result.append({
                'id': str(repo.id),
                'repo_name': repo.repo_name,
                'repo_type': repo.repo_type,
                'remote_url': repo.remote_url,
                'default_branch': repo.default_branch,
                'is_active': repo.is_active,
                'created_at': repo.created_at.isoformat(),
                'updated_at': repo.updated_at.isoformat()
            })
        
        return jsonify({'repositories': result}), 200
        
    except Exception as e:
        logger.error(f"Failed to list repositories: {str(e)}")
        return jsonify({'error': str(e)}), 500


@repos_bp.route('', methods=['POST'])
@jwt_required()
def add_repository(tenant_id):
    """Add a new repository configuration."""
    try:
        # Verify tenant context and admin role
        jwt_claims = get_jwt()
        if str(jwt_claims.get('tenant_id')) != str(tenant_id):
            return jsonify({'error': 'Tenant context mismatch'}), 403
        
        # Check if user has admin role
        if 'admin' not in jwt_claims.get('roles', []) and 'tenant_admin' not in jwt_claims.get('roles', []):
            return jsonify({'error': 'Admin role required'}), 403
        
        # Get request data
        data = request.get_json()
        
        # Validate required fields
        required_fields = ['repo_name', 'repo_type', 'remote_url']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'Missing required field: {field}'}), 400
        
        # Validate credentials based on repo type
        if data['repo_type'] == 'github':
            if 'token' not in data:
                return jsonify({'error': 'GitHub token is required'}), 400
        elif data['repo_type'] == 'bitbucket':
            if 'username' not in data or 'app_password' not in data:
                return jsonify({'error': 'Bitbucket username and app password are required'}), 400
        
        # Add repository
        repo_config = repo_service.add_repository(
            tenant_id=uuid.UUID(tenant_id) if isinstance(tenant_id, str) else tenant_id,
            user_id=get_jwt_identity(),
            repo_data=data
        )
        
        return jsonify({
            'id': str(repo_config.id),
            'repo_name': repo_config.repo_name,
            'repo_type': repo_config.repo_type,
            'remote_url': repo_config.remote_url,
            'default_branch': repo_config.default_branch,
            'is_active': repo_config.is_active,
            'created_at': repo_config.created_at.isoformat()
        }), 201
        
    except ValueError as e:
        return jsonify({'error': str(e)}), 400
    except Exception as e:
        logger.error(f"Failed to add repository: {str(e)}")
        return jsonify({'error': str(e)}), 500


@repos_bp.route('/<uuid:repo_id>', methods=['GET'])
@jwt_required()
def get_repository(tenant_id, repo_id):
    """Get repository details."""
    try:
        # Verify tenant context
        jwt_claims = get_jwt()
        if str(jwt_claims.get('tenant_id')) != str(tenant_id):
            return jsonify({'error': 'Tenant context mismatch'}), 403
        
        # Get repository using the service's get_repository method
        repo = repo_service.get_repository(tenant_id, str(repo_id))
        
        if not repo:
            return jsonify({'error': 'Repository not found'}), 404
        
        return jsonify({
            'id': str(repo.id),
            'repo_name': repo.repo_name,
            'repo_type': repo.repo_type,
            'remote_url': repo.remote_url,
            'default_branch': repo.default_branch,
            'is_active': repo.is_active,
            'created_at': repo.created_at.isoformat(),
            'updated_at': repo.updated_at.isoformat()
        }), 200
        
    except Exception as e:
        logger.error(f"Failed to get repository: {str(e)}")
        return jsonify({'error': str(e)}), 500


@repos_bp.route('/<uuid:repo_id>', methods=['PUT'])
@jwt_required()
def update_repository(tenant_id, repo_id):
    """Update repository configuration."""
    try:
        # Verify tenant context and admin role
        jwt_claims = get_jwt()
        if str(jwt_claims.get('tenant_id')) != str(tenant_id):
            return jsonify({'error': 'Tenant context mismatch'}), 403
        
        # Check if user has admin role
        if 'admin' not in jwt_claims.get('roles', []) and 'tenant_admin' not in jwt_claims.get('roles', []):
            return jsonify({'error': 'Admin role required'}), 403
        
        # Get request data
        data = request.get_json()
        
        # Update repository
        repo_config = repo_service.update_repository(
            tenant_id=uuid.UUID(tenant_id) if isinstance(tenant_id, str) else tenant_id,
            repo_id=str(repo_id),
            user_id=get_jwt_identity(),
            update_data=data
        )
        
        return jsonify({
            'id': str(repo_config.id),
            'repo_name': repo_config.repo_name,
            'repo_type': repo_config.repo_type,
            'remote_url': repo_config.remote_url,
            'default_branch': repo_config.default_branch,
            'is_active': repo_config.is_active,
            'updated_at': repo_config.updated_at.isoformat()
        }), 200
        
    except ValueError as e:
        return jsonify({'error': str(e)}), 400
    except Exception as e:
        logger.error(f"Failed to update repository: {str(e)}")
        return jsonify({'error': str(e)}), 500


@repos_bp.route('/<uuid:repo_id>', methods=['DELETE'])
@jwt_required()
def delete_repository(tenant_id, repo_id):
    """Delete (deactivate) repository configuration."""
    try:
        # Verify tenant context and admin role
        jwt_claims = get_jwt()
        if str(jwt_claims.get('tenant_id')) != str(tenant_id):
            return jsonify({'error': 'Tenant context mismatch'}), 403
        
        # Check if user has admin role
        if 'admin' not in jwt_claims.get('roles', []) and 'tenant_admin' not in jwt_claims.get('roles', []):
            return jsonify({'error': 'Admin role required'}), 403
        
        # Delete repository
        success = repo_service.delete_repository(
            tenant_id=uuid.UUID(tenant_id) if isinstance(tenant_id, str) else tenant_id,
            repo_id=str(repo_id),
            user_id=get_jwt_identity()
        )
        
        if success:
            return '', 204
        else:
            return jsonify({'error': 'Failed to delete repository'}), 500
        
    except ValueError as e:
        return jsonify({'error': str(e)}), 400
    except Exception as e:
        logger.error(f"Failed to delete repository: {str(e)}")
        return jsonify({'error': str(e)}), 500


@repos_bp.route('/<uuid:repo_id>/sync', methods=['POST'])
@jwt_required()
def sync_repository(tenant_id, repo_id):
    """Sync repository with remote."""
    try:
        # Verify tenant context
        jwt_claims = get_jwt()
        if str(jwt_claims.get('tenant_id')) != str(tenant_id):
            return jsonify({'error': 'Tenant context mismatch'}), 403
        
        # Sync repository
        result = repo_service.sync_repository(
            tenant_id=uuid.UUID(tenant_id) if isinstance(tenant_id, str) else tenant_id,
            repo_id=str(repo_id),
            user_id=get_jwt_identity()
        )
        
        return jsonify(result), 200
        
    except ValueError as e:
        return jsonify({'error': str(e)}), 400
    except Exception as e:
        logger.error(f"Failed to sync repository: {str(e)}")
        return jsonify({'error': str(e)}), 500


@repos_bp.route('/<uuid:repo_id>/test', methods=['POST'])
@jwt_required()
def test_repository_connection(tenant_id, repo_id):
    """Test repository connection and credentials."""
    try:
        # Verify tenant context
        jwt_claims = get_jwt()
        if str(jwt_claims.get('tenant_id')) != str(tenant_id):
            return jsonify({'error': 'Tenant context mismatch'}), 403
        
        # Get repository
        repos = repo_service.get_repositories(tenant_id, active_only=False)
        repo = next((r for r in repos if r.id == repo_id), None)
        
        if not repo:
            return jsonify({'error': 'Repository not found'}), 404
        
        # Test connection by attempting to sync
        try:
            result = repo_service.sync_repository(
                tenant_id=uuid.UUID(tenant_id) if isinstance(tenant_id, str) else tenant_id,
                repo_id=str(repo_id),
                user_id=get_jwt_identity()
            )
            return jsonify({'status': 'success', 'message': 'Connection successful'}), 200
        except Exception as e:
            return jsonify({'status': 'failed', 'message': str(e)}), 400
        
    except Exception as e:
        logger.error(f"Failed to test repository connection: {str(e)}")
        return jsonify({'error': str(e)}), 500


@repos_bp.route('/<uuid:repo_id>/activity', methods=['GET'])
@jwt_required()
def get_repository_activity(tenant_id, repo_id):
    """Get repository activity logs."""
    try:
        # Verify tenant context
        jwt_claims = get_jwt()
        if str(jwt_claims.get('tenant_id')) != str(tenant_id):
            return jsonify({'error': 'Tenant context mismatch'}), 403
        
        # Get activity logs
        limit = request.args.get('limit', 50, type=int)
        activities = repo_service.get_repository_activity(
            tenant_id=uuid.UUID(tenant_id) if isinstance(tenant_id, str) else tenant_id,
            repo_id=str(repo_id),
            limit=limit
        )
        
        # Serialize response
        result = []
        for activity in activities:
            result.append({
                'id': str(activity.id),
                'activity_type': activity.activity_type,
                'status': activity.status,
                'details': activity.details,
                'created_at': activity.created_at.isoformat(),
                'user_id': str(activity.user_id) if activity.user_id else None
            })
        
        return jsonify({'activities': result}), 200
        
    except Exception as e:
        logger.error(f"Failed to get repository activity: {str(e)}")
        return jsonify({'error': str(e)}), 500