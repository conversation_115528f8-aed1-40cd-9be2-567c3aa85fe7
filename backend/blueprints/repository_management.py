"""API endpoints for repository management and project linking."""

from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt
from services.repository_management_service import RepositoryManagementService
from utils import validate_uuid
from agents.tools import tool_registry
import logging

logger = logging.getLogger(__name__)
repository_management_bp = Blueprint('repository_management', __name__)


@repository_management_bp.route('/repositories', methods=['GET'])
@jwt_required()
def list_repositories(tenant_id):
    """List all repositories for a tenant."""
    try:
        tenant_id = validate_uuid(tenant_id)
        claims = get_jwt()
        token_tenant_id = validate_uuid(claims.get('tenant_id'))
        
        # Verify tenant access
        if str(tenant_id) != str(token_tenant_id):
            return jsonify({'error': 'Unauthorized tenant access'}), 403
        
        repositories = RepositoryManagementService.list_repositories(tenant_id)
        
        return jsonify({
            'repositories': [{
                'id': str(repo.id),
                'name': repo.name,
                'display_name': repo.display_name,
                'repository_type': repo.repository_type,
                'remote_url': repo.remote_url,
                'default_branch': repo.default_branch,
                'description': repo.description,
                'agent_instruction': repo.agent_instruction,
                'agent_description': repo.agent_description,
                'enabled_tools': repo.enabled_tools or [],
                'tool_configs': repo.tool_configs or {},
                'is_active': repo.is_active,
                'created_at': repo.created_at.isoformat(),
                'updated_at': repo.updated_at.isoformat()
            } for repo in repositories]
        }), 200
        
    except Exception as e:
        logger.error(f"Error listing repositories: {e}")
        return jsonify({'error': str(e)}), 500


@repository_management_bp.route('/repositories', methods=['POST'])
@jwt_required()
def create_repository(tenant_id):
    """Create a new repository."""
    try:
        tenant_id = validate_uuid(tenant_id)
        claims = get_jwt()
        user_id = validate_uuid(claims.get('sub'))
        token_tenant_id = validate_uuid(claims.get('tenant_id'))
        roles = claims.get('roles', [])
        
        # Verify tenant access
        if str(tenant_id) != str(token_tenant_id):
            return jsonify({'error': 'Unauthorized tenant access'}), 403
        
        # Check admin role
        if 'admin' not in roles and 'tenant_admin' not in roles:
            return jsonify({'error': 'Admin access required'}), 403
        
        data = request.get_json()
        
        repository = RepositoryManagementService.create_repository(
            tenant_id=tenant_id,
            name=data['name'],
            repository_type=data['repository_type'],
            remote_url=data['remote_url'],
            created_by=user_id,
            display_name=data.get('display_name'),
            default_branch=data.get('default_branch', 'main'),
            description=data.get('description')
        )
        
        return jsonify({
            'id': str(repository.id),
            'name': repository.name,
            'display_name': repository.display_name,
            'repository_type': repository.repository_type,
            'remote_url': repository.remote_url,
            'default_branch': repository.default_branch,
            'description': repository.description,
            'agent_instruction': repository.agent_instruction,
            'agent_description': repository.agent_description,
            'enabled_tools': repository.enabled_tools or [],
            'tool_configs': repository.tool_configs or {},
            'created_at': repository.created_at.isoformat()
        }), 201
        
    except ValueError as e:
        return jsonify({'error': str(e)}), 400
    except Exception as e:
        logger.error(f"Error creating repository: {e}")
        return jsonify({'error': str(e)}), 500


@repository_management_bp.route('/repositories/<repository_id>', methods=['PUT'])
@jwt_required()
def update_repository(tenant_id, repository_id):
    """Update repository details."""
    try:
        tenant_id = validate_uuid(tenant_id)
        repository_id = validate_uuid(repository_id)
        claims = get_jwt()
        token_tenant_id = validate_uuid(claims.get('tenant_id'))
        roles = claims.get('roles', [])
        
        # Verify tenant access
        if str(tenant_id) != str(token_tenant_id):
            return jsonify({'error': 'Unauthorized tenant access'}), 403
        
        # Check admin role
        if 'admin' not in roles and 'tenant_admin' not in roles:
            return jsonify({'error': 'Admin access required'}), 403
        
        data = request.get_json()
        
        repository = RepositoryManagementService.update_repository(
            repository_id=repository_id,
            tenant_id=tenant_id,
            **data
        )
        
        return jsonify({
            'id': str(repository.id),
            'name': repository.name,
            'display_name': repository.display_name,
            'repository_type': repository.repository_type,
            'remote_url': repository.remote_url,
            'default_branch': repository.default_branch,
            'description': repository.description,
            'agent_instruction': repository.agent_instruction,
            'agent_description': repository.agent_description,
            'enabled_tools': repository.enabled_tools or [],
            'tool_configs': repository.tool_configs or {},
            'is_active': repository.is_active,
            'updated_at': repository.updated_at.isoformat()
        }), 200
        
    except ValueError as e:
        return jsonify({'error': str(e)}), 404
    except Exception as e:
        logger.error(f"Error updating repository: {e}")
        return jsonify({'error': str(e)}), 500


@repository_management_bp.route('/projects/<project_key>/repositories', methods=['GET'])
@jwt_required()
def get_project_repositories(tenant_id, project_key):
    """Get all repositories linked to a project."""
    try:
        tenant_id = validate_uuid(tenant_id)
        claims = get_jwt()
        token_tenant_id = validate_uuid(claims.get('tenant_id'))
        user_id = validate_uuid(claims.get('sub'))
        
        # Verify tenant access
        if str(tenant_id) != str(token_tenant_id):
            return jsonify({'error': 'Unauthorized tenant access'}), 403
        
        repos_with_paths = RepositoryManagementService.get_project_user_paths(
            tenant_id=tenant_id,
            user_id=user_id,
            project_key=project_key
        )
        
        return jsonify({
            'repositories': [{
                'id': str(item['repository'].id),
                'name': item['repository'].name,
                'display_name': item['repository'].full_name or item['repository'].name,
                'clone_url': item['repository'].clone_url,
                'is_primary': item['is_primary'],
                'local_path': item['local_path']
            } for item in repos_with_paths]
        }), 200
        
    except Exception as e:
        logger.error(f"Error getting project repositories: {e}")
        return jsonify({'error': str(e)}), 500


@repository_management_bp.route('/projects/<project_key>/repositories', methods=['POST'])
@jwt_required()
def link_repository_to_project(tenant_id, project_key):
    """Link a repository to a project."""
    try:
        tenant_id = validate_uuid(tenant_id)
        claims = get_jwt()
        user_id = validate_uuid(claims.get('sub'))
        token_tenant_id = validate_uuid(claims.get('tenant_id'))
        roles = claims.get('roles', [])
        
        # Verify tenant access
        if str(tenant_id) != str(token_tenant_id):
            return jsonify({'error': 'Unauthorized tenant access'}), 403
        
        # Check admin role
        if 'admin' not in roles and 'tenant_admin' not in roles:
            return jsonify({'error': 'Admin access required'}), 403
        
        data = request.get_json()
        repository_id = validate_uuid(data['repository_id'])
        is_primary = data.get('is_primary', False)
        
        link = RepositoryManagementService.link_repository_to_project(
            tenant_id=tenant_id,
            project_key=project_key,
            repository_id=repository_id,
            linked_by=user_id,
            is_primary=is_primary
        )
        
        return jsonify({
            'id': str(link.id),
            'project_key': link.project_key,
            'repository_id': str(link.repository_id),
            'is_primary': link.is_primary,
            'linked_at': link.linked_at.isoformat()
        }), 201
        
    except ValueError as e:
        return jsonify({'error': str(e)}), 400
    except Exception as e:
        logger.error(f"Error linking repository to project: {e}")
        return jsonify({'error': str(e)}), 500


@repository_management_bp.route('/projects/<project_key>/repositories/<repository_id>', methods=['DELETE'])
@jwt_required()
def unlink_repository_from_project(tenant_id, project_key, repository_id):
    """Unlink a repository from a project."""
    try:
        tenant_id = validate_uuid(tenant_id)
        repository_id = validate_uuid(repository_id)
        claims = get_jwt()
        token_tenant_id = validate_uuid(claims.get('tenant_id'))
        roles = claims.get('roles', [])
        
        # Verify tenant access
        if str(tenant_id) != str(token_tenant_id):
            return jsonify({'error': 'Unauthorized tenant access'}), 403
        
        # Check admin role
        if 'admin' not in roles and 'tenant_admin' not in roles:
            return jsonify({'error': 'Admin access required'}), 403
        
        success = RepositoryManagementService.unlink_repository_from_project(
            tenant_id=tenant_id,
            project_key=project_key,
            repository_id=repository_id
        )
        
        if success:
            return jsonify({'message': 'Repository unlinked successfully'}), 200
        else:
            return jsonify({'error': 'Link not found'}), 404
        
    except Exception as e:
        logger.error(f"Error unlinking repository from project: {e}")
        return jsonify({'error': str(e)}), 500


@repository_management_bp.route('/repositories/<repository_id>/local-path', methods=['POST'])
@jwt_required()
def set_repository_local_path(tenant_id, repository_id):
    """Set user's local path for a repository."""
    try:
        tenant_id = validate_uuid(tenant_id)
        repository_id = validate_uuid(repository_id)
        claims = get_jwt()
        user_id = validate_uuid(claims.get('sub'))
        token_tenant_id = validate_uuid(claims.get('tenant_id'))
        
        # Verify tenant access
        if str(tenant_id) != str(token_tenant_id):
            return jsonify({'error': 'Unauthorized tenant access'}), 403
        
        data = request.get_json()
        local_path = data.get('local_path')
        
        if not local_path:
            return jsonify({'error': 'local_path is required'}), 400
        
        path_mapping = RepositoryManagementService.set_user_repository_path(
            tenant_id=tenant_id,
            user_id=user_id,
            repository_id=repository_id,
            local_path=local_path
        )
        
        return jsonify({
            'id': str(path_mapping.id),
            'repository_id': str(path_mapping.repository_id),
            'local_path': path_mapping.local_path,
            'created_at': path_mapping.created_at.isoformat(),
            'updated_at': path_mapping.updated_at.isoformat()
        }), 200
        
    except Exception as e:
        logger.error(f"Error setting repository local path: {e}")
        return jsonify({'error': str(e)}), 500


@repository_management_bp.route('/repositories/<repository_id>/local-path', methods=['GET'])
@jwt_required()
def get_repository_local_path(tenant_id, repository_id):
    """Get user's local path for a repository."""
    try:
        tenant_id = validate_uuid(tenant_id)
        repository_id = validate_uuid(repository_id)
        claims = get_jwt()
        user_id = validate_uuid(claims.get('sub'))
        token_tenant_id = validate_uuid(claims.get('tenant_id'))
        
        # Verify tenant access
        if str(tenant_id) != str(token_tenant_id):
            return jsonify({'error': 'Unauthorized tenant access'}), 403
        
        local_path = RepositoryManagementService.get_user_repository_path(
            tenant_id=tenant_id,
            user_id=user_id,
            repository_id=repository_id
        )
        
        if local_path is None:
            return jsonify({'error': 'Path not found'}), 404
        
        return jsonify({
            'repository_id': str(repository_id),
            'local_path': local_path
        }), 200
        
    except Exception as e:
        logger.error(f"Error getting repository local path: {e}")
        return jsonify({'error': str(e)}), 500


@repository_management_bp.route('/repositories/<repository_id>', methods=['DELETE'])
@jwt_required()
def delete_repository(tenant_id, repository_id):
    """Delete a repository."""
    try:
        tenant_id = validate_uuid(tenant_id)
        repository_id = validate_uuid(repository_id)
        claims = get_jwt()
        token_tenant_id = validate_uuid(claims.get('tenant_id'))
        roles = claims.get('roles', [])
        
        # Verify tenant access
        if str(tenant_id) != str(token_tenant_id):
            return jsonify({'error': 'Unauthorized tenant access'}), 403
        
        # Check admin role
        if 'admin' not in roles and 'tenant_admin' not in roles:
            return jsonify({'error': 'Admin access required'}), 403
        
        # Use soft delete by default
        force_delete = request.args.get('force', 'false').lower() == 'true'
        
        if force_delete:
            success = RepositoryManagementService.delete_repository(
                tenant_id=tenant_id,
                repository_id=repository_id
            )
            if success:
                return jsonify({'message': 'Repository permanently deleted'}), 200
        else:
            repository = RepositoryManagementService.soft_delete_repository(
                tenant_id=tenant_id,
                repository_id=repository_id
            )
            return jsonify({
                'message': 'Repository deactivated',
                'repository': {
                    'id': str(repository.id),
                    'name': repository.name,
                    'is_active': repository.is_active
                }
            }), 200
        
    except ValueError as e:
        return jsonify({'error': str(e)}), 404
    except Exception as e:
        logger.error(f"Error deleting repository: {e}")
        return jsonify({'error': str(e)}), 500


@repository_management_bp.route('/tools', methods=['GET'])
@jwt_required()
def list_available_tools(tenant_id):
    """Get list of available tools and their configurations."""
    try:
        # Any authenticated user can view available tools
        tools = tool_registry.list_all()
        return jsonify({'tools': tools}), 200
    except Exception as e:
        logger.error(f"Error listing tools: {e}")
        return jsonify({'error': str(e)}), 500