"""Repository source management API endpoints."""

import json
import base64
from datetime import datetime, timezone
from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt
from uuid import UUID
import logging

from models import db, RepositorySource
from services.repository_source_service import RepositorySourceService
from utils import validate_uuid, encrypt_data


logger = logging.getLogger(__name__)
repository_source_bp = Blueprint('repository_source_management', __name__)
service = RepositorySourceService()


@repository_source_bp.route('/repository-sources', methods=['GET'])
@jwt_required()
def list_repository_sources(tenant_id):
    """List all repository sources for a tenant (admin only)."""
    try:
        tenant_id = validate_uuid(tenant_id)
        claims = get_jwt()
        token_tenant_id = validate_uuid(claims.get('tenant_id'))
        roles = claims.get('roles', [])
        
        # Verify tenant access
        if str(tenant_id) != str(token_tenant_id):
            return jsonify({'error': 'Unauthorized tenant access'}), 403
        
        # Check admin role
        if 'tenant_admin' not in roles:
            return jsonify({'error': 'Administrator access required'}), 403
        
        sources = service.list_sources(tenant_id)
        
        return jsonify({
            'sources': [{
                'id': str(source.id),
                'name': source.name,
                'source_type': source.source_type,
                'is_active': source.is_active,
                'last_sync_at': source.last_sync_at.isoformat() + 'Z' if source.last_sync_at else None,
                'created_at': source.created_at.isoformat() + 'Z',
                'repository_count': len(source.discovered_repositories)
            } for source in sources]
        }), 200
        
    except Exception as e:
        logger.error(f"Error listing repository sources: {e}")
        return jsonify({'error': str(e)}), 500


@repository_source_bp.route('/repository-sources', methods=['POST'])
@jwt_required()
def create_repository_source(tenant_id):
    """Create a new repository source (admin only)."""
    try:
        tenant_id = validate_uuid(tenant_id)
        claims = get_jwt()
        user_id = validate_uuid(claims.get('sub'))
        token_tenant_id = validate_uuid(claims.get('tenant_id'))
        roles = claims.get('roles', [])
        
        # Verify tenant access
        if str(tenant_id) != str(token_tenant_id):
            return jsonify({'error': 'Unauthorized tenant access'}), 403
        
        # Check admin role
        if 'tenant_admin' not in roles:
            return jsonify({'error': 'Administrator access required'}), 403
        
        data = request.get_json()
        required_fields = ['name', 'source_type', 'auth_config', 'settings']
        
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'Missing required field: {field}'}), 400
        
        # Create the source
        source = service.create_source(
            tenant_id=tenant_id,
            name=data['name'],
            source_type=data['source_type'],
            auth_config=data['auth_config'],
            settings=data['settings'],
            created_by=user_id
        )
        
        return jsonify({
            'id': str(source.id),
            'name': source.name,
            'source_type': source.source_type,
            'created_at': source.created_at.isoformat() + 'Z'
        }), 201
        
    except ValueError as e:
        return jsonify({'error': str(e)}), 400
    except Exception as e:
        logger.error(f"Error creating repository source: {e}")
        return jsonify({'error': str(e)}), 500


@repository_source_bp.route('/repository-sources/<uuid:source_id>', methods=['GET'])
@jwt_required()
def get_repository_source(tenant_id, source_id):
    """Get a specific repository source."""
    try:
        tenant_id = validate_uuid(tenant_id)
        source_id = validate_uuid(source_id)
        claims = get_jwt()
        token_tenant_id = validate_uuid(claims.get('tenant_id'))
        
        # Verify tenant access
        if str(tenant_id) != str(token_tenant_id):
            return jsonify({'error': 'Unauthorized tenant access'}), 403
        
        source = service.get_source(source_id, tenant_id)
        if not source:
            return jsonify({'error': 'Repository source not found'}), 404
        
        return jsonify({
            'id': str(source.id),
            'name': source.name,
            'source_type': source.source_type,
            'is_active': source.is_active,
            'last_sync_at': source.last_sync_at.isoformat() + 'Z' if source.last_sync_at else None,
            'created_at': source.created_at.isoformat() + 'Z',
            'settings': source.settings_json,
            'repository_count': len(source.discovered_repositories)
        }), 200
        
    except Exception as e:
        logger.error(f"Error getting repository source: {e}")
        return jsonify({'error': str(e)}), 500


@repository_source_bp.route('/repository-sources/<uuid:source_id>', methods=['PUT'])
@jwt_required()
def update_repository_source(tenant_id, source_id):
    """Update a repository source (admin only)."""
    try:
        tenant_id = validate_uuid(tenant_id)
        source_id = validate_uuid(source_id)
        claims = get_jwt()
        token_tenant_id = validate_uuid(claims.get('tenant_id'))
        roles = claims.get('roles', [])
        
        # Verify tenant access and admin role
        if str(tenant_id) != str(token_tenant_id):
            return jsonify({'error': 'Unauthorized tenant access'}), 403
        
        if 'tenant_admin' not in roles and 'platform_admin' not in roles:
            return jsonify({'error': 'Admin role required'}), 403
        
        data = request.get_json()
        source = service.get_source(source_id, tenant_id)
        
        if not source:
            return jsonify({'error': 'Repository source not found'}), 404
        
        # Update allowed fields
        if 'name' in data:
            source.name = data['name']
        
        if 'is_active' in data:
            source.is_active = data['is_active']
        
        if 'auth_config' in data:
            # Encrypt new authentication configuration
            nonce, ciphertext = encrypt_data(json.dumps(data['auth_config']))
            source.auth_config_encrypted = base64.b64encode(nonce + ciphertext).decode('utf-8')
        
        if 'settings' in data:
            source.settings_json = data['settings']
        
        source.updated_at = datetime.utcnow()
        db.session.commit()
        
        logger.info(f"Updated repository source {source_id} for tenant {tenant_id}")
        
        return jsonify({
            'id': str(source.id),
            'name': source.name,
            'source_type': source.source_type,
            'is_active': source.is_active,
            'last_sync_at': source.last_sync_at.isoformat() + 'Z' if source.last_sync_at else None,
            'updated_at': source.updated_at.isoformat() + 'Z'
        }), 200
        
    except Exception as e:
        logger.error(f"Error updating repository source: {e}")
        return jsonify({'error': str(e)}), 500


@repository_source_bp.route('/repository-sources/<uuid:source_id>', methods=['DELETE'])
@jwt_required()
def delete_repository_source(tenant_id, source_id):
    """Delete a repository source (admin only)."""
    try:
        tenant_id = validate_uuid(tenant_id)
        source_id = validate_uuid(source_id)
        claims = get_jwt()
        token_tenant_id = validate_uuid(claims.get('tenant_id'))
        roles = claims.get('roles', [])
        
        # Verify tenant access and admin role
        if str(tenant_id) != str(token_tenant_id):
            return jsonify({'error': 'Unauthorized tenant access'}), 403
        
        if 'tenant_admin' not in roles and 'platform_admin' not in roles:
            return jsonify({'error': 'Admin role required'}), 403
        
        source = service.get_source(source_id, tenant_id)
        
        if not source:
            return jsonify({'error': 'Repository source not found'}), 404
        
        # Delete the source and all its repositories (cascade)
        db.session.delete(source)
        db.session.commit()
        
        logger.info(f"Deleted repository source {source_id} for tenant {tenant_id}")
        
        return '', 204
        
    except Exception as e:
        logger.error(f"Error deleting repository source: {e}")
        return jsonify({'error': str(e)}), 500


@repository_source_bp.route('/repository-sources/<uuid:source_id>/sync', methods=['POST'])
@jwt_required()
def sync_repository_source(tenant_id, source_id):
    """Sync repositories from a source (admin only)."""
    try:
        tenant_id = validate_uuid(tenant_id)
        source_id = validate_uuid(source_id)
        claims = get_jwt()
        token_tenant_id = validate_uuid(claims.get('tenant_id'))
        roles = claims.get('roles', [])
        
        # Verify tenant access
        if str(tenant_id) != str(token_tenant_id):
            return jsonify({'error': 'Unauthorized tenant access'}), 403
        
        # Check admin role
        if 'tenant_admin' not in roles:
            return jsonify({'error': 'Administrator access required'}), 403
        
        # Get force parameter from request
        data = request.get_json() or {}
        force = data.get('force', False)
        
        # Sync repositories
        repositories = service.sync_repositories(source_id, tenant_id, force=force)
        
        # Check if cache was used
        source = service.get_source(source_id, tenant_id)
        cache_used = False
        if source.last_sync_at and not force:
            time_since_sync = datetime.utcnow() - source.last_sync_at
            cache_used = time_since_sync.total_seconds() < 3600
        
        return jsonify({
            'synced': True,
            'cache_used': cache_used,
            'last_sync': source.last_sync_at.isoformat() + 'Z' if source.last_sync_at else None,
            'repository_count': len(repositories),
            'repositories': [{
                'id': str(repo.id),
                'name': repo.name,
                'full_name': repo.full_name,
                'external_id': repo.external_id,
                'clone_url': repo.clone_url
            } for repo in repositories]
        }), 200
        
    except ValueError as e:
        return jsonify({'error': str(e)}), 400
    except Exception as e:
        logger.error(f"Error syncing repository source: {e}")
        return jsonify({'error': str(e)}), 500


@repository_source_bp.route('/repositories', methods=['GET'])
@jwt_required()
def list_discovered_repositories(tenant_id):
    """List all discovered repositories for a tenant."""
    try:
        tenant_id = validate_uuid(tenant_id)
        claims = get_jwt()
        token_tenant_id = validate_uuid(claims.get('tenant_id'))
        
        # Verify tenant access
        if str(tenant_id) != str(token_tenant_id):
            return jsonify({'error': 'Unauthorized tenant access'}), 403
        
        # Get all sources for the tenant (with eager loading)
        from sqlalchemy.orm import joinedload
        sources = RepositorySource.query.filter_by(
            tenant_id=tenant_id,
            is_active=True
        ).options(joinedload(RepositorySource.discovered_repositories)).all()
        
        # Collect all repositories
        all_repositories = []
        for source in sources:
            for repo in source.discovered_repositories:
                all_repositories.append({
                    'id': str(repo.id),
                    'name': repo.name,
                    'full_name': repo.full_name,
                    'description': repo.description,
                    'clone_url': repo.clone_url,
                    'source_id': str(repo.source_id),
                    'source_name': source.name,
                    'source_type': source.source_type,
                    'agent_instruction': repo.agent_instruction,
                    'agent_description': repo.agent_description,
                    'enabled_tools': repo.enabled_tools or [],
                    'tool_configs': repo.tool_configs or {},
                    'last_updated': repo.last_updated.isoformat() + 'Z'
                })
        
        return jsonify({'repositories': all_repositories}), 200
        
    except Exception as e:
        logger.error(f"Error listing discovered repositories: {e}")
        return jsonify({'error': str(e)}), 500


@repository_source_bp.route('/repositories/<uuid:repository_id>/agent-config', methods=['PUT'])
@jwt_required()
def update_repository_agent_config(tenant_id, repository_id):
    """Update agent configuration for a repository."""
    try:
        tenant_id = validate_uuid(tenant_id)
        repository_id = validate_uuid(repository_id)
        claims = get_jwt()
        token_tenant_id = validate_uuid(claims.get('tenant_id'))
        roles = claims.get('roles', [])
        
        # Verify tenant access
        if str(tenant_id) != str(token_tenant_id):
            return jsonify({'error': 'Unauthorized tenant access'}), 403
        
        # Check admin role
        if 'tenant_admin' not in roles:
            return jsonify({'error': 'Administrator access required'}), 403
        
        data = request.get_json()
        
        # Get the repository
        from models import Repository
        repository = Repository.query.filter_by(
            id=repository_id,
            tenant_id=tenant_id
        ).first()
        
        if not repository:
            return jsonify({'error': 'Repository not found'}), 404
        
        # Update agent configuration
        if 'agent_instruction' in data:
            repository.agent_instruction = data['agent_instruction']
        if 'agent_description' in data:
            repository.agent_description = data['agent_description']
        if 'enabled_tools' in data:
            repository.enabled_tools = data['enabled_tools']
        if 'tool_configs' in data:
            repository.tool_configs = data['tool_configs']
        
        repository.last_updated = datetime.now(timezone.utc)
        db.session.commit()
        
        return jsonify({
            'id': str(repository.id),
            'name': repository.name,
            'agent_instruction': repository.agent_instruction,
            'agent_description': repository.agent_description,
            'enabled_tools': repository.enabled_tools or [],
            'tool_configs': repository.tool_configs or {},
            'last_updated': repository.last_updated.isoformat() + 'Z'
        }), 200
        
    except Exception as e:
        logger.error(f"Error updating repository agent config: {e}")
        return jsonify({'error': str(e)}), 500


@repository_source_bp.route('/projects/<project_key>/available-repositories', methods=['GET'])
@jwt_required()
def list_available_repositories_for_project(tenant_id, project_key):
    """List repositories that can be linked to a project."""
    try:
        tenant_id = validate_uuid(tenant_id)
        claims = get_jwt()
        token_tenant_id = validate_uuid(claims.get('tenant_id'))
        
        # Verify tenant access
        if str(tenant_id) != str(token_tenant_id):
            return jsonify({'error': 'Unauthorized tenant access'}), 403
        
        # Get all sources for the tenant
        sources = service.list_sources(tenant_id)
        
        # Collect all repositories with linkage status
        available_repositories = []
        for source in sources:
            for repo in source.discovered_repositories:
                # Check if already linked to this project
                is_linked = any(
                    link.project_key == project_key 
                    for link in repo.projects
                )
                
                available_repositories.append({
                    'id': str(repo.id),
                    'name': repo.name,
                    'full_name': repo.full_name,
                    'description': repo.description,
                    'source_type': source.source_type,
                    'source_name': source.name,
                    'is_linked': is_linked
                })
        
        return jsonify({'repositories': available_repositories}), 200
        
    except Exception as e:
        logger.error(f"Error listing available repositories: {e}")
        return jsonify({'error': str(e)}), 500


@repository_source_bp.route('/projects/<project_key>/repositories', methods=['GET'])
@jwt_required()
def get_project_repositories(tenant_id, project_key):
    """Get all repositories linked to a project."""
    try:
        tenant_id = validate_uuid(tenant_id)
        claims = get_jwt()
        token_tenant_id = validate_uuid(claims.get('tenant_id'))
        
        # Verify tenant access
        if str(tenant_id) != str(token_tenant_id):
            return jsonify({'error': 'Unauthorized tenant access'}), 403
        
        # Get linked repositories for the project
        from models import ProjectRepository, Repository
        from sqlalchemy.orm import joinedload
        
        project_repos = ProjectRepository.query.filter_by(
            tenant_id=tenant_id,
            project_key=project_key
        ).options(
            joinedload(ProjectRepository.repository).joinedload(Repository.source)
        ).all()
        
        repositories = []
        for link in project_repos:
            repo = link.repository
            if repo:
                repositories.append({
                    'id': str(repo.id),
                    'name': repo.name,
                    'full_name': repo.full_name,
                    'description': repo.description,
                    'clone_url': repo.clone_url,
                    'source_name': repo.source.name if repo.source else None,
                    'source_type': repo.source.source_type if repo.source else None,
                    'is_primary': link.is_primary,
                    'linked_at': link.linked_at.isoformat() + 'Z'
                })
        
        return jsonify({'repositories': repositories}), 200
        
    except Exception as e:
        logger.error(f"Error getting project repositories: {e}")
        return jsonify({'error': str(e)}), 500


@repository_source_bp.route('/projects/<project_key>/repositories', methods=['POST'])
@jwt_required()
def link_repository_to_project(tenant_id, project_key):
    """Link a repository to a project."""
    try:
        tenant_id = validate_uuid(tenant_id)
        claims = get_jwt()
        user_id = validate_uuid(claims.get('sub'))
        token_tenant_id = validate_uuid(claims.get('tenant_id'))
        
        # Verify tenant access
        if str(tenant_id) != str(token_tenant_id):
            return jsonify({'error': 'Unauthorized tenant access'}), 403
        
        data = request.get_json()
        repository_id = validate_uuid(data.get('repository_id'))
        is_primary = data.get('is_primary', False)
        
        # Create the link
        from models import ProjectRepository, Repository
        
        # Verify repository belongs to tenant
        repository = Repository.query.filter_by(
            id=repository_id,
            tenant_id=tenant_id
        ).first()
        
        if not repository:
            return jsonify({'error': 'Repository not found'}), 404
        
        # Check if already linked
        existing_link = ProjectRepository.query.filter_by(
            tenant_id=tenant_id,
            project_key=project_key,
            repository_id=repository_id
        ).first()
        
        if existing_link:
            return jsonify({'error': 'Repository already linked to project'}), 400
        
        # Create new link
        link = ProjectRepository(
            tenant_id=tenant_id,
            project_key=project_key,
            repository_id=repository_id,
            is_primary=is_primary,
            linked_by=user_id
        )
        
        from app import db
        db.session.add(link)
        db.session.commit()
        
        return jsonify({
            'id': str(link.id),
            'project_key': link.project_key,
            'repository_id': str(link.repository_id),
            'is_primary': link.is_primary,
            'linked_at': link.linked_at.isoformat() + 'Z'
        }), 201
        
    except ValueError as e:
        return jsonify({'error': str(e)}), 400
    except Exception as e:
        logger.error(f"Error linking repository to project: {e}")
        return jsonify({'error': str(e)}), 500


@repository_source_bp.route('/<source_id>', methods=['DELETE'])
@jwt_required()
def delete_source(tenant_id, source_id):
    """Delete a repository source."""
    try:
        tenant_id = validate_uuid(tenant_id)
        source_id = validate_uuid(source_id)
        claims = get_jwt()
        token_tenant_id = validate_uuid(claims.get('tenant_id'))
        roles = claims.get('roles', [])
        
        # Verify tenant access
        if str(tenant_id) != str(token_tenant_id):
            return jsonify({'error': 'Unauthorized tenant access'}), 403
        
        # Check admin role
        if 'admin' not in roles and 'tenant_admin' not in roles:
            return jsonify({'error': 'Admin access required'}), 403
        
        # Delete the source
        service.delete_source(tenant_id, source_id)
        
        return jsonify({'message': 'Repository source deleted successfully'}), 200
        
    except ValueError as e:
        return jsonify({'error': str(e)}), 404
    except Exception as e:
        logger.error(f"Error deleting repository source: {e}")
        return jsonify({'error': str(e)}), 500


@repository_source_bp.route('/tools', methods=['GET'])
@jwt_required()
def list_available_tools(tenant_id):
    """Get list of available tools and their configurations."""
    try:
        # Import tool registry
        from agents.tools import tool_registry
        
        # Any authenticated user can view available tools
        all_tools = tool_registry.list_all()
        
        # Filter out internal-only tools (JIRA Bug Analyzer is used internally by DeveloperAgent)
        user_tools = [tool for tool in all_tools if tool['id'] != 'jira_bug_analyzer']
        
        return jsonify({'tools': user_tools}), 200
    except Exception as e:
        logger.error(f"Error listing tools: {e}")
        return jsonify({'error': str(e)}), 500