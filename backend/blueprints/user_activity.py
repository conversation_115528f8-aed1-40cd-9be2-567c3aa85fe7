from flask import Blueprint, jsonify, request
from flask_jwt_extended import jwt_required, get_jwt
from models import UserProjectActivity, ProjectCache
from app import db
from sqlalchemy import desc
import datetime
import uuid

user_activity_bp = Blueprint('user_activity_bp', __name__)

@user_activity_bp.route('/recent-projects', methods=['GET'])
@jwt_required()
def get_recent_projects(tenant_id):
    """
    Get recent projects accessed by the user.
    
    Returns:
        A list of recent project data objects accessed by the current user, 
        with the most recently accessed first.
    """
    # Tenant isolation check
    jwt_data = get_jwt()
    if jwt_data.get('tenant_id') != str(tenant_id):
        return jsonify(title="Forbidden", status=403, detail="Access to this tenant's data is forbidden"), 403
    
    limit = request.args.get('limit', 3, type=int)  # Default to 3 recent projects
    user_id = jwt_data.get('sub')  # 'sub' claim contains the user ID
    
    try:
        # Convert string user_id to UUID for database query
        try:
            user_uuid = uuid.UUID(user_id) if user_id else None
        except ValueError as e:
            return jsonify(title="Bad Request", status=400, detail=f"Invalid user ID format: {str(e)}"), 400
        
        if not user_uuid:
            return jsonify(title="Bad Request", status=400, detail="User ID not found in token"), 400
        
        # Convert string tenant_id to UUID if it's not already a UUID
        try:
            tenant_uuid = uuid.UUID(tenant_id) if not isinstance(tenant_id, uuid.UUID) else tenant_id
        except ValueError as e:
            return jsonify(title="Bad Request", status=400, detail=f"Invalid tenant ID format: {str(e)}"), 400
            
        # Query user's recent project activity
        recent_activities = UserProjectActivity.query.filter_by(
            tenant_id=tenant_uuid,
            user_id=user_uuid
        ).order_by(desc(UserProjectActivity.last_accessed)).limit(limit).all()
        
        # Collect project keys from activities
        project_keys = [activity.project_key for activity in recent_activities]
        
        # If no recent activities, return empty list
        if not project_keys:
            return jsonify({"projects": []})
        
        # Get project details from cache
        project_data = []
        for project_key in project_keys:
            project_cache = ProjectCache.query.filter_by(
                tenant_id=tenant_uuid,
                project_key=project_key
            ).first()
            
            if project_cache:
                project_data.append(project_cache.json_data)
            
        return jsonify({"projects": project_data})
    
    except Exception as e:
        import logging
        logging.error(f"Error fetching recent projects: {str(e)}", exc_info=True)
        return jsonify(title="Internal Server Error", status=500, detail=f"Error fetching recent projects: {str(e)}"), 500


@user_activity_bp.route('/track-project-access', methods=['POST'])
@jwt_required()
def track_project_access(tenant_id):
    """
    Record that a user has accessed a project.
    
    Request body:
        project_key: The key of the project accessed
    
    Returns:
        Success message or error
    """
    # Tenant isolation check
    jwt_data = get_jwt()
    if jwt_data.get('tenant_id') != str(tenant_id):
        return jsonify(title="Forbidden", status=403, detail="Access to this tenant's data is forbidden"), 403
    
    data = request.get_json()
    project_key = data.get('project_key')
    
    if not project_key:
        return jsonify(title="Bad Request", status=400, detail="Project key is required"), 400
    
    user_id = jwt_data.get('sub')  # 'sub' claim contains the user ID
    
    try:
        # Convert string user_id to UUID for database query
        try:
            user_uuid = uuid.UUID(user_id) if user_id else None
        except ValueError as e:
            return jsonify(title="Bad Request", status=400, detail=f"Invalid user ID format: {str(e)}"), 400
        
        if not user_uuid:
            return jsonify(title="Bad Request", status=400, detail="User ID not found in token"), 400
        
        # Convert string tenant_id to UUID if it's not already a UUID
        try:
            tenant_uuid = uuid.UUID(tenant_id) if not isinstance(tenant_id, uuid.UUID) else tenant_id
        except ValueError as e:
            return jsonify(title="Bad Request", status=400, detail=f"Invalid tenant ID format: {str(e)}"), 400
            
        # Check if an activity record already exists
        activity = UserProjectActivity.query.filter_by(
            tenant_id=tenant_uuid,
            user_id=user_uuid,
            project_key=project_key
        ).first()
        
        if activity:
            # Update existing record
            activity.last_accessed = datetime.datetime.utcnow()
        else:
            # Create new record
            activity = UserProjectActivity(
                tenant_id=tenant_uuid,
                user_id=user_uuid,
                project_key=project_key
            )
            db.session.add(activity)
        
        db.session.commit()
        return jsonify({"success": True, "message": "Project access recorded"})
    
    except Exception as e:
        db.session.rollback()
        import logging
        logging.error(f"Error recording project access: {str(e)}", exc_info=True)
        return jsonify(title="Internal Server Error", status=500, detail=f"Error recording project access: {str(e)}"), 500