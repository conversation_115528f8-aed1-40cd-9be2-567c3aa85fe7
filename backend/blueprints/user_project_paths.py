"""API endpoints for managing user project paths."""

from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt
from services.user_project_path_service import UserProjectPathService
from utils import validate_uuid
import logging

logger = logging.getLogger(__name__)
user_project_paths_bp = Blueprint('user_project_paths', __name__)


@user_project_paths_bp.route('/user-project-paths', methods=['POST'])
@jwt_required()
def create_or_update_path(tenant_id):
    """Create or update a user's project path mapping."""
    try:
        tenant_id = validate_uuid(tenant_id)
        claims = get_jwt()
        user_id = validate_uuid(claims.get('sub'))
        token_tenant_id = validate_uuid(claims.get('tenant_id'))
        
        # Verify tenant access
        if str(tenant_id) != str(token_tenant_id):
            return jsonify({'error': 'Unauthorized tenant access'}), 403
        
        data = request.get_json()
        project_key = data.get('project_key')
        local_path = data.get('local_path')
        
        if not project_key or not local_path:
            return jsonify({'error': 'Both project_key and local_path are required'}), 400
        
        path_mapping = UserProjectPathService.create_or_update_path(
            tenant_id=tenant_id,
            user_id=user_id,
            project_key=project_key,
            local_path=local_path
        )
        
        return jsonify({
            'id': str(path_mapping.id),
            'project_key': path_mapping.project_key,
            'local_path': path_mapping.local_path,
            'created_at': path_mapping.created_at.isoformat(),
            'updated_at': path_mapping.updated_at.isoformat()
        }), 200
        
    except Exception as e:
        logger.error(f"Error creating/updating project path: {e}")
        return jsonify({'error': str(e)}), 500


@user_project_paths_bp.route('/user-project-paths/<project_key>', methods=['GET'])
@jwt_required()
def get_project_path(tenant_id, project_key):
    """Get a user's local path for a specific project."""
    try:
        tenant_id = validate_uuid(tenant_id)
        claims = get_jwt()
        user_id = validate_uuid(claims.get('sub'))
        token_tenant_id = validate_uuid(claims.get('tenant_id'))
        
        # Verify tenant access
        if str(tenant_id) != str(token_tenant_id):
            return jsonify({'error': 'Unauthorized tenant access'}), 403
        
        local_path = UserProjectPathService.get_path(
            tenant_id=tenant_id,
            user_id=user_id,
            project_key=project_key
        )
        
        if local_path is None:
            return jsonify({'error': 'Project path not found'}), 404
        
        return jsonify({
            'project_key': project_key,
            'local_path': local_path
        }), 200
        
    except Exception as e:
        logger.error(f"Error getting project path: {e}")
        return jsonify({'error': str(e)}), 500


@user_project_paths_bp.route('/user-project-paths', methods=['GET'])
@jwt_required()
def list_user_paths(tenant_id):
    """List all project path mappings for the current user."""
    try:
        tenant_id = validate_uuid(tenant_id)
        claims = get_jwt()
        user_id = validate_uuid(claims.get('sub'))
        token_tenant_id = validate_uuid(claims.get('tenant_id'))
        
        # Verify tenant access
        if str(tenant_id) != str(token_tenant_id):
            return jsonify({'error': 'Unauthorized tenant access'}), 403
        
        path_mappings = UserProjectPathService.list_user_paths(
            tenant_id=tenant_id,
            user_id=user_id
        )
        
        return jsonify({
            'project_paths': [{
                'id': str(mapping.id),
                'project_key': mapping.project_key,
                'local_path': mapping.local_path,
                'created_at': mapping.created_at.isoformat(),
                'updated_at': mapping.updated_at.isoformat()
            } for mapping in path_mappings]
        }), 200
        
    except Exception as e:
        logger.error(f"Error listing user paths: {e}")
        return jsonify({'error': str(e)}), 500


@user_project_paths_bp.route('/user-project-paths/<project_key>', methods=['DELETE'])
@jwt_required()
def delete_project_path(tenant_id, project_key):
    """Delete (soft delete) a user's project path mapping."""
    try:
        tenant_id = validate_uuid(tenant_id)
        claims = get_jwt()
        user_id = validate_uuid(claims.get('sub'))
        token_tenant_id = validate_uuid(claims.get('tenant_id'))
        
        # Verify tenant access
        if str(tenant_id) != str(token_tenant_id):
            return jsonify({'error': 'Unauthorized tenant access'}), 403
        
        success = UserProjectPathService.delete_path(
            tenant_id=tenant_id,
            user_id=user_id,
            project_key=project_key
        )
        
        if not success:
            return jsonify({'error': 'Project path not found'}), 404
        
        return jsonify({'message': 'Project path deleted successfully'}), 200
        
    except Exception as e:
        logger.error(f"Error deleting project path: {e}")
        return jsonify({'error': str(e)}), 500