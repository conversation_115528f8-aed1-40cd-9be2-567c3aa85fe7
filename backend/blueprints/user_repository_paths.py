"""API endpoints for user repository paths."""

from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt
from models import db, UserDirectRepositoryPath as UserRepoPath, Repository
from services.repository_management_service import RepositoryManagementService
from utils import validate_uuid
import logging
from uuid import UUID, uuid4
from datetime import datetime

logger = logging.getLogger(__name__)
user_repo_paths_bp = Blueprint('user_repository_paths', __name__)


@user_repo_paths_bp.route('/repositories/<uuid:repository_id>/path', methods=['GET'])
@jwt_required()
def get_user_repository_path(tenant_id, repository_id):
    """Get user's local path for a repository."""
    try:
        tenant_id = validate_uuid(tenant_id)
        repository_id = validate_uuid(repository_id)
        claims = get_jwt()
        user_id = validate_uuid(claims.get('sub'))
        token_tenant_id = validate_uuid(claims.get('tenant_id'))
        
        # Verify tenant access
        if str(tenant_id) != str(token_tenant_id):
            return jsonify({'error': 'Unauthorized tenant access'}), 403
        
        # Check if repository exists
        repository = Repository.query.filter_by(
            id=repository_id,
            tenant_id=tenant_id
        ).first()
        
        if not repository:
            return jsonify({'error': 'Repository not found'}), 404
        
        # Get user path
        user_path = db.session.query(UserRepoPath).filter_by(
            user_id=user_id,
            repository_id=repository_id
        ).first()
        
        if user_path:
            return jsonify({
                'local_path': user_path.local_path,
                'is_valid': user_path.is_valid,
                'last_verified': user_path.last_verified.isoformat() if user_path.last_verified else None
            }), 200
        
        return jsonify({'local_path': None}), 200
        
    except Exception as e:
        logger.error(f"Error getting user repository path: {e}")
        return jsonify({'error': str(e)}), 500


@user_repo_paths_bp.route('/repositories/<uuid:repository_id>/path', methods=['PUT'])
@jwt_required()
def set_user_repository_path(tenant_id, repository_id):
    """Set user's local path for a repository."""
    try:
        tenant_id = validate_uuid(tenant_id)
        repository_id = validate_uuid(repository_id)
        claims = get_jwt()
        user_id = validate_uuid(claims.get('sub'))
        token_tenant_id = validate_uuid(claims.get('tenant_id'))
        
        # Verify tenant access
        if str(tenant_id) != str(token_tenant_id):
            return jsonify({'error': 'Unauthorized tenant access'}), 403
        
        data = request.get_json()
        local_path = data.get('local_path')
        
        if not local_path:
            return jsonify({'error': 'local_path is required'}), 400
        
        # Check if repository exists
        repository = Repository.query.filter_by(
            id=repository_id,
            tenant_id=tenant_id
        ).first()
        
        if not repository:
            return jsonify({'error': 'Repository not found'}), 404
        
        # Create or update user path
        user_path = db.session.query(UserRepoPath).filter_by(
            user_id=user_id,
            repository_id=repository_id
        ).first()
        
        if user_path:
            user_path.local_path = local_path
            user_path.updated_at = datetime.utcnow()
        else:
            user_path = UserRepoPath(
                id=uuid4(),
                user_id=user_id,
                repository_id=repository_id,
                local_path=local_path,
                is_valid=True
            )
            db.session.add(user_path)
        
        db.session.commit()
        
        return jsonify({
            'local_path': user_path.local_path,
            'updated': True
        }), 200
        
    except Exception as e:
        logger.error(f"Error setting user repository path: {e}")
        return jsonify({'error': str(e)}), 500


@user_repo_paths_bp.route('/repositories/paths', methods=['GET'])
@jwt_required()
def get_all_user_repository_paths(tenant_id):
    """Get all repository paths for the current user."""
    try:
        tenant_id = validate_uuid(tenant_id)
        claims = get_jwt()
        user_id = validate_uuid(claims.get('sub'))
        token_tenant_id = validate_uuid(claims.get('tenant_id'))
        
        # Verify tenant access
        if str(tenant_id) != str(token_tenant_id):
            return jsonify({'error': 'Unauthorized tenant access'}), 403
        
        # Get all paths for the user with repository info
        from sqlalchemy.orm import joinedload
        user_paths = db.session.query(UserRepoPath).filter_by(
            user_id=user_id
        ).options(joinedload(UserRepoPath.repository)).all()
        
        paths = {}
        for path in user_paths:
            if path.repository and str(path.repository.tenant_id) == str(tenant_id):
                paths[str(path.repository_id)] = {
                    'local_path': path.local_path,
                    'is_valid': path.is_valid,
                    'repository_name': path.repository.name
                }
        
        return jsonify({'paths': paths}), 200
        
    except Exception as e:
        logger.error(f"Error getting all user repository paths: {e}")
        return jsonify({'error': str(e)}), 500