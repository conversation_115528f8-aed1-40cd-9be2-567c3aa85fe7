import sqlite3

# Connect to the database
conn = sqlite3.connect('instance/jira_browser_dev.db')
cursor = conn.cursor()

# Check alembic version
cursor.execute("SELECT * FROM alembic_version")
rows = cursor.fetchall()
print("Current alembic version:", rows)

# Check if tables exist
cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
tables = cursor.fetchall()
print("\nExisting tables:")
for table in tables:
    print("-", table[0])

conn.close()