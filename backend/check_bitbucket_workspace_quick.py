#!/usr/bin/env python3
"""Quick check for Bitbucket workspace."""

import requests
import base64
import json

username = "r<PERSON><PERSON><PERSON><PERSON>"
app_password = "ATBB3GscQ3Yue35Tts6kehc2n2vP29A844B2"

# Create auth header
credentials = f"{username}:{app_password}"
encoded = base64.b64encode(credentials.encode()).decode()
headers = {
    "Authorization": f"Basic {encoded}",
    "Accept": "application/json"
}

print("Checking authentication...")
response = requests.get("https://api.bitbucket.org/2.0/user", headers=headers, timeout=5)
if response.status_code == 200:
    print("✓ Authenticated successfully")
else:
    print("✗ Authentication failed")
    exit(1)

# Check specific workspaces
workspaces_to_check = ["Jiffy_WS", "jiffy_ws", "jiffy", "jiffy_bb_admin"]

for ws in workspaces_to_check:
    print(f"\nChecking workspace: {ws}")
    try:
        # Check workspace
        ws_response = requests.get(f"https://api.bitbucket.org/2.0/workspaces/{ws}", headers=headers, timeout=5)
        if ws_response.status_code == 200:
            ws_data = ws_response.json()
            print(f"✓ Found workspace: {ws_data.get('name')}")
            print(f"  Slug: {ws_data.get('slug')}")
            
            # Try to list repos
            repos_response = requests.get(f"https://api.bitbucket.org/2.0/repositories/{ws}", headers=headers, params={"pagelen": 2}, timeout=5)
            if repos_response.status_code == 200:
                repos_data = repos_response.json()
                print(f"  Repositories: {repos_data.get('size', 0)} found")
                if repos_data.get('values'):
                    print(f"  First repo: {repos_data['values'][0]['name']}")
        else:
            print(f"✗ Cannot access workspace: {ws_response.status_code}")
    except Exception as e:
        print(f"✗ Error: {e}")

print("\n" + "="*50)
print("Use the successful workspace ID in the form.")
print(f"Username: {username}")
print("App Password: [your app password]")