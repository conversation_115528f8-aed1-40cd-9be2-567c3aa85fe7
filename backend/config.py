import os
import logging
from logging.handlers import RotatingFileHandler
from dotenv import load_dotenv

load_dotenv()

# Define the base directory of the Flask application
BASE_DIR = os.path.abspath(os.path.dirname(__file__))
# Define the instance path if it's not already set by <PERSON>lask. 
# Usually <PERSON><PERSON><PERSON> creates an 'instance' folder at the same level as your app package.
INSTANCE_FOLDER_PATH = os.path.join(BASE_DIR, 'instance')
if not os.path.exists(INSTANCE_FOLDER_PATH):
    os.makedirs(INSTANCE_FOLDER_PATH)

# Create logs directory if it doesn't exist
LOGS_FOLDER_PATH = os.path.join(BASE_DIR, 'logs')
if not os.path.exists(LOGS_FOLDER_PATH):
    os.makedirs(LOGS_FOLDER_PATH)

class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY', os.urandom(24))
    # Default to SQLite if DATABASE_URL is not set or to simplify setup
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or \
                              'sqlite:///' + os.path.join(INSTANCE_FOLDER_PATH, 'jira_browser_dev.db')
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    JWT_SECRET_KEY = os.environ.get('JWT_SECRET_KEY', os.urandom(24))
    
    # Logging Configuration
    LOG_LEVEL = os.environ.get('LOG_LEVEL', 'DEBUG')  # Default to DEBUG
    LOG_FILE_PATH = os.path.join(LOGS_FOLDER_PATH, 'app.log')
    LOG_FORMAT = '%(asctime)s [%(levelname)s] %(name)s:%(lineno)d - %(message)s'
    LOG_MAX_SIZE = 10 * 1024 * 1024  # 10 MB
    LOG_BACKUP_COUNT = 5  # Keep 5 backup files
    
    # Set SQL Alchemy logging level
    SQLALCHEMY_ECHO = os.environ.get('SQLALCHEMY_ECHO', 'False') == 'True'
    
    # Add other configurations as needed, e.g., JIRA API details, tenant settings
    
    # Repository Configuration
    REPO_BASE_PATH = os.environ.get('REPO_BASE_PATH', '/var/app/repositories')
    GIT_TIMEOUT_SECONDS = int(os.environ.get('GIT_TIMEOUT_SECONDS', '30'))
    PR_TEMPLATE_PATH = os.environ.get('PR_TEMPLATE_PATH', '/var/app/templates/pr')
    
    # Platform Rate Limits
    GITHUB_RATE_LIMIT = int(os.environ.get('GITHUB_RATE_LIMIT', '60'))
    BITBUCKET_RATE_LIMIT = int(os.environ.get('BITBUCKET_RATE_LIMIT', '100'))
    
    @staticmethod
    def configure_logging(app=None):
        # Configure logging with the specified LOG_LEVEL
        logging.basicConfig(level=getattr(logging, Config.LOG_LEVEL.upper()))
        
        # Remove existing handlers to avoid duplicate logs
        for handler in app.logger.handlers[:] if app else logging.root.handlers[:]:
            (app.logger if app else logging.root).removeHandler(handler)
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(getattr(logging, Config.LOG_LEVEL.upper()))
        console_formatter = logging.Formatter(Config.LOG_FORMAT)
        console_handler.setFormatter(console_formatter)
        
        # File handler with rotation
        file_handler = RotatingFileHandler(
            Config.LOG_FILE_PATH,
            maxBytes=Config.LOG_MAX_SIZE,
            backupCount=Config.LOG_BACKUP_COUNT
        )
        file_handler.setLevel(getattr(logging, Config.LOG_LEVEL.upper()))
        file_handler.setFormatter(console_formatter)
        
        # Add handlers
        if app:
            app.logger.addHandler(console_handler)
            app.logger.addHandler(file_handler)
            app.logger.setLevel(getattr(logging, Config.LOG_LEVEL.upper()))
        else:
            logging.root.addHandler(console_handler)
            logging.root.addHandler(file_handler)
    
    @classmethod
    def init_app(cls, app):
        # You can perform any app initialization here
        app.logger.setLevel(getattr(logging, cls.LOG_LEVEL.upper()))  # Set app logger level
        Config.configure_logging(app)
        app.logger.info(f"JIRA Bug Browser API starting up with log level: {cls.LOG_LEVEL}")
        pass


class TestConfig(Config):
    """Configuration for testing"""
    TESTING = True
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'
    JWT_SECRET_KEY = 'test-secret-key'
    SECRET_KEY = 'test-secret-key'
    APPLICATION_AES_KEY = '00112233445566778899aabbccddeeff00112233445566778899aabbccddeeff'
    SQLALCHEMY_ECHO = False
    
    # Test repository paths
    REPO_BASE_PATH = '/tmp/test_repositories'
    
    @classmethod
    def init_app(cls, app):
        """Configure app for testing."""
        app.logger.setLevel(logging.WARNING)  # Less verbose during tests