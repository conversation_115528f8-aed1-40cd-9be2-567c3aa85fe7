# Repository Management Feature

## Overview

The Repository Management feature allows administrators to configure and manage GitHub/Bitbucket repositories within the JIRA Bug Browser application. This enables users to map their local repository paths and link repositories to JIRA projects in a many-to-many relationship.

## Architecture

### Database Models

1. **Repository**: Stores repository configurations
   - `id`: Unique identifier
   - `tenant_id`: Tenant isolation
   - `name`: Unique repository identifier
   - `display_name`: User-friendly name
   - `repository_type`: github or bitbucket
   - `remote_url`: Repository clone URL
   - `credentials_encrypted`: Encrypted access credentials
   - `settings_json`: Additional configuration

2. **ProjectRepository**: Junction table for N:N relationship between projects and repositories
   - `project_key`: JIRA project identifier
   - `repository_id`: Repository reference
   - `is_primary`: Flag for primary repository

3. **UserRepositoryPath**: Maps repositories to local filesystem paths per user
   - `user_id`: User reference
   - `repository_id`: Repository reference
   - `local_path`: User's local repository path

### API Endpoints

#### Repository Management (Admin Only)
- `GET /tenants/{tid}/repositories` - List all repositories
- `POST /tenants/{tid}/repositories` - Create new repository
- `PUT /tenants/{tid}/repositories/{rid}` - Update repository
- `DELETE /tenants/{tid}/repositories/{rid}` - Delete repository

#### Project-Repository Links
- `GET /tenants/{tid}/projects/{pkey}/repositories` - List repositories for a project
- `POST /tenants/{tid}/projects/{pkey}/repositories` - Link repository to project
- `DELETE /tenants/{tid}/projects/{pkey}/repositories/{rid}` - Unlink repository

#### User Repository Paths
- `GET /tenants/{tid}/repositories/{rid}/path` - Get user's repository path
- `POST /tenants/{tid}/repositories/{rid}/path` - Set user's repository path

## Frontend Components

### RepositoryManagementPage
Admin-only page for managing repositories:
- List all repositories with CRUD operations
- Configure repository settings
- View repository status

### ProjectRepositoryDialog
Dialog for linking/unlinking repositories to projects:
- Show all available repositories
- Checkbox selection for linking
- Mark primary repository

### RepositoryPathDialog
Dialog for users to set their local repository paths:
- Input field for local path
- Path validation
- Save user preferences

### ProjectsPageV2
Enhanced projects page with repository information:
- Display linked repositories as chips
- Quick access to repository management
- Set local paths directly from project view

## Security Considerations

1. **Admin Access**: Repository management is restricted to tenant administrators
2. **Credential Encryption**: Repository credentials are encrypted using AES-256-GCM
3. **Tenant Isolation**: All operations are scoped to the authenticated tenant
4. **Path Validation**: Local paths are validated to prevent directory traversal

## Usage Guide

### As an Administrator

1. Navigate to the "Repositories" page from the main menu
2. Click "Add Repository" to create a new repository configuration
3. Fill in repository details:
   - Name (unique identifier)
   - Display Name (user-friendly)
   - Type (GitHub/Bitbucket)
   - Remote URL
   - Credentials (optional)
4. Save the repository

### Linking Repositories to Projects

1. Go to the Projects page
2. Click the menu icon (⋮) on a project
3. Select "Manage Repositories"
4. Check repositories to link them
5. Optionally mark one as primary
6. Save changes

### Setting Local Paths (As a User)

1. From any project with linked repositories
2. Click on a repository chip
3. Select "Set Local Path"
4. Enter your local repository path
5. Save the path

## Implementation Details

### Service Layer
The `RepositoryManagementService` provides:
- Repository CRUD operations
- Project-repository linking
- User path management
- Credential encryption/decryption

### API Integration
Frontend uses the `api.js` service to:
- Make authenticated requests
- Handle tenant context
- Process errors

### State Management
- Repository data is fetched on demand
- User paths are cached per session
- Project-repository links are refreshed on changes

## Future Enhancements

1. **Git Operations**: Integrate actual Git commands using configured paths
2. **Webhook Integration**: Automatically sync repository events
3. **Branch Management**: Show and switch between branches
4. **Commit History**: Display recent commits related to issues
5. **Pull Request Creation**: Create PRs directly from issues

## Migration Guide

To enable this feature in an existing installation:

1. Run the migration script:
   ```bash
   cd backend
   flask db upgrade add_repository_management_v2
   ```

2. Grant admin access to users who should manage repositories:
   ```sql
   UPDATE users SET role = 'tenant_admin' WHERE email = '<EMAIL>';
   ```

3. Deploy the updated frontend with new components

## Troubleshooting

### Common Issues

1. **Migration Fails**: Ensure the database user has CREATE TABLE permissions
2. **Repositories Not Showing**: Check user has tenant_admin role
3. **Path Not Saving**: Verify path exists and user has access
4. **Credentials Not Working**: Check encryption key is properly configured

### Debugging

Enable detailed logging:
```python
# In config.py
LOGGING_LEVEL = 'DEBUG'
```

Check service logs:
```bash
tail -f backend/logs/app.log | grep repository
```