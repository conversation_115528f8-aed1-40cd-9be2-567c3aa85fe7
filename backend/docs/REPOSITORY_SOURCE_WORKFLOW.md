# Repository Source Management Workflow

## Overview

This new architecture separates repository configuration into two distinct concepts:

1. **Repository Sources**: Admin-configured connections to repository providers (Bitbucket, GitHub, GitLab)
2. **Discovered Repositories**: Actual repositories fetched from these sources

## Workflow

### 1. Admin Configuration

```
Admin → Configure Repository Source → Provide Credentials → Save
```

Example for Bitbucket:
- Source Name: "Main Bitbucket Workspace"
- Type: Bitbucket
- Authentication:
  - Username: <EMAIL>
  - App Password: xxxx-xxxx-xxxx
- Settings:
  - Workspace ID: company-workspace

### 2. Repository Discovery

```
Admin → Select Source → Sync Repositories → Fetch from API → Cache Results
```

The system fetches all accessible repositories from the configured source and caches them locally.

### 3. Project-Repository Linking

```
User → Browse Projects → Select Project → View Available Repos → Link Repository
```

Users can browse discovered repositories and link them to JIRA projects.

### 4. Local Path Configuration

```
User → View Linked Repository → Set Local Path → Save Path
```

Each user can configure their own local path for linked repositories.

## Benefits

1. **Centralized Authentication**: Admin manages repository credentials once
2. **Dynamic Discovery**: Automatically discover new repositories
3. **User Choice**: Users select which repositories to link to projects
4. **Scalability**: Support multiple repository sources
5. **Security**: Credentials are encrypted and managed centrally

## Database Schema

### Repository Sources
- Stores provider configurations
- Encrypted credentials
- Source-specific settings

### Discovered Repositories
- Cached repository information
- Last sync timestamp
- Links to source

### Project Repository Links
- N:N relationship between projects and repositories
- User who created the link
- Primary repository flag

### User Repository Paths
- User-specific local paths
- Per repository per user
- Path validation status

## API Endpoints

### Repository Source Management (Admin)
- `GET /repository-sources` - List sources
- `POST /repository-sources` - Create source
- `PUT /repository-sources/{id}` - Update source
- `DELETE /repository-sources/{id}` - Delete source
- `POST /repository-sources/{id}/sync` - Sync repositories

### Repository Discovery
- `GET /repository-sources/{id}/repositories` - List discovered repos
- `GET /discovered-repositories` - List all discovered repos

### Project-Repository Linking
- `GET /projects/{key}/available-repositories` - List linkable repos
- `POST /projects/{key}/repositories` - Link repository
- `DELETE /projects/{key}/repositories/{id}` - Unlink repository

### User Path Management
- `GET /projects/{key}/repositories/{id}/path` - Get user's path
- `PUT /projects/{key}/repositories/{id}/path` - Set user's path

## Implementation Steps

1. Create new models for repository sources
2. Implement repository sync services
3. Build admin UI for source management
4. Create repository browser UI
5. Update project UI to show available repositories
6. Add user path configuration

## Example Configuration

### Bitbucket Source
```json
{
  "name": "Company Bitbucket",
  "source_type": "bitbucket",
  "auth_config": {
    "username": "<EMAIL>",
    "app_password": "ATBBxxxxx"
  },
  "settings": {
    "workspace_id": "company-workspace"
  }
}
```

### GitHub Source
```json
{
  "name": "Company GitHub",
  "source_type": "github",
  "auth_config": {
    "token": "ghp_xxxxx"
  },
  "settings": {
    "org_name": "company-org"
  }
}
```

## Security Considerations

1. All credentials are encrypted using AES-256-GCM
2. Repository sync requires admin privileges
3. Users can only link repositories to accessible projects
4. Local paths are user-specific and isolated