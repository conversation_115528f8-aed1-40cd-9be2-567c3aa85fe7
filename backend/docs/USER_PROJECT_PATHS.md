# User Project Paths

This feature allows users to map their JIRA projects to local filesystem paths where the corresponding code repositories are located.

## Overview

The User Project Paths functionality enables users to:
- Associate JIRA project keys with local directory paths
- Store these mappings per user and per tenant
- Retrieve local paths based on JIRA project keys
- Manage multiple project-to-path mappings

## API Endpoints

### Create or Update Project Path

Creates a new project path mapping or updates an existing one.

```
POST /tenants/{tenant_id}/user-project-paths
```

**Request Body:**
```json
{
    "project_key": "PROJ-1",
    "local_path": "/Users/<USER>/projects/project1"
}
```

**Response:**
```json
{
    "id": "uuid",
    "project_key": "PROJ-1",
    "local_path": "/Users/<USER>/projects/project1",
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
}
```

### Get Project Path

Retrieves the local path for a specific project.

```
GET /tenants/{tenant_id}/user-project-paths/{project_key}
```

**Response:**
```json
{
    "project_key": "PROJ-1",
    "local_path": "/Users/<USER>/projects/project1"
}
```

### List User Paths

Lists all project path mappings for the current user.

```
GET /tenants/{tenant_id}/user-project-paths
```

**Response:**
```json
{
    "project_paths": [
        {
            "id": "uuid",
            "project_key": "PROJ-1",
            "local_path": "/Users/<USER>/projects/project1",
            "created_at": "2024-01-01T00:00:00Z",
            "updated_at": "2024-01-01T00:00:00Z"
        },
        {
            "id": "uuid",
            "project_key": "PROJ-2",
            "local_path": "/Users/<USER>/projects/project2",
            "created_at": "2024-01-01T00:00:00Z",
            "updated_at": "2024-01-01T00:00:00Z"
        }
    ]
}
```

### Delete Project Path

Removes a project path mapping (soft delete).

```
DELETE /tenants/{tenant_id}/user-project-paths/{project_key}
```

**Response:**
```json
{
    "message": "Project path deleted successfully"
}
```

## Database Model

The `UserProjectPath` model stores the mappings with the following fields:

- `id`: UUID primary key
- `tenant_id`: Foreign key to the tenant
- `user_id`: Foreign key to the user
- `project_key`: JIRA project key
- `local_path`: Local filesystem path
- `is_active`: Boolean flag for soft deletes
- `created_at`: Timestamp of creation
- `updated_at`: Timestamp of last update

A unique constraint ensures one mapping per tenant+user+project combination.

## Security

- All endpoints require JWT authentication
- Users can only access their own project paths
- Cross-tenant access is denied
- Tenant ID from JWT must match the tenant ID in the URL

## Usage Example

```python
# In a service that needs to access local repositories
from services.user_project_path_service import UserProjectPathService

# Get the local path for a JIRA project
local_path = UserProjectPathService.get_path(
    tenant_id=current_user_tenant_id,
    user_id=current_user_id,
    project_key="PROJ-123"
)

if local_path:
    # Use the local path to access the repository
    repo_path = local_path
else:
    # Handle case where no path is configured
    pass
```

## Testing

Run the tests for this functionality:

```bash
./run_user_project_paths_tests.py
```

With coverage:
```bash
./run_user_project_paths_tests.py --coverage
```