```mermaid
sequenceDiagram
    participant Admin
    participant System
    participant Bitbucket
    participant User
    participant LocalFS
    
    Admin->>System: Configure Bitbucket Source
    System->>System: Encrypt & Store Credentials
    
    Admin->>System: Sync Repositories
    System->>Bitbucket: API: List Repositories
    Bitbucket-->>System: Repository List
    System->>System: Cache Discovered Repos
    
    User->>System: Browse JIRA Project
    System-->>User: Show Project Details
    
    User->>System: View Available Repositories
    System-->>User: Show Discovered Repos
    
    User->>System: Link Repository to Project
    System->>System: Create Project-Repo Link
    
    User->>System: Set Local Path
    System->>System: Store User Path
    
    User->>System: Get Repository Info
    System-->>User: Repo Details + Local Path
    
    User->>LocalFS: Clone/Pull Repository
    LocalFS-->>User: Repository Files
```