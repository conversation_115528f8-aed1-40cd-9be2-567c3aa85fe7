```mermaid
graph TD
    A[Admin] -->|Configure| B[Repository Source]
    B -->|Bitbucket| C[Workspace Credentials]
    B -->|GitHub| D[Organization Token]
    B -->|GitLab| E[Instance URL + Token]
    
    B -->|Sync| F[Discovered Repositories]
    F -->|Cache| G[Local Repository List]
    
    H[User] -->|Browse| I[JIRA Projects]
    I -->|Select Project| J[Available Repositories]
    G -->|Provide| J
    
    J -->|Link| K[Project-Repository Link]
    K -->|Configure| L[User Local Path]
    
    M[Multiple Users] -->|Each Sets| L
    
    style A fill:#f9f,stroke:#333,stroke-width:2px
    style H fill:#9ff,stroke:#333,stroke-width:2px
    style M fill:#ff9,stroke:#333,stroke-width:2px
    style B fill:#9f9,stroke:#333,stroke-width:2px
    style F fill:#99f,stroke:#333,stroke-width:2px
```