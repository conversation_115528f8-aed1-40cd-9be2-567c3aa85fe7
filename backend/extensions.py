"""
Extensions module to avoid circular imports.
This module initializes Flask extensions that will be used throughout the application.
"""
from flask_sqlalchemy import SQLAlchemy
from flask_migrate import Migrate
from flask_jwt_extended import <PERSON><PERSON><PERSON>anager
from flask_socketio import SocketIO

# Initialize extensions
db = SQLAlchemy()
migrate = Migrate()
jwt = JWTManager()

# Using a simple Socket.IO configuration with threading mode
# This avoids dependency conflicts with eventlet/gevent
socketio = SocketIO(
    async_mode='threading',   # Use threading mode for stability
    cors_allowed_origins="*", # For development only
    ping_timeout=60,
    ping_interval=25,
)
