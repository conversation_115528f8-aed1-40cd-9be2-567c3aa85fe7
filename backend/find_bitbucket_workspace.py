#!/usr/bin/env python3
"""Find the correct Bitbucket workspace ID."""

import requests
import json
import sys
import base64
from datetime import datetime

# Color codes for terminal output
GREEN = '\033[92m'
YELLOW = '\033[93m'
RED = '\033[91m'
RESET = '\033[0m'

def log(message, color=RESET):
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"{color}[{timestamp}] {message}{RESET}")

def find_workspace(username, app_password):
    """Find accessible Bitbucket workspaces."""
    
    log("Bitbucket Workspace Finder", YELLOW)
    log("=" * 50, YELLOW)
    
    # Create basic auth header
    credentials = f"{username}:{app_password}"
    encoded_credentials = base64.b64encode(credentials.encode()).decode()
    headers = {
        "Authorization": f"Basic {encoded_credentials}",
        "Accept": "application/json"
    }
    
    # First, verify authentication
    user_url = "https://api.bitbucket.org/2.0/user"
    try:
        log("Verifying authentication...", YELLOW)
        response = requests.get(user_url, headers=headers, timeout=10)
        
        if response.status_code == 401:
            log("Authentication failed. Check username and app password.", RED)
            return
        elif response.status_code != 200:
            log(f"Error: {response.status_code} - {response.text}", RED)
            return
            
        user_data = response.json()
        log(f"✓ Authenticated as: {user_data.get('display_name')} ({user_data.get('username')})", GREEN)
        actual_username = user_data.get('username')
        
    except Exception as e:
        log(f"Error: {e}", RED)
        return
    
    # Get workspaces the user has access to
    try:
        log("\nFinding accessible workspaces...", YELLOW)
        
        # Method 1: User workspaces
        workspaces_url = f"https://api.bitbucket.org/2.0/user/permissions/workspaces"
        response = requests.get(workspaces_url, headers=headers, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            workspaces = data.get('values', [])
            
            if workspaces:
                log(f"\nFound {len(workspaces)} workspace(s):", GREEN)
                for ws in workspaces:
                    workspace_info = ws.get('workspace', {})
                    log(f"\n  Workspace: {workspace_info.get('name')}", GREEN)
                    log(f"  Slug/ID: {workspace_info.get('slug')}", YELLOW)
                    log(f"  Type: {workspace_info.get('type')}")
                    log(f"  UUID: {workspace_info.get('uuid')}")
        
        # Method 2: Try common workspace names
        log("\nTrying common workspace patterns...", YELLOW)
        test_workspaces = [
            "jiffy_bb_admin",
            "Jiffy_WS",
            "jiffy_ws",
            "jiffy-ws",
            "jiffy",
            actual_username
        ]
        
        for ws_id in test_workspaces:
            try:
                test_url = f"https://api.bitbucket.org/2.0/workspaces/{ws_id}"
                response = requests.get(test_url, headers=headers, timeout=5)
                
                if response.status_code == 200:
                    ws_data = response.json()
                    log(f"\n✓ Found workspace: {ws_data.get('name')}", GREEN)
                    log(f"  Slug/ID: {ws_data.get('slug')}", YELLOW)
                    log(f"  UUID: {ws_data.get('uuid')}")
                    
                    # Try to list repositories
                    repos_url = f"https://api.bitbucket.org/2.0/repositories/{ws_id}"
                    repos_response = requests.get(repos_url, headers=headers, params={"pagelen": 1}, timeout=5)
                    
                    if repos_response.status_code == 200:
                        repos_data = repos_response.json()
                        total_repos = repos_data.get('size', 0)
                        log(f"  Repositories: {total_repos} accessible")
                elif response.status_code == 404:
                    log(f"  {ws_id}: Not found", RED)
                elif response.status_code == 403:
                    log(f"  {ws_id}: No access", RED)
            except:
                pass
        
        # Method 3: Try from a repository URL if available
        log("\n" + "=" * 50, YELLOW)
        log("Configuration for backend:", YELLOW)
        log("If one of the workspaces above worked, use:", GREEN)
        log("  Workspace ID: [the slug/ID shown above]")
        log(f"  Username: {username}")
        log("  App Password: [your app password]")
        
    except Exception as e:
        log(f"Error finding workspaces: {e}", RED)

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python find_bitbucket_workspace.py <username> <app_password>")
        print("Example: python find_bitbucket_workspace.py <EMAIL> ATBB...")
        sys.exit(1)
    
    username = sys.argv[1]
    app_password = sys.argv[2]
    
    find_workspace(username, app_password)