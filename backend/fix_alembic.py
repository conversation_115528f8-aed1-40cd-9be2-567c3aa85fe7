import sqlite3

# Connect to the database
conn = sqlite3.connect('instance/jira_browser_dev.db')
cursor = conn.cursor()

# Update alembic version to point to our latest migration
cursor.execute("UPDATE alembic_version SET version_num = 'b8a3eb743d1f'")
conn.commit()

# Verify the update
cursor.execute("SELECT * FROM alembic_version")
rows = cursor.fetchall()
print("Updated alembic version:", rows)

conn.close()