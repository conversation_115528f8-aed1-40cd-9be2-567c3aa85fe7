"""Fix syntax errors in services."""
import re

# Fix repository_service.py
with open('services/repository_service.py', 'r') as f:
    content = f.read()

# Remove the garbled UUID conversions
content = re.sub(
    r"user_id=uuid\.UUID\(uuid\.UUID\([^)]+\) if isinstance\(uuid\.UUID\([^)]+, str\) else uuid\.UUID\([^)]+\) if isinstance\([^)]+, str\) else [^,]+,",
    "user_id=uuid.UUID(user_id) if isinstance(user_id, str) else user_id,",
    content
)

# Fix other patterns  
content = re.sub(
    r"tenant_id=uuid\.UUID\(uuid\.UUID\([^)]+\) if isinstance\([^)]+, str\) else [^)]+\) if isinstance\([^)]+, str\) else [^,]+,",
    "tenant_id=uuid.UUID(tenant_id) if isinstance(tenant_id, str) else tenant_id,",
    content
)

with open('services/repository_service.py', 'w') as f:
    f.write(content)

# Fix pr_service.py
with open('services/pr_service.py', 'r') as f:
    content = f.read()

# Similar fixes
content = re.sub(
    r"user_id=uuid\.UUID\(uuid\.UUID\([^)]+\) if isinstance\([^)]+, str\) else [^)]+\) if isinstance\([^)]+, str\) else [^,]+,",
    "user_id=uuid.UUID(user_id) if isinstance(user_id, str) else user_id,",
    content
)

with open('services/pr_service.py', 'w') as f:
    f.write(content)

print("Fixed syntax errors in services!")