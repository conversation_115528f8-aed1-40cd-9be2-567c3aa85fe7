"""Fix UUID conversion issues across the codebase."""
import os
import re
import glob

def fix_uuid_conversion(file_path):
    """Fix UUID type conversion issues in a file."""
    print(f"Processing {file_path}...")
    
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Fix repo_config_id queries
    content = re.sub(
        r"(RepositoryConfig\.query\.filter_by\(\s*id=)([^,\)]+)",
        lambda m: f"{m.group(1)}uuid.UUID({m.group(2)}) if isinstance({m.group(2)}, str) else {m.group(2)}",
        content
    )
    
    # Fix user_id assignments
    content = re.sub(
        r"user_id=([^,\)]+)(?!\.)",
        lambda m: f"user_id=uuid.UUID({m.group(1)}) if isinstance({m.group(1)}, str) else {m.group(1)}",
        content
    )
    
    # Fix tenant_id assignments
    content = re.sub(
        r"tenant_id=([^,\)]+)(?!\.)",
        lambda m: f"tenant_id=uuid.UUID({m.group(1)}) if isinstance({m.group(1)}, str) else {m.group(1)}",
        content
    )
    
    # Fix repository activity
    content = re.sub(
        r"repo_config_id=([^,\)]+)(?!\.)",
        lambda m: f"repo_config_id=uuid.UUID({m.group(1)}) if isinstance({m.group(1)}, str) else {m.group(1)}",
        content
    )
    
    # Ensure uuid is imported
    if 'uuid.UUID' in content and 'import uuid' not in content:
        if 'from typing import' in content:
            content = content.replace('from typing import', 'import uuid\nfrom typing import')
        else:
            content = 'import uuid\n' + content
    
    with open(file_path, 'w') as f:
        f.write(content)

# Fix specific files
files_to_fix = [
    'blueprints/repositories.py',
    'blueprints/pull_requests.py',
    'services/repository_service.py',
    'services/pr_service.py'
]

for file_path in files_to_fix:
    if os.path.exists(file_path):
        fix_uuid_conversion(file_path)
    
print("UUID conversion fixes applied!")

# Also fix the test fixtures
test_files = glob.glob('tests/test_*.py')
for test_file in test_files:
    with open(test_file, 'r') as f:
        content = f.read()
    
    # Fix test repository creation
    content = re.sub(
        r"created_by=auth_headers_admin\['user_id'\]",
        "created_by=uuid.UUID(auth_headers_admin['user_id'])",
        content
    )
    
    with open(test_file, 'w') as f:
        f.write(content)