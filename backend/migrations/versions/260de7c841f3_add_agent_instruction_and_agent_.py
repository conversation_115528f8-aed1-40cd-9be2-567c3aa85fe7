"""Add agent_instruction and agent_description to repositories

Revision ID: 260de7c841f3
Revises: merge_heads
Create Date: 2025-05-22 19:42:17.847942

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '260de7c841f3'
down_revision = 'merge_heads'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('user_project_paths')
    with op.batch_alter_table('repositories', schema=None) as batch_op:
        batch_op.add_column(sa.Column('agent_instruction', sa.Text(), nullable=True))
        batch_op.add_column(sa.Column('agent_description', sa.Text(), nullable=True))

    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.batch_alter_table('repositories', schema=None) as batch_op:
        batch_op.drop_column('agent_description')
        batch_op.drop_column('agent_instruction')

    op.create_table('user_project_paths',
    sa.Column('id', sa.NUMERIC(), nullable=False),
    sa.Column('tenant_id', sa.NUMERIC(), nullable=False),
    sa.Column('user_id', sa.NUMERIC(), nullable=False),
    sa.Column('project_key', sa.VARCHAR(length=100), nullable=False),
    sa.Column('local_path', sa.VARCHAR(length=500), nullable=False),
    sa.Column('is_active', sa.BOOLEAN(), nullable=True),
    sa.Column('created_at', sa.DATETIME(), nullable=True),
    sa.Column('updated_at', sa.DATETIME(), nullable=True),
    sa.ForeignKeyConstraint(['tenant_id'], ['tenants.id'], ),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('tenant_id', 'user_id', 'project_key', name='uq_user_project_path')
    )
    # ### end Alembic commands ###
