"""add email to jira credentials

Revision ID: add_email_to_jira_credentials
Revises: 22375d77fad7
Create Date: 2025-05-15

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = 'add_email_to_jira_credentials'
down_revision = '22375d77fad7' # Set this to the ID of your most recent migration
branch_labels = None
depends_on = None


def upgrade():
    # Add email column to jira_credentials table
    op.add_column('jira_credentials', sa.Column('email', sa.String(255), nullable=True))


def downgrade():
    # Remove email column from jira_credentials table
    op.drop_column('jira_credentials', 'email')