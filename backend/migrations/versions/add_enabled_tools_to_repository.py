"""Add enabled_tools column to repositories table

Revision ID: add_enabled_tools_01
Revises: 260de7c841f3
Create Date: 2025-01-23
"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'add_enabled_tools_01'
down_revision = '260de7c841f3'
branch_labels = None
depends_on = None


def upgrade():
    # Add enabled_tools column to repositories table
    op.add_column('repositories', sa.Column('enabled_tools', sa.JSON(), nullable=True))
    
    # Set default value for existing rows
    op.execute("UPDATE repositories SET enabled_tools = '[]' WHERE enabled_tools IS NULL")


def downgrade():
    # Remove enabled_tools column from repositories table
    op.drop_column('repositories', 'enabled_tools')