"""Add functional areas tables

Revision ID: add_functional_areas
Revises: add_repository_management_tables
Create Date: 2025-01-25 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.types import TypeDecorator, CHAR
import uuid

# Custom UUID type for SQLite compatibility
class UUID_Type(TypeDecorator):
    impl = CHAR
    cache_ok = True

    def __init__(self, as_uuid=False, **kwargs):
        self.as_uuid = as_uuid
        super(UUID_Type, self).__init__(length=36, **kwargs)

    def load_dialect_impl(self, dialect):
        if dialect.name == 'postgresql':
            return dialect.type_descriptor(UUID(as_uuid=self.as_uuid))
        else:
            return dialect.type_descriptor(CHAR(36))

    def process_bind_param(self, value, dialect):
        if value is None:
            return value
        elif dialect.name == 'postgresql':
            return value
        else:
            if isinstance(value, uuid.UUID):
                return str(value)
            elif isinstance(value, str):
                return str(uuid.UUID(value))
            return str(value)

    def process_result_value(self, value, dialect):
        if value is None:
            return value
        if self.as_uuid and not isinstance(value, uuid.UUID):
            try:
                return uuid.UUID(value)
            except (TypeError, ValueError):
                return value
        return value

# revision identifiers, used by Alembic.
revision = 'add_functional_areas'
down_revision = 'add_repository_management'
branch_labels = None
depends_on = None


def upgrade():
    # Create functional_areas table
    op.create_table(
        'functional_areas',
        sa.Column('id', UUID_Type(as_uuid=True), primary_key=True, default=uuid.uuid4),
        sa.Column('tenant_id', UUID_Type(as_uuid=True), sa.ForeignKey('tenants.id'), nullable=False),
        sa.Column('name', sa.String(255), nullable=False),
        sa.Column('description', sa.Text),
        sa.Column('created_at', sa.DateTime, default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime, default=sa.func.now()),
        sa.Column('created_by', UUID_Type(as_uuid=True), sa.ForeignKey('users.id')),
        sa.Column('is_active', sa.Boolean, default=True),
        sa.Column('category', sa.String(100)),
        sa.Column('priority', sa.String(50), default='medium'),
        sa.Column('metadata_json', sa.JSON),
    )
    
    # Create unique constraint for tenant + name
    op.create_unique_constraint(
        'uq_tenant_functional_area_name',
        'functional_areas',
        ['tenant_id', 'name']
    )
    
    # Create indexes
    op.create_index('idx_functional_areas_tenant', 'functional_areas', ['tenant_id'])
    op.create_index('idx_functional_areas_active', 'functional_areas', ['is_active'])
    
    # Create functional_area_projects table
    op.create_table(
        'functional_area_projects',
        sa.Column('id', UUID_Type(as_uuid=True), primary_key=True, default=uuid.uuid4),
        sa.Column('tenant_id', UUID_Type(as_uuid=True), sa.ForeignKey('tenants.id'), nullable=False),
        sa.Column('functional_area_id', UUID_Type(as_uuid=True), sa.ForeignKey('functional_areas.id'), nullable=False),
        sa.Column('project_key', sa.String(100), nullable=False),
        sa.Column('project_role', sa.String(100)),
        sa.Column('linked_at', sa.DateTime, default=sa.func.now()),
        sa.Column('linked_by', UUID_Type(as_uuid=True), sa.ForeignKey('users.id')),
        sa.Column('is_primary', sa.Boolean, default=False),
    )
    
    # Create unique constraint for functional_area + project
    op.create_unique_constraint(
        'uq_functional_area_project',
        'functional_area_projects',
        ['tenant_id', 'functional_area_id', 'project_key']
    )
    
    # Create indexes
    op.create_index('idx_functional_area_projects_area', 'functional_area_projects', ['functional_area_id'])
    op.create_index('idx_functional_area_projects_project', 'functional_area_projects', ['project_key'])
    
    # Create functional_area_repositories table
    op.create_table(
        'functional_area_repositories',
        sa.Column('id', UUID_Type(as_uuid=True), primary_key=True, default=uuid.uuid4),
        sa.Column('tenant_id', UUID_Type(as_uuid=True), sa.ForeignKey('tenants.id'), nullable=False),
        sa.Column('functional_area_id', UUID_Type(as_uuid=True), sa.ForeignKey('functional_areas.id'), nullable=False),
        sa.Column('repository_id', UUID_Type(as_uuid=True), sa.ForeignKey('repositories.id'), nullable=False),
        sa.Column('repository_role', sa.String(100)),
        sa.Column('linked_at', sa.DateTime, default=sa.func.now()),
        sa.Column('linked_by', UUID_Type(as_uuid=True), sa.ForeignKey('users.id')),
        sa.Column('is_primary', sa.Boolean, default=False),
    )
    
    # Create unique constraint for functional_area + repository
    op.create_unique_constraint(
        'uq_functional_area_repository',
        'functional_area_repositories',
        ['tenant_id', 'functional_area_id', 'repository_id']
    )
    
    # Create indexes
    op.create_index('idx_functional_area_repos_area', 'functional_area_repositories', ['functional_area_id'])
    op.create_index('idx_functional_area_repos_repo', 'functional_area_repositories', ['repository_id'])


def downgrade():
    # Drop tables in reverse order
    op.drop_table('functional_area_repositories')
    op.drop_table('functional_area_projects')
    op.drop_table('functional_areas')