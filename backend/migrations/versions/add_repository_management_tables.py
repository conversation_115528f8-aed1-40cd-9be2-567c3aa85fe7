"""Add repository management tables

Revision ID: add_repository_management
Revises: add_user_project_activity
Create Date: 2025-05-17

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'add_repository_management'
down_revision = 'add_user_project_activity'
branch_labels = None
depends_on = None


def upgrade():
    # Create repository_sources table
    op.create_table('repository_sources',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('tenant_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('source_type', sa.Enum('bitbucket', 'github', 'gitlab', name='source_type_enum'), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('config', sa.<PERSON>(), nullable=True),
        sa.Column('credentials_encrypted', sa.Text(), nullable=True),
        sa.Column('is_active', sa.<PERSON>(), nullable=True, default=True),
        sa.Column('last_sync_at', sa.DateTime(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.Column('created_by', postgresql.UUID(as_uuid=True), nullable=True),
        sa.ForeignKeyConstraint(['created_by'], ['users.id'], ),
        sa.ForeignKeyConstraint(['tenant_id'], ['tenants.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_repository_sources_tenant_id'), 'repository_sources', ['tenant_id'], unique=False)

    # Create repositories table
    op.create_table('repositories',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('tenant_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('source_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('display_name', sa.String(length=255), nullable=True),
        sa.Column('repository_type', sa.String(length=50), nullable=True),
        sa.Column('remote_url', sa.String(length=1024), nullable=True),
        sa.Column('default_branch', sa.String(length=100), nullable=True),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('is_active', sa.Boolean(), nullable=True, default=True),
        sa.Column('last_sync_at', sa.DateTime(), nullable=True),
        sa.Column('metadata', sa.JSON(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['source_id'], ['repository_sources.id'], ),
        sa.ForeignKeyConstraint(['tenant_id'], ['tenants.id'], ),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_repositories_source_id'), 'repositories', ['source_id'], unique=False)
    op.create_index(op.f('ix_repositories_tenant_id'), 'repositories', ['tenant_id'], unique=False)

    # Create project_repositories table
    op.create_table('project_repositories',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('tenant_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('project_key', sa.String(length=50), nullable=False),
        sa.Column('repository_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('is_primary', sa.Boolean(), nullable=True, default=False),
        sa.Column('linked_by', postgresql.UUID(as_uuid=True), nullable=True),
        sa.Column('linked_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['linked_by'], ['users.id'], ),
        sa.ForeignKeyConstraint(['repository_id'], ['repositories.id'], ),
        sa.ForeignKeyConstraint(['tenant_id'], ['tenants.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('tenant_id', 'project_key', 'repository_id', name='uq_tenant_project_repo')
    )
    op.create_index(op.f('ix_project_repositories_tenant_id'), 'project_repositories', ['tenant_id'], unique=False)

    # Create user_repository_paths table
    op.create_table('user_repository_paths',
        sa.Column('id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('tenant_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('user_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('project_repository_id', postgresql.UUID(as_uuid=True), nullable=False),
        sa.Column('local_path', sa.String(length=1024), nullable=False),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.Column('updated_at', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['project_repository_id'], ['project_repositories.id'], ),
        sa.ForeignKeyConstraint(['tenant_id'], ['tenants.id'], ),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('user_id', 'project_repository_id', name='uq_user_project_repo')
    )
    op.create_index(op.f('ix_user_repository_paths_tenant_id'), 'user_repository_paths', ['tenant_id'], unique=False)
    op.create_index(op.f('ix_user_repository_paths_user_id'), 'user_repository_paths', ['user_id'], unique=False)


def downgrade():
    # Drop tables in reverse order
    op.drop_index(op.f('ix_user_repository_paths_user_id'), table_name='user_repository_paths')
    op.drop_index(op.f('ix_user_repository_paths_tenant_id'), table_name='user_repository_paths')
    op.drop_table('user_repository_paths')
    
    op.drop_index(op.f('ix_project_repositories_tenant_id'), table_name='project_repositories')
    op.drop_table('project_repositories')
    
    op.drop_index(op.f('ix_repositories_tenant_id'), table_name='repositories')
    op.drop_index(op.f('ix_repositories_source_id'), table_name='repositories')
    op.drop_table('repositories')
    
    op.drop_index(op.f('ix_repository_sources_tenant_id'), table_name='repository_sources')
    op.drop_table('repository_sources')
    
    # Drop the enum type
    sa.Enum(name='source_type_enum').drop(op.get_bind(), checkfirst=False)