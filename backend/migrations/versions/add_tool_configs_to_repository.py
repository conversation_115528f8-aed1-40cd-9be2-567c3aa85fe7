"""Add tool_configs column to repositories table

Revision ID: add_tool_configs_01
Revises: add_enabled_tools_01
Create Date: 2025-01-23
"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'add_tool_configs_01'
down_revision = 'add_enabled_tools_01'
branch_labels = None
depends_on = None


def upgrade():
    # Add tool_configs column to repositories table
    op.add_column('repositories', sa.Column('tool_configs', sa.JSON(), nullable=True))
    
    # Set default value for existing rows
    op.execute("UPDATE repositories SET tool_configs = '{}' WHERE tool_configs IS NULL")


def downgrade():
    # Remove tool_configs column from repositories table
    op.drop_column('repositories', 'tool_configs')