"""Add user direct repository paths table

Revision ID: add_user_direct_repo_paths
Revises: 
Create Date: 2025-05-17

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import UUID


# revision identifiers, used by Alembic.
revision = 'add_user_direct_repo_paths'
down_revision = 'add_repository_management'
branch_labels = None
depends_on = None


def upgrade():
    """Create user_direct_repository_paths table."""
    # Check if table already exists
    conn = op.get_bind()
    result = conn.execute(sa.text("SELECT name FROM sqlite_master WHERE type='table' AND name='user_direct_repository_paths'"))
    if result.fetchone() is not None:
        return  # Table already exists
    
    op.create_table(
        'user_direct_repository_paths',
        sa.Column('id', UUID(as_uuid=True), primary_key=True),
        sa.Column('user_id', UUID(as_uuid=True), sa.ForeignKey('users.id'), nullable=False),
        sa.Column('repository_id', UUID(as_uuid=True), sa.<PERSON>ey('repositories.id'), nullable=False),
        sa.Column('local_path', sa.String(1024), nullable=False),
        sa.Column('is_valid', sa.Boolean(), default=True),
        sa.Column('last_verified', sa.DateTime()),
        sa.Column('created_at', sa.DateTime(), default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime(), default=sa.func.now(), onupdate=sa.func.now()),
        sa.UniqueConstraint('user_id', 'repository_id', name='uq_user_direct_repository_path')
    )


def downgrade():
    """Drop user_direct_repository_paths table."""
    op.drop_table('user_direct_repository_paths')