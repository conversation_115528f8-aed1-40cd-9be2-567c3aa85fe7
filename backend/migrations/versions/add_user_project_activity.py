"""add user project activity table

Revision ID: add_user_project_activity
Revises: add_email_to_jira_credentials
Create Date: 2025-05-15

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'add_user_project_activity'
down_revision = 'add_email_to_jira_credentials'
branch_labels = None
depends_on = None


def upgrade():
    # Create user_project_activity table to track recent project activity
    op.create_table('user_project_activity',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('tenant_id', sa.UUID(), nullable=False),
        sa.Column('user_id', sa.UUID(), nullable=False),
        sa.Column('project_key', sa.String(length=100), nullable=False),
        sa.Column('last_accessed', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['tenant_id'], ['tenants.id'], ),
        sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('tenant_id', 'user_id', 'project_key', name='uq_user_project')
    )


def downgrade():
    # Drop user_project_activity table
    op.drop_table('user_project_activity')