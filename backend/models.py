from extensions import db # Import db from extensions.py to avoid circular imports
import uuid
from sqlalchemy.types import TypeDecorator, CHAR
from sqlalchemy.dialects.postgresql import UUID as PostgresUUID
from datetime import datetime

# Custom UUID type for SQLite compatibility
class UUID(TypeDecorator):
    """Platform-independent UUID type.

    Uses PostgreSQL's UUID type when available, otherwise uses CHAR(36).
    """
    impl = CHAR
    cache_ok = True

    def __init__(self, as_uuid=False, **kwargs):
        self.as_uuid = as_uuid
        super(UUID, self).__init__(length=36, **kwargs)

    def load_dialect_impl(self, dialect):
        if dialect.name == 'postgresql':
            return dialect.type_descriptor(PostgresUUID(as_uuid=self.as_uuid))
        else:
            return dialect.type_descriptor(CHAR(36))

    def process_bind_param(self, value, dialect):
        if value is None:
            return value
        elif dialect.name == 'postgresql':
            return value
        else:
            if isinstance(value, uuid.UUID):
                return str(value)
            elif isinstance(value, str):
                return str(uuid.UUID(value))
            return str(value)

    def process_result_value(self, value, dialect):
        if value is None:
            return value
        if self.as_uuid and not isinstance(value, uuid.UUID):
            try:
                return uuid.UUID(value)
            except (TypeError, ValueError):
                return value
        return value

class Tenant(db.Model):
    __tablename__ = 'tenants'
    id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = db.Column(db.String(100), nullable=False)
    plan = db.Column(db.String(50))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    users = db.relationship('User', backref='tenant', lazy=True)
    jira_credentials = db.relationship('JiraCredential', backref='tenant', lazy=True)
    project_caches = db.relationship('ProjectCache', backref='tenant', lazy=True)
    issue_caches = db.relationship('IssueCache', backref='tenant', lazy=True)
    refresh_tokens = db.relationship('RefreshToken', backref='tenant', lazy=True)

class User(db.Model):
    __tablename__ = 'users'
    id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = db.Column(UUID(as_uuid=True), db.ForeignKey('tenants.id'), nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False) # Should be unique per tenant in logic
    password_hash = db.Column(db.String(255), nullable=False)
    role = db.Column(db.String(50), default='user') # e.g., user, admin
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    jira_credentials = db.relationship('JiraCredential', backref='user', lazy=True)
    refresh_tokens = db.relationship('RefreshToken', backref='user', lazy=True)

class JiraCredential(db.Model):
    __tablename__ = 'jira_credentials'
    id = db.Column(db.Integer, primary_key=True)
    tenant_id = db.Column(UUID(as_uuid=True), db.ForeignKey('tenants.id'), nullable=False)
    user_id = db.Column(UUID(as_uuid=True), db.ForeignKey('users.id'), nullable=True) # Nullable if tenant-wide credential
    base_url = db.Column(db.String(255), nullable=False)
    api_key_encrypted = db.Column(db.String(512), nullable=False) # Or db.LargeBinary for raw encrypted bytes
    email = db.Column(db.String(255), nullable=True) # Email associated with the API key
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class ProjectCache(db.Model):
    __tablename__ = 'project_cache'
    id = db.Column(db.Integer, primary_key=True)
    tenant_id = db.Column(UUID(as_uuid=True), db.ForeignKey('tenants.id'), nullable=False)
    project_key = db.Column(db.String(100), nullable=False)
    json_data = db.Column(db.JSON, nullable=False) # Or db.Text if storing as string
    fetched_at = db.Column(db.DateTime, default=datetime.utcnow)
    __table_args__ = (db.UniqueConstraint('tenant_id', 'project_key', name='uq_tenant_project'),)

class IssueCache(db.Model):
    __tablename__ = 'issue_cache'
    id = db.Column(db.Integer, primary_key=True)
    tenant_id = db.Column(UUID(as_uuid=True), db.ForeignKey('tenants.id'), nullable=False)
    issue_key = db.Column(db.String(100), nullable=False)
    project_key = db.Column(db.String(100), nullable=True) # For easier querying alongside ProjectCache
    json_data = db.Column(db.JSON, nullable=False) # Or db.Text
    fetched_at = db.Column(db.DateTime, default=datetime.utcnow)
    __table_args__ = (db.UniqueConstraint('tenant_id', 'issue_key', name='uq_tenant_issue'),)

class RefreshToken(db.Model):
    __tablename__ = 'refresh_tokens'
    id = db.Column(db.Integer, primary_key=True)
    tenant_id = db.Column(UUID(as_uuid=True), db.ForeignKey('tenants.id'), nullable=False)
    user_id = db.Column(UUID(as_uuid=True), db.ForeignKey('users.id'), nullable=False)
    token_hash = db.Column(db.String(255), nullable=False, unique=True)
    expires_at = db.Column(db.DateTime, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class UserProjectActivity(db.Model):
    """Tracks user's recent activity with projects for providing personalized recent projects list."""
    __tablename__ = 'user_project_activity'
    id = db.Column(db.Integer, primary_key=True)
    tenant_id = db.Column(UUID(as_uuid=True), db.ForeignKey('tenants.id'), nullable=False)
    user_id = db.Column(UUID(as_uuid=True), db.ForeignKey('users.id'), nullable=False)
    project_key = db.Column(db.String(100), nullable=False)
    last_accessed = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Define unique constraint to ensure one record per user-project combination
    __table_args__ = (db.UniqueConstraint('tenant_id', 'user_id', 'project_key', name='uq_user_project'),)

class RepositorySource(db.Model):
    """Repository source configuration (Bitbucket workspace, GitHub org, etc)."""
    __tablename__ = 'repository_sources'

    id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = db.Column(UUID(as_uuid=True), db.ForeignKey('tenants.id'), nullable=False)
    name = db.Column(db.String(255), nullable=False)  # e.g., "Main Bitbucket Workspace"
    source_type = db.Column(db.String(50), nullable=False)  # bitbucket, github, gitlab

    # Authentication configuration (encrypted)
    auth_config_encrypted = db.Column(db.Text)  # JSON with app password, tokens, etc.

    # Source-specific settings
    settings_json = db.Column(db.JSON)  # workspace_id, org_name, gitlab_url, etc.

    # Metadata
    is_active = db.Column(db.Boolean, default=True)
    last_sync_at = db.Column(db.DateTime)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    created_by = db.Column(UUID(as_uuid=True), db.ForeignKey('users.id'))

    # Relationships
    tenant = db.relationship('Tenant', backref='repository_sources')
    creator = db.relationship('User', foreign_keys=[created_by], backref='created_sources')

    __table_args__ = (
        db.UniqueConstraint('tenant_id', 'name', name='uq_tenant_source_name'),
        db.CheckConstraint("source_type IN ('bitbucket', 'github', 'gitlab')", name='check_source_type'),
    )


class Repository(db.Model):
    """Repositories discovered from a source (cached list)."""
    __tablename__ = 'repositories'

    id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    source_id = db.Column(UUID(as_uuid=True), db.ForeignKey('repository_sources.id'), nullable=False)
    tenant_id = db.Column(UUID(as_uuid=True), db.ForeignKey('tenants.id'), nullable=False)

    # Repository information from the source
    external_id = db.Column(db.String(255), nullable=False)  # Bitbucket UUID, GitHub ID, etc.
    name = db.Column(db.String(255), nullable=False)  # Repository name/slug
    full_name = db.Column(db.String(500))  # Full name with namespace
    description = db.Column(db.Text)
    clone_url = db.Column(db.String(1024))  # HTTPS clone URL
    ssh_url = db.Column(db.String(1024))    # SSH clone URL
    default_branch = db.Column(db.String(100))

    # Agent configuration for AI bug fixing
    agent_instruction = db.Column(db.Text)  # Custom instruction for the AI agent
    agent_description = db.Column(db.Text)  # Custom description for the AI agent
    enabled_tools = db.Column(db.JSON, default=list)  # List of enabled tool names
    tool_configs = db.Column(db.JSON, default=dict)  # Tool-specific configurations

    # Source-specific metadata
    metadata_json = db.Column(db.JSON)  # Additional info from API

    # Sync information
    discovered_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_updated = db.Column(db.DateTime, default=datetime.utcnow)
    is_accessible = db.Column(db.Boolean, default=True)  # Can we access this repo?

    # Relationships
    source = db.relationship('RepositorySource', backref='discovered_repositories')
    tenant = db.relationship('Tenant', backref='repositories')
    projects = db.relationship('ProjectRepository', back_populates='repository')

    __table_args__ = (
        db.UniqueConstraint('source_id', 'external_id', name='uq_source_repo'),
        db.Index('idx_discovered_repos_source', 'source_id'),
        db.Index('idx_discovered_repos_tenant', 'tenant_id'),
    )


class ProjectRepository(db.Model):
    """Junction table linking JIRA projects to repositories (N:N)."""
    __tablename__ = 'project_repositories'

    id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = db.Column(UUID(as_uuid=True), db.ForeignKey('tenants.id'), nullable=False)
    project_key = db.Column(db.String(100), nullable=False)
    repository_id = db.Column(UUID(as_uuid=True), db.ForeignKey('repositories.id'), nullable=False)
    linked_by = db.Column(UUID(as_uuid=True), db.ForeignKey('users.id'))
    linked_at = db.Column(db.DateTime, default=datetime.utcnow)
    is_primary = db.Column(db.Boolean, default=False)  # Mark primary repo for a project

    # Relationships
    tenant = db.relationship('Tenant', backref='project_repository_links')
    repository = db.relationship('Repository', back_populates='projects')
    linker = db.relationship('User', backref='linked_project_repos')

    __table_args__ = (
        db.UniqueConstraint('tenant_id', 'project_key', 'repository_id', name='uq_project_repository'),
    )


class UserRepositoryPath(db.Model):
    """User-specific local paths for project-linked repositories."""
    __tablename__ = 'user_repository_paths'

    id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = db.Column(UUID(as_uuid=True), db.ForeignKey('users.id'), nullable=False)
    project_repository_id = db.Column(UUID(as_uuid=True), db.ForeignKey('project_repositories.id'), nullable=False)
    local_path = db.Column(db.String(1024), nullable=False)

    # Metadata
    is_valid = db.Column(db.Boolean, default=True)  # Path exists and is accessible
    last_verified = db.Column(db.DateTime)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    user = db.relationship('User', backref='repository_paths')
    project_repository_link = db.relationship('ProjectRepository', backref='user_paths')

    __table_args__ = (
        db.UniqueConstraint('user_id', 'project_repository_id', name='uq_user_project_repo_path'),
    )

class UserDirectRepositoryPath(db.Model):
    """User-specific local paths for repositories (direct, not through projects)."""
    __tablename__ = 'user_direct_repository_paths'

    id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = db.Column(UUID(as_uuid=True), db.ForeignKey('users.id'), nullable=False)
    repository_id = db.Column(UUID(as_uuid=True), db.ForeignKey('repositories.id'), nullable=False)
    local_path = db.Column(db.String(1024), nullable=False)

    # Metadata
    is_valid = db.Column(db.Boolean, default=True)  # Path exists and is accessible
    last_verified = db.Column(db.DateTime)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    user = db.relationship('User', backref='repository_paths_direct')
    repository = db.relationship('Repository', backref='user_paths_direct')

    __table_args__ = (
        db.UniqueConstraint('user_id', 'repository_id', name='uq_user_direct_repository_path'),
    )

class RepositoryConfig(db.Model):
    """Stores repository configurations for each tenant"""
    __tablename__ = 'repository_configs'
    id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = db.Column(UUID(as_uuid=True), db.ForeignKey('tenants.id'), nullable=False)
    repo_name = db.Column(db.String(255), nullable=False)
    repo_type = db.Column(db.String(50), nullable=False)  # 'github' or 'bitbucket'
    local_path = db.Column(db.String(500))
    remote_url = db.Column(db.String(500), nullable=False)
    default_branch = db.Column(db.String(100), default='main')
    credentials_encrypted = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    created_by = db.Column(UUID(as_uuid=True), db.ForeignKey('users.id'))
    is_active = db.Column(db.Boolean, default=True)

    # Relationships
    tenant = db.relationship('Tenant', backref='repository_configs')
    creator = db.relationship('User', foreign_keys=[created_by], backref='created_repos')
    pull_requests = db.relationship('PullRequest', backref='repository_config', lazy=True)
    activities = db.relationship('RepositoryActivity', backref='repository_config', lazy=True)

    __table_args__ = (
        db.UniqueConstraint('tenant_id', 'repo_name', name='uq_tenant_repo'),
        db.CheckConstraint("repo_type IN ('github', 'bitbucket')", name='check_repo_type'),
    )

class PullRequest(db.Model):
    """Tracks pull requests created through the application"""
    __tablename__ = 'pull_requests'
    id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = db.Column(UUID(as_uuid=True), db.ForeignKey('tenants.id'), nullable=False)
    repo_config_id = db.Column(UUID(as_uuid=True), db.ForeignKey('repository_configs.id'), nullable=False)
    issue_key = db.Column(db.String(50), nullable=False)
    pr_number = db.Column(db.Integer)
    pr_url = db.Column(db.String(500))
    title = db.Column(db.String(500), nullable=False)
    description = db.Column(db.Text)
    source_branch = db.Column(db.String(100), nullable=False)
    target_branch = db.Column(db.String(100), nullable=False)
    status = db.Column(db.String(50), default='pending')  # pending, created, merged, closed
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    created_by = db.Column(UUID(as_uuid=True), db.ForeignKey('users.id'))
    pr_metadata = db.Column(db.JSON)  # Renamed from metadata to pr_metadata

    # Relationships
    tenant = db.relationship('Tenant', backref='pull_requests')
    creator = db.relationship('User', foreign_keys=[created_by], backref='created_prs')

class RepositoryActivity(db.Model):
    """Logs repository-related activities for audit and troubleshooting"""
    __tablename__ = 'repository_activity'
    id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = db.Column(UUID(as_uuid=True), db.ForeignKey('tenants.id'), nullable=False)
    repo_config_id = db.Column(UUID(as_uuid=True), db.ForeignKey('repository_configs.id'), nullable=False)
    activity_type = db.Column(db.String(50), nullable=False)  # clone, pull, push, pr_created
    status = db.Column(db.String(50), nullable=False)  # success, failed
    details = db.Column(db.JSON)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    user_id = db.Column(UUID(as_uuid=True), db.ForeignKey('users.id'))

    # Relationships
    tenant = db.relationship('Tenant', backref='repository_activities')

class FunctionalArea(db.Model):
    """Functional areas that group multiple JIRA projects and repositories together.
    
    Examples: 'E-commerce Platform', 'User Authentication System', 'Payment Processing'
    Each functional area can contain multiple projects (backend, frontend, mobile)
    and multiple repositories.
    """
    __tablename__ = 'functional_areas'
    
    id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = db.Column(UUID(as_uuid=True), db.ForeignKey('tenants.id'), nullable=False)
    name = db.Column(db.String(255), nullable=False)
    description = db.Column(db.Text)
    
    # Metadata
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    created_by = db.Column(UUID(as_uuid=True), db.ForeignKey('users.id'))
    is_active = db.Column(db.Boolean, default=True)
    
    # Optional categorization
    category = db.Column(db.String(100))  # e.g., 'Frontend', 'Backend', 'Full-Stack', 'Infrastructure'
    priority = db.Column(db.String(50), default='medium')  # high, medium, low
    
    # Additional metadata
    metadata_json = db.Column(db.JSON)  # For future extensibility
    
    # Relationships
    tenant = db.relationship('Tenant', backref='functional_areas')
    creator = db.relationship('User', foreign_keys=[created_by], backref='created_functional_areas')
    project_links = db.relationship('FunctionalAreaProject', back_populates='functional_area', cascade='all, delete-orphan')
    repository_links = db.relationship('FunctionalAreaRepository', back_populates='functional_area', cascade='all, delete-orphan')
    
    __table_args__ = (
        db.UniqueConstraint('tenant_id', 'name', name='uq_tenant_functional_area_name'),
    )

class FunctionalAreaProject(db.Model):
    """Links JIRA projects to functional areas (N:N relationship)."""
    __tablename__ = 'functional_area_projects'
    
    id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = db.Column(UUID(as_uuid=True), db.ForeignKey('tenants.id'), nullable=False)
    functional_area_id = db.Column(UUID(as_uuid=True), db.ForeignKey('functional_areas.id'), nullable=False)
    project_key = db.Column(db.String(100), nullable=False)
    
    # Role of this project within the functional area
    project_role = db.Column(db.String(100))  # e.g., 'backend', 'frontend', 'mobile-app', 'api'
    
    # Metadata
    linked_at = db.Column(db.DateTime, default=datetime.utcnow)
    linked_by = db.Column(UUID(as_uuid=True), db.ForeignKey('users.id'))
    is_primary = db.Column(db.Boolean, default=False)  # Primary project for this functional area
    
    # Relationships
    tenant = db.relationship('Tenant', backref='functional_area_project_links')
    functional_area = db.relationship('FunctionalArea', back_populates='project_links')
    linker = db.relationship('User', backref='linked_functional_area_projects')
    
    __table_args__ = (
        db.UniqueConstraint('tenant_id', 'functional_area_id', 'project_key', name='uq_functional_area_project'),
    )

class FunctionalAreaRepository(db.Model):
    """Links repositories to functional areas (N:N relationship)."""
    __tablename__ = 'functional_area_repositories'
    
    id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = db.Column(UUID(as_uuid=True), db.ForeignKey('tenants.id'), nullable=False)
    functional_area_id = db.Column(UUID(as_uuid=True), db.ForeignKey('functional_areas.id'), nullable=False)
    repository_id = db.Column(UUID(as_uuid=True), db.ForeignKey('repositories.id'), nullable=False)
    
    # Role of this repository within the functional area
    repository_role = db.Column(db.String(100))  # e.g., 'backend-api', 'frontend-web', 'mobile-ios', 'shared-lib'
    
    # Metadata
    linked_at = db.Column(db.DateTime, default=datetime.utcnow)
    linked_by = db.Column(UUID(as_uuid=True), db.ForeignKey('users.id'))
    is_primary = db.Column(db.Boolean, default=False)  # Primary repository for this functional area
    
    # Relationships
    tenant = db.relationship('Tenant', backref='functional_area_repository_links')
    functional_area = db.relationship('FunctionalArea', back_populates='repository_links')
    repository = db.relationship('Repository', backref='functional_area_links')
    linker = db.relationship('User', backref='linked_functional_area_repositories')
    
    __table_args__ = (
        db.UniqueConstraint('tenant_id', 'functional_area_id', 'repository_id', name='uq_functional_area_repository'),
    )