"""Enhanced repository source management models - Version 3."""

from datetime import datetime
import uuid
from sqlalchemy.dialects.postgresql import UUID
from app import db


class RepositorySource(db.Model):
    """Repository source configuration (Bitbucket workspace, GitHub org, etc)."""
    __tablename__ = 'repository_sources'
    
    id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = db.Column(UUID(as_uuid=True), db.ForeignKey('tenants.id'), nullable=False)
    name = db.Column(db.String(255), nullable=False)  # e.g., "Main Bitbucket Workspace"
    source_type = db.Column(db.String(50), nullable=False)  # bitbucket, github, gitlab
    
    # Authentication configuration (encrypted)
    auth_config_encrypted = db.Column(db.Text)  # JSON with app password, tokens, etc.
    
    # Source-specific settings
    settings_json = db.Column(db.JSON)  # workspace_id, org_name, gitlab_url, etc.
    
    # Metadata
    is_active = db.Column(db.Bo<PERSON>, default=True)
    last_sync_at = db.Column(db.DateTime)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    created_by = db.Column(UUID(as_uuid=True), db.ForeignKey('users.id'))
    
    # Relationships
    discovered_repositories = db.relationship('DiscoveredRepository', backref='source', lazy=True, cascade='all, delete-orphan')
    tenant = db.relationship('Tenant', backref='repository_sources')
    creator = db.relationship('User', foreign_keys=[created_by])
    
    __table_args__ = (
        db.UniqueConstraint('tenant_id', 'name', name='uq_tenant_source_name'),
        db.CheckConstraint("source_type IN ('bitbucket', 'github', 'gitlab')", name='check_source_type'),
    )


class DiscoveredRepository(db.Model):
    """Repositories discovered from a source (cached list)."""
    __tablename__ = 'discovered_repositories'
    
    id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    source_id = db.Column(UUID(as_uuid=True), db.ForeignKey('repository_sources.id'), nullable=False)
    tenant_id = db.Column(UUID(as_uuid=True), db.ForeignKey('tenants.id'), nullable=False)
    
    # Repository information from the source
    external_id = db.Column(db.String(255), nullable=False)  # Bitbucket UUID, GitHub ID, etc.
    name = db.Column(db.String(255), nullable=False)  # Repository name/slug
    full_name = db.Column(db.String(500))  # Full name with namespace
    description = db.Column(db.Text)
    clone_url = db.Column(db.String(1024))  # HTTPS clone URL
    ssh_url = db.Column(db.String(1024))    # SSH clone URL
    default_branch = db.Column(db.String(100))
    
    # Source-specific metadata
    metadata_json = db.Column(db.JSON)  # Additional info from API
    
    # Sync information
    discovered_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_updated = db.Column(db.DateTime, default=datetime.utcnow)
    is_accessible = db.Column(db.Boolean, default=True)  # Can we access this repo?
    
    # Relationships
    project_links = db.relationship('ProjectRepositoryLink', backref='repository', lazy=True, cascade='all, delete-orphan')
    tenant = db.relationship('Tenant', backref='discovered_repositories')
    
    __table_args__ = (
        db.UniqueConstraint('source_id', 'external_id', name='uq_source_repo'),
        db.Index('idx_discovered_repos_source', 'source_id'),
        db.Index('idx_discovered_repos_tenant', 'tenant_id'),
    )


class ProjectRepositoryLink(db.Model):
    """Links JIRA projects to discovered repositories."""
    __tablename__ = 'project_repository_links'
    
    id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = db.Column(UUID(as_uuid=True), db.ForeignKey('tenants.id'), nullable=False)
    project_key = db.Column(db.String(100), nullable=False)
    repository_id = db.Column(UUID(as_uuid=True), db.ForeignKey('discovered_repositories.id'), nullable=False)
    
    # Link metadata
    is_primary = db.Column(db.Boolean, default=False)
    linked_by = db.Column(UUID(as_uuid=True), db.ForeignKey('users.id'))
    linked_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relationships
    tenant = db.relationship('Tenant', backref='project_repository_links')
    linker = db.relationship('User', foreign_keys=[linked_by])
    user_paths = db.relationship('UserRepositoryPath', backref='link', lazy=True, cascade='all, delete-orphan')
    
    __table_args__ = (
        db.UniqueConstraint('tenant_id', 'project_key', 'repository_id', name='uq_project_repo_link'),
        db.Index('idx_project_links_tenant_project', 'tenant_id', 'project_key'),
    )


class UserRepositoryPath(db.Model):
    """User-specific local paths for linked repositories."""
    __tablename__ = 'user_repository_paths_v3'
    
    id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = db.Column(UUID(as_uuid=True), db.ForeignKey('users.id'), nullable=False)
    link_id = db.Column(UUID(as_uuid=True), db.ForeignKey('project_repository_links.id'), nullable=False)
    local_path = db.Column(db.String(1024), nullable=False)
    
    # Metadata
    is_valid = db.Column(db.Boolean, default=True)  # Path exists and is accessible
    last_verified = db.Column(db.DateTime)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    user = db.relationship('User', backref='repository_paths_v3')
    
    __table_args__ = (
        db.UniqueConstraint('user_id', 'link_id', name='uq_user_link_path'),
    )