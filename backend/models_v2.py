# Updated models for repository management with N:N relationships

from models import db, UUID
from datetime import datetime
import uuid

class Repository(db.Model):
    """Represents a code repository (GitHub/Bitbucket)."""
    __tablename__ = 'repositories'
    
    id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = db.Column(UUID(as_uuid=True), db.ForeignKey('tenants.id'), nullable=False)
    name = db.Column(db.String(255), nullable=False)
    display_name = db.Column(db.String(255))
    repository_type = db.Column(db.String(50), nullable=False)  # 'github' or 'bitbucket'
    remote_url = db.Column(db.String(500), nullable=False)
    default_branch = db.Column(db.String(100), default='main')
    description = db.Column(db.Text)
    is_active = db.Column(db.<PERSON>, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    created_by = db.Column(UUID(as_uuid=True), db.ForeignKey('users.id'))
    
    # Relationships
    tenant = db.relationship('Tenant', backref='repositories')
    creator = db.relationship('User', backref='created_repositories', foreign_keys=[created_by])
    projects = db.relationship('ProjectRepository', back_populates='repository')
    user_paths = db.relationship('UserRepositoryPath', back_populates='repository')
    
    __table_args__ = (
        db.UniqueConstraint('tenant_id', 'name', name='uq_tenant_repository'),
        db.CheckConstraint(repository_type.in_(['github', 'bitbucket']), name='check_repository_type')
    )


class ProjectRepository(db.Model):
    """Junction table linking JIRA projects to repositories (N:N)."""
    __tablename__ = 'project_repositories'
    
    id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = db.Column(UUID(as_uuid=True), db.ForeignKey('tenants.id'), nullable=False)
    project_key = db.Column(db.String(100), nullable=False)
    repository_id = db.Column(UUID(as_uuid=True), db.ForeignKey('repositories.id'), nullable=False)
    linked_by = db.Column(UUID(as_uuid=True), db.ForeignKey('users.id'))
    linked_at = db.Column(db.DateTime, default=datetime.utcnow)
    is_primary = db.Column(db.Boolean, default=False)  # Mark primary repo for a project
    
    # Relationships
    tenant = db.relationship('Tenant', backref='project_repository_links')
    repository = db.relationship('Repository', back_populates='projects')
    linker = db.relationship('User', backref='linked_project_repos')
    
    __table_args__ = (
        db.UniqueConstraint('tenant_id', 'project_key', 'repository_id', name='uq_project_repository'),
    )


class UserRepositoryPath(db.Model):
    """Maps repositories to local filesystem paths for each user."""
    __tablename__ = 'user_repository_paths'
    
    id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = db.Column(UUID(as_uuid=True), db.ForeignKey('tenants.id'), nullable=False)
    user_id = db.Column(UUID(as_uuid=True), db.ForeignKey('users.id'), nullable=False)
    repository_id = db.Column(UUID(as_uuid=True), db.ForeignKey('repositories.id'), nullable=False)
    local_path = db.Column(db.String(500), nullable=False)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    tenant = db.relationship('Tenant', backref='user_repository_paths')
    user = db.relationship('User', backref='repository_paths')
    repository = db.relationship('Repository', back_populates='user_paths')
    
    __table_args__ = (
        db.UniqueConstraint('tenant_id', 'user_id', 'repository_id', name='uq_user_repository_path'),
    )


class RepositoryCredential(db.Model):
    """Stores encrypted credentials for repository access."""
    __tablename__ = 'repository_credentials'
    
    id = db.Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = db.Column(UUID(as_uuid=True), db.ForeignKey('tenants.id'), nullable=False)
    repository_id = db.Column(UUID(as_uuid=True), db.ForeignKey('repositories.id'), nullable=False)
    credential_type = db.Column(db.String(50), nullable=False)  # 'token', 'ssh_key', 'username_password'
    encrypted_value = db.Column(db.Text, nullable=False)
    username = db.Column(db.String(255))  # For username/password type
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    created_by = db.Column(UUID(as_uuid=True), db.ForeignKey('users.id'))
    
    # Relationships
    tenant = db.relationship('Tenant', backref='repository_credentials')
    repository = db.relationship('Repository', backref='credentials')
    creator = db.relationship('User', backref='created_credentials')
    
    __table_args__ = (
        db.UniqueConstraint('tenant_id', 'repository_id', 'credential_type', name='uq_repository_credential'),
    )