#!/usr/bin/env python3
"""
Reset database by dropping all tables and recreating them.
Useful for development when migrations get complicated.
"""

from app import create_app, db
from models import *  # Import all models to ensure they're registered

if __name__ == '__main__':
    app = create_app()
    
    with app.app_context():
        # Drop all tables
        print("Dropping all tables...")
        db.drop_all()
        
        # Create all tables from models
        print("Creating all tables from models...")
        db.create_all()
        
        print("Database reset complete!")
        print("Note: You may need to run seed.py to populate initial data.")