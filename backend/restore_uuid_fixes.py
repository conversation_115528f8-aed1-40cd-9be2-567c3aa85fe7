"""Restore UUID fixes to working state."""

# Fix pr_service.py
pr_service_content = """\"\"\"Pull Request creation and management service.\"\"\"
import os
import json
import logging
from typing import Optional, Dict, List
from datetime import datetime
import uuid
import requests
from string import Template

from app import db
from models import PullRequest, RepositoryConfig, IssueCache
from flask import current_app
from utils import decrypt_data

logger = logging.getLogger(__name__)


class PullRequestService:
    \"\"\"Service for managing pull requests.\"\"\"
    
    def __init__(self):
        self.repo_base_path = None
        self.git_timeout = None
        self.pr_template_path = None
    
    def _init_config(self):
        \"\"\"Initialize configuration from Flask app context.\"\"\"
        if self.repo_base_path is None:
            self.repo_base_path = current_app.config.get('REPO_BASE_PATH', '/var/app/repositories')
            self.git_timeout = current_app.config.get('GIT_TIMEOUT_SECONDS', 30)
            self.pr_template_path = current_app.config.get('PR_TEMPLATE_PATH', '/var/app/templates/pr')
    
    def create_pull_request(self, tenant_id: str, user_id: str, pr_data: dict) -> PullRequest:
        \"\"\"Create a new pull request.\"\"\"
        try:
            # Get repository configuration
            repo_config = RepositoryConfig.query.filter_by(
                id=uuid.UUID(pr_data['repo_config_id']) if isinstance(pr_data['repo_config_id'], str) else pr_data['repo_config_id'],
                tenant_id=uuid.UUID(tenant_id) if isinstance(tenant_id, str) else tenant_id,
                is_active=True
            ).first()
            
            if not repo_config:
                raise ValueError("Repository not found or inactive")
            
            # Get issue details from cache or JIRA
            issue_data = self._get_issue_data(tenant_id, pr_data['issue_key'])
            
            # Create feature branch
            source_branch = self._create_feature_branch(
                repo_config, 
                pr_data['issue_key'],
                pr_data.get('branch_name')
            )
            
            # Generate PR description from template
            description = self._generate_pr_description(
                issue_data,
                pr_data.get('description_template', 'default')
            )
            
            # Create PR record in database
            pull_request = PullRequest(
                tenant_id=tenant_id,
                repo_config_id=repo_config.id,
                issue_key=pr_data['issue_key'],
                title=pr_data.get('title') or f"Fix: {issue_data.get('summary', pr_data['issue_key'])}",
                description=description,
                source_branch=source_branch,
                target_branch=pr_data.get('target_branch', repo_config.default_branch),
                status='pending',
                created_by=uuid.UUID(user_id) if isinstance(user_id, str) else user_id,
                pr_metadata={}
            )
            
            db.session.add(pull_request)
            db.session.commit()
            
            # Create PR on the platform
            platform_pr = self._create_platform_pr(
                repo_config,
                pull_request.title,
                description,
                source_branch,
                pull_request.target_branch
            )
            
            # Update PR with platform data
            pull_request.pr_number = platform_pr.get('number')
            pull_request.pr_url = platform_pr.get('url')
            pull_request.status = 'created'
            pull_request.pr_metadata = platform_pr
            db.session.commit()
            
            return pull_request
            
        except Exception as e:
            logger.error(f"Failed to create pull request: {str(e)}")
            db.session.rollback()
            raise
    
    def get_pull_requests(self, tenant_id: str, filters: dict = None) -> List[PullRequest]:
        \"\"\"Get pull requests with optional filters.\"\"\"
        query = PullRequest.query.filter_by(tenant_id=tenant_id)
        
        if filters:
            if 'repo_config_id' in filters:
                query = query.filter_by(repo_config_id=filters['repo_config_id'])
            if 'issue_key' in filters:
                query = query.filter_by(issue_key=filters['issue_key'])
            if 'status' in filters:
                query = query.filter_by(status=filters['status'])
            if 'created_by' in filters:
                query = query.filter_by(created_by=filters['created_by'])
        
        return query.order_by(PullRequest.created_at.desc()).all()
    
    def get_pull_request(self, tenant_id: str, pr_id: str) -> Optional[PullRequest]:
        \"\"\"Get a specific pull request.\"\"\"
        return PullRequest.query.filter_by(
            id=pr_id,
            tenant_id=tenant_id
        ).first()
    
    def sync_pull_request_status(self, tenant_id: str, pr_id: str) -> PullRequest:
        \"\"\"Sync pull request status with the platform.\"\"\"
        try:
            # Get pull request
            pull_request = self.get_pull_request(tenant_id, pr_id)
            if not pull_request:
                raise ValueError("Pull request not found")
            
            # Get repository configuration
            repo_config = RepositoryConfig.query.get(pull_request.repo_config_id)
            if not repo_config:
                raise ValueError("Repository configuration not found")
            
            # Get status from platform
            platform_status = self._get_platform_pr_status(
                repo_config,
                pull_request.pr_number
            )
            
            # Update local status
            pull_request.status = platform_status.get('status', pull_request.status)
            pull_request.pr_metadata.update(platform_status)
            db.session.commit()
            
            return pull_request
            
        except Exception as e:
            logger.error(f"Failed to sync pull request status: {str(e)}")
            raise
    
    def _get_issue_data(self, tenant_id: str, issue_key: str) -> dict:
        \"\"\"Get issue data from cache or JIRA.\"\"\"
        # Check cache first
        issue_cache = IssueCache.query.filter_by(
            tenant_id=tenant_id,
            issue_key=issue_key
        ).first()
        
        if issue_cache:
            return issue_cache.json_data
        
        # TODO: Implement JIRA fetching if not in cache
        # For now, return mock data
        return {
            'key': issue_key,
            'summary': f'Issue {issue_key}',
            'description': f'Description for issue {issue_key}'
        }
    
    def _create_feature_branch(self, repo_config: RepositoryConfig, issue_key: str, 
                              branch_name: Optional[str] = None) -> str:
        \"\"\"Create a feature branch for the issue.\"\"\"
        self._init_config()
        
        if not branch_name:
            branch_name = f"feature/{issue_key.lower()}"
        
        # TODO: Implement actual git operations
        # For now, return the branch name
        return branch_name
    
    def _generate_pr_description(self, issue_data: dict, template_name: str = 'default') -> str:
        \"\"\"Generate PR description from template.\"\"\"
        self._init_config()
        
        # Basic template
        template = Template(\"\"\"
## Issue
$issue_key - $issue_summary

## Description
$issue_description

## Type of Change
- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)

## Testing
- [ ] Unit tests pass
- [ ] Integration tests pass
- [ ] Manual testing completed

## Checklist
- [ ] My code follows the project's style guidelines
- [ ] I have performed a self-review of my own code
- [ ] I have commented my code, particularly in hard-to-understand areas
- [ ] I have made corresponding changes to the documentation
- [ ] My changes generate no new warnings
\"\"\")
        
        return template.substitute(
            issue_key=issue_data.get('key', ''),
            issue_summary=issue_data.get('summary', ''),
            issue_description=issue_data.get('description', '')
        )
    
    def _create_platform_pr(self, repo_config: RepositoryConfig, title: str, 
                           description: str, source_branch: str, target_branch: str) -> dict:
        \"\"\"Create PR on the platform (GitHub/Bitbucket).\"\"\"
        if repo_config.repo_type == 'github':
            return self._create_github_pr(repo_config, title, description, source_branch, target_branch)
        elif repo_config.repo_type == 'bitbucket':
            return self._create_bitbucket_pr(repo_config, title, description, source_branch, target_branch)
        else:
            raise ValueError(f"Unsupported repository type: {repo_config.repo_type}")
    
    def _create_github_pr(self, repo_config: RepositoryConfig, title: str, 
                         description: str, source_branch: str, target_branch: str) -> dict:
        \"\"\"Create PR on GitHub.\"\"\"
        # TODO: Implement actual GitHub API call
        # For now, return mock data
        return {
            'number': 123,
            'url': f'https://github.com/{repo_config.remote_url.split("/")[-2]}/{repo_config.repo_name}/pull/123',
            'status': 'open',
            'platform': 'github'
        }
    
    def _create_bitbucket_pr(self, repo_config: RepositoryConfig, title: str, 
                            description: str, source_branch: str, target_branch: str) -> dict:
        \"\"\"Create PR on Bitbucket.\"\"\"
        # TODO: Implement actual Bitbucket API call
        # For now, return mock data
        return {
            'number': 456,
            'url': f'https://bitbucket.org/{repo_config.remote_url.split("/")[-2]}/{repo_config.repo_name}/pull-requests/456',
            'status': 'open',
            'platform': 'bitbucket'
        }
    
    def _get_platform_pr_status(self, repo_config: RepositoryConfig, pr_number: int) -> dict:
        \"\"\"Get PR status from the platform.\"\"\"
        if repo_config.repo_type == 'github':
            return self._get_github_pr_status(repo_config, pr_number)
        elif repo_config.repo_type == 'bitbucket':
            return self._get_bitbucket_pr_status(repo_config, pr_number)
        else:
            raise ValueError(f"Unsupported repository type: {repo_config.repo_type}")
    
    def _get_github_pr_status(self, repo_config: RepositoryConfig, pr_number: int) -> dict:
        \"\"\"Get PR status from GitHub.\"\"\"
        # TODO: Implement actual GitHub API call
        # For now, return mock data
        return {
            'status': 'open',
            'merged': False,
            'comments': 0,
            'reviews': 0
        }
    
    def _get_bitbucket_pr_status(self, repo_config: RepositoryConfig, pr_number: int) -> dict:
        \"\"\"Get PR status from Bitbucket.\"\"\"
        # TODO: Implement actual Bitbucket API call
        # For now, return mock data
        return {
            'status': 'open',
            'merged': False,
            'comments': 0,
            'reviews': 0
        }
"""

with open('services/pr_service.py', 'w') as f:
    f.write(pr_service_content)

print("Restored pr_service.py to working state!")

# Let me also revert the repository service to a simple working state
repo_service_content = """\"\"\"Repository management service for handling git operations and repository configurations.\"\"\"
import os
import json
import subprocess
import logging
from typing import Optional, Dict, List, Tuple
from datetime import datetime
import uuid

from app import db
from models import RepositoryConfig, RepositoryActivity
from flask import current_app
from utils import encrypt_data, decrypt_data

logger = logging.getLogger(__name__)


class RepositoryService:
    \"\"\"Service for managing repository configurations and operations.\"\"\"
    
    def __init__(self):
        self.base_path = None
        self.git_timeout = None
    
    def _init_config(self):
        \"\"\"Initialize configuration from Flask app context.\"\"\"
        if self.base_path is None:
            self.base_path = current_app.config.get('REPO_BASE_PATH', '/var/app/repositories')
            self.git_timeout = current_app.config.get('GIT_TIMEOUT_SECONDS', 30)
            # Ensure base path exists
            os.makedirs(self.base_path, exist_ok=True)
    
    def add_repository(self, tenant_id: str, user_id: str, repo_data: dict) -> RepositoryConfig:
        \"\"\"Add a new repository configuration.\"\"\"
        try:
            # Validate repository type
            if repo_data['repo_type'] not in ['github', 'bitbucket']:
                raise ValueError("Invalid repository type. Must be 'github' or 'bitbucket'")
            
            # Check if repository already exists
            existing_repo = RepositoryConfig.query.filter_by(
                tenant_id=tenant_id,
                repo_name=repo_data['repo_name']
            ).first()
            
            if existing_repo:
                raise ValueError(f"Repository '{repo_data['repo_name']}' already exists for this tenant")
            
            # Create local path
            local_path = self._get_local_path(tenant_id, repo_data['repo_name'])
            
            # Encrypt credentials
            credentials = {
                'token': repo_data.get('token'),
                'username': repo_data.get('username'),  # For Bitbucket
                'app_password': repo_data.get('app_password')  # For Bitbucket
            }
            # encrypt_data returns a tuple (nonce, ciphertext)
            nonce, ciphertext = encrypt_data(json.dumps(credentials))
            encrypted_credentials = nonce + ciphertext  # Combine them as expected by the model
            
            # Create repository configuration
            repo_config = RepositoryConfig(
                tenant_id=uuid.UUID(tenant_id) if isinstance(tenant_id, str) else tenant_id,
                repo_name=repo_data['repo_name'],
                repo_type=repo_data['repo_type'],
                local_path=local_path,
                remote_url=repo_data['remote_url'],
                default_branch=repo_data.get('default_branch', 'main'),
                credentials_encrypted=encrypted_credentials,
                created_by=uuid.UUID(user_id) if isinstance(user_id, str) else user_id,
                is_active=True
            )
            
            db.session.add(repo_config)
            db.session.commit()
            
            # Clone repository
            self._clone_repository(repo_config)
            
            # Log activity
            self._log_activity(
                tenant_id=tenant_id,
                repo_config_id=repo_config.id,
                activity_type='clone',
                status='success',
                user_id=user_id,
                details={'message': 'Repository cloned successfully'}
            )
            
            return repo_config
            
        except Exception as e:
            logger.error(f"Failed to add repository: {str(e)}")
            db.session.rollback()
            
            # Log failed activity if repo_config was created
            if 'repo_config' in locals():
                self._log_activity(
                    tenant_id=tenant_id,
                    repo_config_id=repo_config.id,
                    activity_type='clone',
                    status='failed',
                    user_id=user_id,
                    details={'error': str(e)}
                )
            raise
    
    def get_repositories(self, tenant_id: str) -> List[RepositoryConfig]:
        \"\"\"Get all repositories for a tenant.\"\"\"
        return RepositoryConfig.query.filter_by(
            tenant_id=tenant_id,
            is_active=True
        ).order_by(RepositoryConfig.created_at.desc()).all()
    
    def get_repository(self, tenant_id: str, repo_id: str) -> Optional[RepositoryConfig]:
        \"\"\"Get a specific repository configuration.\"\"\"
        return RepositoryConfig.query.filter_by(
            id=repo_id,
            tenant_id=tenant_id
        ).first()
    
    def update_repository(self, tenant_id: str, repo_id: str, user_id: str, update_data: dict) -> RepositoryConfig:
        \"\"\"Update repository configuration.\"\"\"
        try:
            repo_config = self.get_repository(tenant_id, repo_id)
            if not repo_config:
                raise ValueError("Repository not found")
            
            # Update fields if provided
            if 'default_branch' in update_data:
                repo_config.default_branch = update_data['default_branch']
            
            if 'token' in update_data or 'username' in update_data or 'app_password' in update_data:
                # Get existing credentials
                existing_creds = json.loads(decrypt_data(repo_config.credentials_encrypted))
                
                # Update credentials
                if 'token' in update_data:
                    existing_creds['token'] = update_data['token']
                if 'username' in update_data:
                    existing_creds['username'] = update_data['username']
                if 'app_password' in update_data:
                    existing_creds['app_password'] = update_data['app_password']
                
                # Re-encrypt
                nonce, ciphertext = encrypt_data(json.dumps(existing_creds))
                repo_config.credentials_encrypted = nonce + ciphertext
            
            repo_config.updated_at = datetime.utcnow()
            db.session.commit()
            
            # Log activity
            self._log_activity(
                tenant_id=tenant_id,
                repo_config_id=repo_config.id,
                activity_type='update',
                status='success',
                user_id=user_id,
                details={'updated_fields': list(update_data.keys())}
            )
            
            return repo_config
            
        except Exception as e:
            logger.error(f"Failed to update repository: {str(e)}")
            db.session.rollback()
            raise
    
    def delete_repository(self, tenant_id: str, repo_id: str, user_id: str) -> bool:
        \"\"\"Delete (deactivate) a repository.\"\"\"
        try:
            repo_config = self.get_repository(tenant_id, repo_id)
            if not repo_config:
                raise ValueError("Repository not found")
            
            # Soft delete
            repo_config.is_active = False
            db.session.commit()
            
            # Log activity
            self._log_activity(
                tenant_id=tenant_id,
                repo_config_id=repo_config.id,
                activity_type='delete',
                status='success',
                user_id=user_id,
                details={'message': 'Repository deactivated'}
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete repository: {str(e)}")
            db.session.rollback()
            raise
    
    def sync_repository(self, tenant_id: str, repo_id: str, user_id: str) -> bool:
        \"\"\"Sync (pull) repository from remote.\"\"\"
        try:
            self._init_config()
            
            repo_config = self.get_repository(tenant_id, repo_id)
            if not repo_config:
                raise ValueError("Repository not found")
            
            # Pull from remote
            self._pull_repository(repo_config)
            
            # Log activity
            self._log_activity(
                tenant_id=tenant_id,
                repo_config_id=repo_config.id,
                activity_type='sync',
                status='success',
                user_id=user_id,
                details={'message': 'Repository synchronized successfully'}
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to sync repository: {str(e)}")
            # Log failure
            self._log_activity(
                tenant_id=tenant_id,
                repo_config_id=repo_config.id,
                activity_type='sync',
                status='failed',
                user_id=user_id,
                details={'error': str(e)}
            )
            raise
    
    def test_connection(self, tenant_id: str, repo_id: str, user_id: str) -> Dict[str, bool]:
        \"\"\"Test repository connection and credentials.\"\"\"
        try:
            repo_config = self.get_repository(tenant_id, repo_id)
            if not repo_config:
                raise ValueError("Repository not found")
            
            # Test based on repository type
            if repo_config.repo_type == 'github':
                valid = self._validate_github_credentials(repo_config)
            else:
                valid = self._validate_bitbucket_credentials(repo_config)
            
            # Log activity
            self._log_activity(
                tenant_id=tenant_id,
                repo_config_id=repo_config.id,
                activity_type='test_connection',
                status='success' if valid else 'failed',
                user_id=user_id,
                details={'valid': valid}
            )
            
            return {'valid': valid}
            
        except Exception as e:
            logger.error(f"Failed to test connection: {str(e)}")
            raise
    
    def get_repository_activity(self, tenant_id: str, repo_id: str, limit: int = 50) -> List[RepositoryActivity]:
        \"\"\"Get repository activity logs.\"\"\"
        return RepositoryActivity.query.filter_by(
            tenant_id=tenant_id,
            repo_config_id=repo_id
        ).order_by(RepositoryActivity.created_at.desc()).limit(limit).all()
    
    def _get_local_path(self, tenant_id: str, repo_name: str) -> str:
        \"\"\"Generate local path for repository.\"\"\"
        self._init_config()
        tenant_path = os.path.join(self.base_path, str(tenant_id))
        os.makedirs(tenant_path, exist_ok=True)
        return os.path.join(tenant_path, repo_name)
    
    def _clone_repository(self, repo_config: RepositoryConfig) -> None:
        \"\"\"Clone repository from remote.\"\"\"
        # TODO: Implement actual git clone operation
        # For now, just create the directory
        os.makedirs(repo_config.local_path, exist_ok=True)
    
    def _pull_repository(self, repo_config: RepositoryConfig) -> None:
        \"\"\"Pull latest changes from remote.\"\"\"
        # TODO: Implement actual git pull operation
        pass
    
    def _validate_github_credentials(self, repo_config: RepositoryConfig) -> bool:
        \"\"\"Validate GitHub credentials.\"\"\"
        # TODO: Implement actual GitHub API validation
        # For now, check if token exists
        credentials = json.loads(decrypt_data(repo_config.credentials_encrypted))
        return bool(credentials.get('token'))
    
    def _validate_bitbucket_credentials(self, repo_config: RepositoryConfig) -> bool:
        \"\"\"Validate Bitbucket credentials.\"\"\"
        # TODO: Implement actual Bitbucket API validation
        # For now, check if username and app_password exist
        credentials = json.loads(decrypt_data(repo_config.credentials_encrypted))
        username = credentials.get('username')
        app_password = credentials.get('app_password')
        return bool(username and app_password)
    
    def _log_activity(self, tenant_id: str, repo_config_id: str, activity_type: str, 
                     status: str, user_id: str, details: dict) -> None:
        \"\"\"Log repository activity.\"\"\"
        try:
            activity = RepositoryActivity(
                tenant_id=uuid.UUID(tenant_id) if isinstance(tenant_id, str) else tenant_id,
                repo_config_id=repo_config_id,
                activity_type=activity_type,
                status=status,
                user_id=uuid.UUID(user_id) if isinstance(user_id, str) else user_id,
                details=details
            )
            db.session.add(activity)
            db.session.commit()
        except Exception as e:
            logger.error(f"Failed to log activity: {str(e)}")
            # Don't raise - this is auxiliary functionality
"""

with open('services/repository_service.py', 'w') as f:
    f.write(repo_service_content)

print("Restored repository_service.py to working state!")