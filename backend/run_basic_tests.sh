#!/bin/bash
# Script to run basic backend tests using unittest (no dependencies)

# Navigate to the backend directory
cd "$(dirname "$0")"

# Set environment variable for testing
export FLASK_ENV=testing
export APPLICATION_AES_KEY="00112233445566778899aabbccddeeff00112233445566778899aabbccddeeff"

# Run the basic tests using unittest
echo "Running basic tests..."
python -m unittest tests/test_basic.py

# Exit with the unittest exit code
exit $?