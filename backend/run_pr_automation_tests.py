#!/usr/bin/env python3
"""Run PR automation tests without starting the server."""
import sys
import os
import subprocess

# Ensure we're in the backend directory
backend_dir = os.path.dirname(os.path.abspath(__file__))
os.chdir(backend_dir)

# Set required environment variables for testing
os.environ['APPLICATION_AES_KEY'] = '00112233445566778899aabbccddeeff00112233445566778899aabbccddeeff'
os.environ['TESTING'] = 'True'

# Run specific PR automation test files
test_files = [
    'tests/test_repositories.py',
    'tests/test_pull_requests.py'
]

print("Running PR Automation Tests")
print("=" * 50)

# Run tests with pytest
cmd = ['python', '-m', 'pytest', '-v', '--tb=short'] + test_files

# Add coverage if requested
if '--coverage' in sys.argv:
    cmd.extend(['--cov=services', '--cov-report=term-missing'])

# Add any additional pytest args
for arg in sys.argv[1:]:
    if arg != '--coverage':
        cmd.append(arg)

# Execute the tests
result = subprocess.run(cmd)

# Exit with the same code as pytest
sys.exit(result.returncode)