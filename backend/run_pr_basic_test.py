#!/usr/bin/env python3
"""Run basic test to debug the issue."""
import os
import sys
import subprocess

# Set environment variables
os.environ['TESTING'] = 'True'
os.environ['APPLICATION_AES_KEY'] = '00112233445566778899aabbccddeeff00112233445566778899aabbccddeeff'

# Run with pytest
result = subprocess.run(['python', '-m', 'pytest', '-v', 'tests/test_repositories_simple.py'], 
                       capture_output=False)
sys.exit(result.returncode)