#!/bin/bash
# Script to run the backend tests

# Navigate to the backend directory
cd "$(dirname "$0")"

# Set environment variable for testing
export FLASK_ENV=testing
export APPLICATION_AES_KEY="00112233445566778899aabbccddeeff00112233445566778899aabbccddeeff"

# Check if we're in a virtual environment
if [[ -z "$VIRTUAL_ENV" ]]; then
    echo "Creating a virtual environment for testing..."
    if [ ! -d "venv" ]; then
        python3 -m venv venv
    fi
    
    echo "Activating virtual environment..."
    source venv/bin/activate
fi

# Install test dependencies
echo "Installing dependencies..."
python -m pip install -r requirements.txt

# Run the tests with coverage
echo "Running tests..."
python -m pytest -v --cov=. --cov-report=term-missing

# Store the exit code
TEST_EXIT_CODE=$?

# Deactivate virtual environment if we created it
if [[ "$VIRTUAL_ENV" == *"/venv" ]]; then
    deactivate
fi

# Exit with the pytest exit code
exit $TEST_EXIT_CODE