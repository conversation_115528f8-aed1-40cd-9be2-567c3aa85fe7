#!/usr/bin/env python
"""
Script to run pytest with proper environment setup.
Usage:
    python runtests.py [pytest arguments]
"""

import sys
import os
import pathlib
import subprocess
from dotenv import load_dotenv

def main():
    """Load environment variables and run pytest with provided arguments."""
    # Load test environment variables
    test_env_path = pathlib.Path(__file__).parent / '.env.test'
    load_dotenv(test_env_path)
    
    # Build pytest command
    pytest_args = ['python', '-m', 'pytest']
    
    # Add any arguments passed to this script
    if len(sys.argv) > 1:
        pytest_args.extend(sys.argv[1:])
    else:
        # Default to verbose output and showing all test details
        pytest_args.extend(['-v'])
    
    # Print command being run
    print(f"Running: {' '.join(pytest_args)}")
    
    # Run pytest with the environment variables loaded
    result = subprocess.run(pytest_args)
    sys.exit(result.returncode)

if __name__ == '__main__':
    main()