from app import create_app, db
from models import Tenant, User
from utils import hash_password
import uuid

app = create_app()

with app.app_context():
    # Check if a default tenant already exists to prevent duplicates if script is run multiple times
    default_tenant_name = "DefaultTenant"
    tenant = Tenant.query.filter_by(name=default_tenant_name).first()
    if not tenant:
        tenant_id = uuid.uuid4()
        tenant = Tenant(id=tenant_id, name=default_tenant_name, plan="free")
        db.session.add(tenant)
        print(f"Tenant '{default_tenant_name}' created with ID: {tenant_id}")
    else:
        print(f"Tenant '{default_tenant_name}' already exists with ID: {tenant.id}")

    # Check if a default user already exists for this tenant
    default_user_email = "<EMAIL>"
    user = User.query.filter_by(email=default_user_email, tenant_id=tenant.id).first()
    if not user:
        user_password = "password123"
        user = User(
            tenant_id=tenant.id,
            email=default_user_email,
            password_hash=hash_password(user_password), # Hash the password
            role="tenant_admin" # Or 'user', make this user a tenant admin for testing
        )
        db.session.add(user)
        print(f"User '{default_user_email}' created for tenant '{tenant.name}' with password '{user_password}'")
    else:
        print(f"User '{default_user_email}' already exists for tenant '{tenant.name}'")
    
    try:
        db.session.commit()
        print("Seed data committed.")
    except Exception as e:
        db.session.rollback()
        print(f"Error committing seed data: {e}")

print("Database seeding process finished.") 