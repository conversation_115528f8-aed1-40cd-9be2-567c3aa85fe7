"""
Service for managing functional areas - groups of related JIRA projects and repositories.
"""

from typing import List, Dict, Optional, Any
from sqlalchemy.orm import joinedload
from extensions import db
from models import (
    FunctionalArea, FunctionalAreaProject, FunctionalAreaRepository,
    Repository, ProjectRepository, User
)
import uuid
from datetime import datetime


class FunctionalAreaService:
    
    @staticmethod
    def create_functional_area(tenant_id: str, user_id: str, data: Dict[str, Any]) -> FunctionalArea:
        """Create a new functional area."""
        functional_area = FunctionalArea(
            tenant_id=tenant_id,
            name=data['name'],
            description=data.get('description'),
            category=data.get('category'),
            priority=data.get('priority', 'medium'),
            created_by=user_id,
            metadata_json=data.get('metadata', {})
        )
        
        db.session.add(functional_area)
        db.session.commit()
        return functional_area
    
    @staticmethod
    def get_functional_areas(tenant_id: str) -> List[FunctionalArea]:
        """Get all functional areas for a tenant."""
        return FunctionalArea.query.filter_by(
            tenant_id=tenant_id, 
            is_active=True
        ).options(
            joinedload(FunctionalArea.project_links),
            joinedload(FunctionalArea.repository_links).joinedload(FunctionalAreaRepository.repository)
        ).order_by(FunctionalArea.name).all()
    
    @staticmethod
    def get_functional_area(tenant_id: str, area_id: str) -> Optional[FunctionalArea]:
        """Get a specific functional area with all its relationships."""
        return FunctionalArea.query.filter_by(
            id=area_id,
            tenant_id=tenant_id,
            is_active=True
        ).options(
            joinedload(FunctionalArea.project_links),
            joinedload(FunctionalArea.repository_links).joinedload(FunctionalAreaRepository.repository)
        ).first()
    
    @staticmethod
    def update_functional_area(tenant_id: str, area_id: str, data: Dict[str, Any]) -> Optional[FunctionalArea]:
        """Update a functional area."""
        area = FunctionalArea.query.filter_by(
            id=area_id,
            tenant_id=tenant_id,
            is_active=True
        ).first()
        
        if not area:
            return None
            
        # Update fields
        if 'name' in data:
            area.name = data['name']
        if 'description' in data:
            area.description = data['description']
        if 'category' in data:
            area.category = data['category']
        if 'priority' in data:
            area.priority = data['priority']
        if 'metadata' in data:
            area.metadata_json = data['metadata']
            
        area.updated_at = datetime.utcnow()
        db.session.commit()
        return area
    
    @staticmethod
    def delete_functional_area(tenant_id: str, area_id: str) -> bool:
        """Soft delete a functional area."""
        area = FunctionalArea.query.filter_by(
            id=area_id,
            tenant_id=tenant_id,
            is_active=True
        ).first()
        
        if not area:
            return False
            
        area.is_active = False
        area.updated_at = datetime.utcnow()
        db.session.commit()
        return True
    
    @staticmethod
    def link_project_to_area(tenant_id: str, area_id: str, project_key: str, 
                           user_id: str, project_role: str = None, is_primary: bool = False) -> bool:
        """Link a JIRA project to a functional area."""
        # Check if functional area exists and belongs to tenant
        area = FunctionalArea.query.filter_by(
            id=area_id,
            tenant_id=tenant_id,
            is_active=True
        ).first()
        
        if not area:
            return False
        
        # Check if link already exists
        existing_link = FunctionalAreaProject.query.filter_by(
            tenant_id=tenant_id,
            functional_area_id=area_id,
            project_key=project_key
        ).first()
        
        if existing_link:
            return False
        
        # If this is marked as primary, unset other primary projects
        if is_primary:
            FunctionalAreaProject.query.filter_by(
                tenant_id=tenant_id,
                functional_area_id=area_id,
                is_primary=True
            ).update({'is_primary': False})
        
        # Create the link
        link = FunctionalAreaProject(
            tenant_id=tenant_id,
            functional_area_id=area_id,
            project_key=project_key,
            project_role=project_role,
            linked_by=user_id,
            is_primary=is_primary
        )
        
        db.session.add(link)
        db.session.commit()
        return True
    
    @staticmethod
    def unlink_project_from_area(tenant_id: str, area_id: str, project_key: str) -> bool:
        """Unlink a JIRA project from a functional area."""
        link = FunctionalAreaProject.query.filter_by(
            tenant_id=tenant_id,
            functional_area_id=area_id,
            project_key=project_key
        ).first()
        
        if not link:
            return False
        
        db.session.delete(link)
        db.session.commit()
        return True
    
    @staticmethod
    def link_repository_to_area(tenant_id: str, area_id: str, repository_id: str,
                               user_id: str, repository_role: str = None, is_primary: bool = False) -> bool:
        """Link a repository to a functional area."""
        # Check if functional area exists and belongs to tenant
        area = FunctionalArea.query.filter_by(
            id=area_id,
            tenant_id=tenant_id,
            is_active=True
        ).first()
        
        if not area:
            return False
        
        # Check if repository exists and belongs to tenant
        repository = Repository.query.filter_by(
            id=repository_id,
            tenant_id=tenant_id
        ).first()
        
        if not repository:
            return False
        
        # Check if link already exists
        existing_link = FunctionalAreaRepository.query.filter_by(
            tenant_id=tenant_id,
            functional_area_id=area_id,
            repository_id=repository_id
        ).first()
        
        if existing_link:
            return False
        
        # If this is marked as primary, unset other primary repositories
        if is_primary:
            FunctionalAreaRepository.query.filter_by(
                tenant_id=tenant_id,
                functional_area_id=area_id,
                is_primary=True
            ).update({'is_primary': False})
        
        # Create the link
        link = FunctionalAreaRepository(
            tenant_id=tenant_id,
            functional_area_id=area_id,
            repository_id=repository_id,
            repository_role=repository_role,
            linked_by=user_id,
            is_primary=is_primary
        )
        
        db.session.add(link)
        db.session.commit()
        return True
    
    @staticmethod
    def unlink_repository_from_area(tenant_id: str, area_id: str, repository_id: str) -> bool:
        """Unlink a repository from a functional area."""
        link = FunctionalAreaRepository.query.filter_by(
            tenant_id=tenant_id,
            functional_area_id=area_id,
            repository_id=repository_id
        ).first()
        
        if not link:
            return False
        
        db.session.delete(link)
        db.session.commit()
        return True
    
    @staticmethod
    def get_functional_areas_for_project(tenant_id: str, project_key: str) -> List[FunctionalArea]:
        """Get all functional areas that contain a specific project."""
        links = FunctionalAreaProject.query.filter_by(
            tenant_id=tenant_id,
            project_key=project_key
        ).all()
        
        area_ids = [link.functional_area_id for link in links]
        
        return FunctionalArea.query.filter(
            FunctionalArea.id.in_(area_ids),
            FunctionalArea.tenant_id == tenant_id,
            FunctionalArea.is_active == True
        ).all()
    
    @staticmethod
    def get_functional_areas_for_repository(tenant_id: str, repository_id: str) -> List[FunctionalArea]:
        """Get all functional areas that contain a specific repository."""
        links = FunctionalAreaRepository.query.filter_by(
            tenant_id=tenant_id,
            repository_id=repository_id
        ).all()
        
        area_ids = [link.functional_area_id for link in links]
        
        return FunctionalArea.query.filter(
            FunctionalArea.id.in_(area_ids),
            FunctionalArea.tenant_id == tenant_id,
            FunctionalArea.is_active == True
        ).all()
    
    @staticmethod
    def get_projects_in_area(tenant_id: str, area_id: str) -> List[Dict[str, Any]]:
        """Get all projects linked to a functional area."""
        links = FunctionalAreaProject.query.filter_by(
            tenant_id=tenant_id,
            functional_area_id=area_id
        ).order_by(FunctionalAreaProject.is_primary.desc(), FunctionalAreaProject.project_key).all()
        
        return [{
            'project_key': link.project_key,
            'project_role': link.project_role,
            'is_primary': link.is_primary,
            'linked_at': link.linked_at.isoformat() if link.linked_at else None
        } for link in links]
    
    @staticmethod
    def get_repositories_in_area(tenant_id: str, area_id: str) -> List[Dict[str, Any]]:
        """Get all repositories linked to a functional area."""
        links = FunctionalAreaRepository.query.filter_by(
            tenant_id=tenant_id,
            functional_area_id=area_id
        ).options(
            joinedload(FunctionalAreaRepository.repository)
        ).order_by(FunctionalAreaRepository.is_primary.desc(), FunctionalAreaRepository.repository_role).all()
        
        return [{
            'repository_id': str(link.repository_id),
            'repository_name': link.repository.name,
            'repository_full_name': link.repository.full_name,
            'repository_role': link.repository_role,
            'is_primary': link.is_primary,
            'linked_at': link.linked_at.isoformat() if link.linked_at else None,
            'clone_url': link.repository.clone_url,
            'default_branch': link.repository.default_branch
        } for link in links]
    
    @staticmethod
    def get_functional_area_summary(tenant_id: str) -> Dict[str, Any]:
        """Get a summary of functional areas for dashboard/overview."""
        areas = FunctionalArea.query.filter_by(
            tenant_id=tenant_id,
            is_active=True
        ).all()
        
        total_areas = len(areas)
        
        # Count projects and repositories across all areas
        total_project_links = FunctionalAreaProject.query.filter_by(tenant_id=tenant_id).count()
        total_repo_links = FunctionalAreaRepository.query.filter_by(tenant_id=tenant_id).count()
        
        # Category breakdown
        category_counts = {}
        for area in areas:
            category = area.category or 'Uncategorized'
            category_counts[category] = category_counts.get(category, 0) + 1
        
        return {
            'total_functional_areas': total_areas,
            'total_project_links': total_project_links,
            'total_repository_links': total_repo_links,
            'category_breakdown': category_counts,
            'areas': [{
                'id': str(area.id),
                'name': area.name,
                'category': area.category,
                'priority': area.priority,
                'project_count': len(area.project_links),
                'repository_count': len(area.repository_links),
                'created_at': area.created_at.isoformat() if area.created_at else None
            } for area in areas]
        }