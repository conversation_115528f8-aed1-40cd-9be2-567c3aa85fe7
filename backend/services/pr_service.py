"""Pull Request creation and management service."""
import os
import json
import logging
from typing import Optional, Dict, List
from datetime import datetime
import uuid
import requests
from string import Template

from app import db
from models import PullRequest, RepositoryConfig, IssueCache
from flask import current_app
from utils import decrypt_data

logger = logging.getLogger(__name__)


class PullRequestService:
    """Service for managing pull requests."""
    
    def __init__(self):
        self.repo_base_path = None
        self.git_timeout = None
        self.pr_template_path = None
    
    def _init_config(self):
        """Initialize configuration from Flask app context."""
        if self.repo_base_path is None:
            self.repo_base_path = current_app.config.get('REPO_BASE_PATH', '/var/app/repositories')
            self.git_timeout = current_app.config.get('GIT_TIMEOUT_SECONDS', 30)
            self.pr_template_path = current_app.config.get('PR_TEMPLATE_PATH', '/var/app/templates/pr')
    
    def create_pull_request(self, tenant_id: str, user_id: str, pr_data: dict) -> PullRequest:
        """Create a new pull request."""
        try:
            # Get repository configuration
            repo_config = RepositoryConfig.query.filter_by(
                id=uuid.UUID(pr_data['repo_config_id']) if isinstance(pr_data['repo_config_id'], str) else pr_data['repo_config_id'],
                tenant_id=uuid.UUID(tenant_id) if isinstance(tenant_id, str) else tenant_id,
                is_active=True
            ).first()
            
            if not repo_config:
                raise ValueError("Repository not found or inactive")
            
            # Get issue details from cache or JIRA
            issue_data = self._get_issue_data(tenant_id, pr_data['issue_key'])
            
            # Create feature branch
            source_branch = self._create_feature_branch(
                repo_config, 
                pr_data['issue_key'],
                pr_data.get('branch_name')
            )
            
            # Generate PR description from template
            description = self._generate_pr_description(
                issue_data,
                pr_data.get('description_template', 'default')
            )
            
            # Create PR record in database
            pull_request = PullRequest(
                tenant_id=tenant_id,
                repo_config_id=repo_config.id,
                issue_key=pr_data['issue_key'],
                title=pr_data.get('title') or f"Fix: {issue_data.get('summary', pr_data['issue_key'])}",
                description=description,
                source_branch=source_branch,
                target_branch=pr_data.get('target_branch', repo_config.default_branch),
                status='pending',
                created_by=uuid.UUID(user_id) if isinstance(user_id, str) else user_id,
                pr_metadata={}
            )
            
            db.session.add(pull_request)
            db.session.commit()
            
            # Create PR on the platform
            platform_pr = self._create_platform_pr(
                repo_config,
                pull_request.title,
                description,
                source_branch,
                pull_request.target_branch
            )
            
            # Update PR with platform data
            pull_request.pr_number = platform_pr.get('number')
            pull_request.pr_url = platform_pr.get('url')
            pull_request.status = 'created'
            pull_request.pr_metadata = platform_pr
            db.session.commit()
            
            return pull_request
            
        except Exception as e:
            logger.error(f"Failed to create pull request: {str(e)}")
            db.session.rollback()
            raise
    
    def get_pull_requests(self, tenant_id: str, filters: dict = None) -> List[PullRequest]:
        """Get pull requests with optional filters."""
        query = PullRequest.query.filter_by(tenant_id=tenant_id)
        
        if filters:
            if 'repo_config_id' in filters:
                query = query.filter_by(repo_config_id=filters['repo_config_id'])
            if 'issue_key' in filters:
                query = query.filter_by(issue_key=filters['issue_key'])
            if 'status' in filters:
                query = query.filter_by(status=filters['status'])
            if 'created_by' in filters:
                query = query.filter_by(created_by=filters['created_by'])
        
        return query.order_by(PullRequest.created_at.desc()).all()
    
    def get_pull_request(self, tenant_id: str, pr_id: str) -> Optional[PullRequest]:
        """Get a specific pull request."""
        return PullRequest.query.filter_by(
            id=pr_id,
            tenant_id=tenant_id
        ).first()
    
    def sync_pull_request_status(self, tenant_id: str, pr_id: str) -> PullRequest:
        """Sync pull request status with the platform."""
        try:
            # Get pull request
            pull_request = self.get_pull_request(tenant_id, pr_id)
            if not pull_request:
                raise ValueError("Pull request not found")
            
            # Get repository configuration
            repo_config = RepositoryConfig.query.get(pull_request.repo_config_id)
            if not repo_config:
                raise ValueError("Repository configuration not found")
            
            # Get status from platform
            platform_status = self._get_platform_pr_status(
                repo_config,
                pull_request.pr_number
            )
            
            # Update local status
            pull_request.status = platform_status.get('status', pull_request.status)
            pull_request.pr_metadata.update(platform_status)
            db.session.commit()
            
            return pull_request
            
        except Exception as e:
            logger.error(f"Failed to sync pull request status: {str(e)}")
            raise
    
    def _get_issue_data(self, tenant_id: str, issue_key: str) -> dict:
        """Get issue data from cache or JIRA."""
        # Check cache first
        issue_cache = IssueCache.query.filter_by(
            tenant_id=tenant_id,
            issue_key=issue_key
        ).first()
        
        if issue_cache:
            return issue_cache.json_data
        
        # TODO: Implement JIRA fetching if not in cache
        # For now, return mock data
        return {
            'key': issue_key,
            'summary': f'Issue {issue_key}',
            'description': f'Description for issue {issue_key}'
        }
    
    def _create_feature_branch(self, repo_config: RepositoryConfig, issue_key: str, 
                              branch_name: Optional[str] = None) -> str:
        """Create a feature branch for the issue."""
        self._init_config()
        
        if not branch_name:
            branch_name = f"feature/{issue_key.lower()}"
        
        # TODO: Implement actual git operations
        # For now, return the branch name
        return branch_name
    
    def _generate_pr_description(self, issue_data: dict, template_name: str = 'default') -> str:
        """Generate PR description from template."""
        self._init_config()
        
        # Basic template
        template = Template("""
## Issue
$issue_key - $issue_summary

## Description
$issue_description

## Type of Change
- [ ] Bug fix (non-breaking change which fixes an issue)
- [ ] New feature (non-breaking change which adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)

## Testing
- [ ] Unit tests pass
- [ ] Integration tests pass
- [ ] Manual testing completed

## Checklist
- [ ] My code follows the project's style guidelines
- [ ] I have performed a self-review of my own code
- [ ] I have commented my code, particularly in hard-to-understand areas
- [ ] I have made corresponding changes to the documentation
- [ ] My changes generate no new warnings
""")
        
        return template.substitute(
            issue_key=issue_data.get('key', ''),
            issue_summary=issue_data.get('summary', ''),
            issue_description=issue_data.get('description', '')
        )
    
    def _create_platform_pr(self, repo_config: RepositoryConfig, title: str, 
                           description: str, source_branch: str, target_branch: str) -> dict:
        """Create PR on the platform (GitHub/Bitbucket)."""
        if repo_config.repo_type == 'github':
            return self._create_github_pr(repo_config, title, description, source_branch, target_branch)
        elif repo_config.repo_type == 'bitbucket':
            return self._create_bitbucket_pr(repo_config, title, description, source_branch, target_branch)
        else:
            raise ValueError(f"Unsupported repository type: {repo_config.repo_type}")
    
    def _create_github_pr(self, repo_config: RepositoryConfig, title: str, 
                         description: str, source_branch: str, target_branch: str) -> dict:
        """Create PR on GitHub."""
        # TODO: Implement actual GitHub API call
        # For now, return mock data
        return {
            'number': 123,
            'url': f'https://github.com/{repo_config.remote_url.split("/")[-2]}/{repo_config.repo_name}/pull/123',
            'status': 'open',
            'platform': 'github'
        }
    
    def _create_bitbucket_pr(self, repo_config: RepositoryConfig, title: str, 
                            description: str, source_branch: str, target_branch: str) -> dict:
        """Create PR on Bitbucket."""
        # TODO: Implement actual Bitbucket API call
        # For now, return mock data
        return {
            'number': 456,
            'url': f'https://bitbucket.org/{repo_config.remote_url.split("/")[-2]}/{repo_config.repo_name}/pull-requests/456',
            'status': 'open',
            'platform': 'bitbucket'
        }
    
    def _get_platform_pr_status(self, repo_config: RepositoryConfig, pr_number: int) -> dict:
        """Get PR status from the platform."""
        if repo_config.repo_type == 'github':
            return self._get_github_pr_status(repo_config, pr_number)
        elif repo_config.repo_type == 'bitbucket':
            return self._get_bitbucket_pr_status(repo_config, pr_number)
        else:
            raise ValueError(f"Unsupported repository type: {repo_config.repo_type}")
    
    def _get_github_pr_status(self, repo_config: RepositoryConfig, pr_number: int) -> dict:
        """Get PR status from GitHub."""
        # TODO: Implement actual GitHub API call
        # For now, return mock data
        return {
            'status': 'open',
            'merged': False,
            'comments': 0,
            'reviews': 0
        }
    
    def _get_bitbucket_pr_status(self, repo_config: RepositoryConfig, pr_number: int) -> dict:
        """Get PR status from Bitbucket."""
        # TODO: Implement actual Bitbucket API call
        # For now, return mock data
        return {
            'status': 'open',
            'merged': False,
            'comments': 0,
            'reviews': 0
        }
