"""Service for managing repositories and project-repository relationships."""

from typing import List, Optional, Dict
from uuid import UUID
from models import db, Repository, ProjectRepository, UserRepositoryPath
from utils import validate_uuid
from datetime import datetime


class RepositoryManagementService:
    
    @staticmethod
    def create_repository(
        tenant_id: UUID,
        name: str,
        repository_type: str,
        remote_url: str,
        created_by: UUID,
        display_name: Optional[str] = None,
        default_branch: str = 'main',
        description: Optional[str] = None
    ) -> Repository:
        """Create a new repository."""
        tenant_id = validate_uuid(tenant_id)
        created_by = validate_uuid(created_by)
        
        # Check if repository already exists
        existing = Repository.query.filter_by(
            tenant_id=tenant_id,
            name=name
        ).first()
        
        if existing:
            raise ValueError(f"Repository '{name}' already exists")
        
        repository = Repository(
            tenant_id=tenant_id,
            name=name,
            display_name=display_name or name,
            repository_type=repository_type,
            remote_url=remote_url,
            default_branch=default_branch,
            description=description,
            created_by=created_by
        )
        
        db.session.add(repository)
        db.session.commit()
        return repository
    
    @staticmethod
    def update_repository(
        repository_id: UUID,
        tenant_id: UUID,
        **kwargs
    ) -> Repository:
        """Update repository details."""
        repository_id = validate_uuid(repository_id)
        tenant_id = validate_uuid(tenant_id)
        
        repository = Repository.query.filter_by(
            id=repository_id,
            tenant_id=tenant_id
        ).first()
        
        if not repository:
            raise ValueError("Repository not found")
        
        # Update allowed fields
        allowed_fields = ['display_name', 'remote_url', 'default_branch', 'description', 'is_active', 'agent_instruction', 'agent_description', 'enabled_tools', 'tool_configs']
        for field, value in kwargs.items():
            if field in allowed_fields and value is not None:
                setattr(repository, field, value)
        
        repository.updated_at = datetime.utcnow()
        db.session.commit()
        return repository
    
    @staticmethod
    def list_repositories(
        tenant_id: UUID,
        is_active: Optional[bool] = True
    ) -> List[Repository]:
        """List all repositories for a tenant."""
        tenant_id = validate_uuid(tenant_id)
        
        query = Repository.query.filter_by(tenant_id=tenant_id)
        
        if is_active is not None:
            query = query.filter_by(is_active=is_active)
        
        return query.order_by(Repository.name).all()
    
    @staticmethod
    def get_repository(
        repository_id: UUID,
        tenant_id: UUID
    ) -> Optional[Repository]:
        """Get a specific repository."""
        repository_id = validate_uuid(repository_id)
        tenant_id = validate_uuid(tenant_id)
        
        return Repository.query.filter_by(
            id=repository_id,
            tenant_id=tenant_id
        ).first()
    
    @staticmethod
    def link_repository_to_project(
        tenant_id: UUID,
        project_key: str,
        repository_id: UUID,
        linked_by: UUID,
        is_primary: bool = False
    ) -> ProjectRepository:
        """Link a repository to a JIRA project."""
        tenant_id = validate_uuid(tenant_id)
        repository_id = validate_uuid(repository_id)
        linked_by = validate_uuid(linked_by)
        
        # Check if link already exists
        existing = ProjectRepository.query.filter_by(
            tenant_id=tenant_id,
            project_key=project_key,
            repository_id=repository_id
        ).first()
        
        if existing:
            raise ValueError("This repository is already linked to the project")
        
        # If marking as primary, unmark other primary repos for this project
        if is_primary:
            ProjectRepository.query.filter_by(
                tenant_id=tenant_id,
                project_key=project_key,
                is_primary=True
            ).update({'is_primary': False})
        
        link = ProjectRepository(
            tenant_id=tenant_id,
            project_key=project_key,
            repository_id=repository_id,
            linked_by=linked_by,
            is_primary=is_primary
        )
        
        db.session.add(link)
        db.session.commit()
        return link
    
    @staticmethod
    def unlink_repository_from_project(
        tenant_id: UUID,
        project_key: str,
        repository_id: UUID
    ) -> bool:
        """Unlink a repository from a JIRA project."""
        tenant_id = validate_uuid(tenant_id)
        repository_id = validate_uuid(repository_id)
        
        link = ProjectRepository.query.filter_by(
            tenant_id=tenant_id,
            project_key=project_key,
            repository_id=repository_id
        ).first()
        
        if link:
            db.session.delete(link)
            db.session.commit()
            return True
        
        return False
    
    @staticmethod
    def get_project_repositories(
        tenant_id: UUID,
        project_key: str
    ) -> List[Dict]:
        """Get all repositories linked to a project."""
        tenant_id = validate_uuid(tenant_id)
        
        links = ProjectRepository.query.filter_by(
            tenant_id=tenant_id,
            project_key=project_key
        ).all()
        
        result = []
        for link in links:
            repo_data = {
                'repository': link.repository,
                'is_primary': link.is_primary,
                'linked_at': link.linked_at,
                'linked_by': link.linker
            }
            result.append(repo_data)
        
        return result
    
    @staticmethod
    def get_repository_projects(
        tenant_id: UUID,
        repository_id: UUID
    ) -> List[ProjectRepository]:
        """Get all projects linked to a repository."""
        tenant_id = validate_uuid(tenant_id)
        repository_id = validate_uuid(repository_id)
        
        return ProjectRepository.query.filter_by(
            tenant_id=tenant_id,
            repository_id=repository_id
        ).all()
    
    @staticmethod
    def set_user_repository_path(
        tenant_id: UUID,
        user_id: UUID,
        repository_id: UUID,
        local_path: str,
        project_key: Optional[str] = None
    ) -> UserRepositoryPath:
        """Set or update a user's local path for a repository."""
        tenant_id = validate_uuid(tenant_id)
        user_id = validate_uuid(user_id)
        repository_id = validate_uuid(repository_id)
        
        # Find the project-repository link if project_key is provided
        project_repository_id = None
        if project_key:
            project_repo = ProjectRepository.query.filter_by(
                tenant_id=tenant_id,
                project_key=project_key,
                repository_id=repository_id
            ).first()
            if project_repo:
                project_repository_id = project_repo.id
        else:
            # Find any project-repository link for this repository
            project_repo = ProjectRepository.query.filter_by(
                tenant_id=tenant_id,
                repository_id=repository_id
            ).first()
            if project_repo:
                project_repository_id = project_repo.id
        
        if not project_repository_id:
            raise ValueError("Repository is not linked to any project")
        
        # Check if mapping already exists
        existing = UserRepositoryPath.query.filter_by(
            tenant_id=tenant_id,
            user_id=user_id,
            project_repository_id=project_repository_id
        ).first()
        
        if existing:
            existing.local_path = local_path
            existing.is_active = True
            existing.updated_at = datetime.utcnow()
            db.session.commit()
            return existing
        
        # Create new mapping
        path_mapping = UserRepositoryPath(
            tenant_id=tenant_id,
            user_id=user_id,
            project_repository_id=project_repository_id,
            local_path=local_path
        )
        
        db.session.add(path_mapping)
        db.session.commit()
        return path_mapping
    
    @staticmethod
    def get_user_repository_path(
        tenant_id: UUID,
        user_id: UUID,
        repository_id: UUID,
        project_key: Optional[str] = None
    ) -> Optional[str]:
        """Get a user's local path for a repository."""
        tenant_id = validate_uuid(tenant_id)
        user_id = validate_uuid(user_id)
        repository_id = validate_uuid(repository_id)
        
        # Find the project-repository link
        query = db.session.query(UserRepositoryPath).join(
            ProjectRepository,
            UserRepositoryPath.project_repository_id == ProjectRepository.id
        ).filter(
            UserRepositoryPath.tenant_id == tenant_id,
            UserRepositoryPath.user_id == user_id,
            ProjectRepository.repository_id == repository_id,
            UserRepositoryPath.is_active == True
        )
        
        if project_key:
            query = query.filter(ProjectRepository.project_key == project_key)
        
        path_mapping = query.first()
        
        return path_mapping.local_path if path_mapping else None
    
    @staticmethod
    def get_project_user_paths(
        tenant_id: UUID,
        user_id: UUID,
        project_key: str
    ) -> List[Dict]:
        """Get all user paths for repositories linked to a project."""
        tenant_id = validate_uuid(tenant_id)
        user_id = validate_uuid(user_id)
        
        # First get all repositories linked to the project
        project_repos = ProjectRepository.query.filter_by(
            tenant_id=tenant_id,
            project_key=project_key
        ).all()
        
        result = []
        for project_repo in project_repos:
            # Get user path for each repository
            user_path = UserRepositoryPath.query.filter_by(
                user_id=user_id,
                project_repository_id=project_repo.id
            ).first()
            
            result.append({
                'repository': project_repo.repository,
                'is_primary': project_repo.is_primary,
                'local_path': user_path.local_path if user_path else None
            })
        
        return result
    
    @staticmethod
    def delete_repository(
        tenant_id: UUID,
        repository_id: UUID
    ) -> bool:
        """Delete a repository and all its relationships."""
        tenant_id = validate_uuid(tenant_id)
        repository_id = validate_uuid(repository_id)
        
        # Get the repository
        repository = Repository.query.filter_by(
            id=repository_id,
            tenant_id=tenant_id
        ).first()
        
        if not repository:
            raise ValueError("Repository not found")
        
        # Get all project-repository links for this repository
        project_repos = ProjectRepository.query.filter_by(
            tenant_id=tenant_id,
            repository_id=repository_id
        ).all()
        
        # Delete all user paths associated with these project-repository links
        for project_repo in project_repos:
            UserRepositoryPath.query.filter_by(
                tenant_id=tenant_id,
                project_repository_id=project_repo.id
            ).delete()
        
        # Delete all project links
        ProjectRepository.query.filter_by(
            tenant_id=tenant_id,
            repository_id=repository_id
        ).delete()
        
        # Delete the repository itself
        db.session.delete(repository)
        db.session.commit()
        return True
    
    @staticmethod
    def soft_delete_repository(
        tenant_id: UUID,
        repository_id: UUID
    ) -> Repository:
        """Soft delete a repository by marking it as inactive."""
        tenant_id = validate_uuid(tenant_id)
        repository_id = validate_uuid(repository_id)
        
        repository = Repository.query.filter_by(
            id=repository_id,
            tenant_id=tenant_id
        ).first()
        
        if not repository:
            raise ValueError("Repository not found")
        
        repository.is_active = False
        repository.updated_at = datetime.utcnow()
        
        # Get all project-repository links for this repository
        project_repos = ProjectRepository.query.filter_by(
            tenant_id=tenant_id,
            repository_id=repository_id
        ).all()
        
        # Deactivate all user paths associated with these project-repository links
        for project_repo in project_repos:
            UserRepositoryPath.query.filter_by(
                tenant_id=tenant_id,
                project_repository_id=project_repo.id
            ).update({'is_active': False, 'updated_at': datetime.utcnow()})
        
        db.session.commit()
        return repository