"""Repository management service for handling git operations and repository configurations."""
import os
import json
import subprocess
import logging
from typing import Optional, Dict, List, Tuple
from datetime import datetime
import uuid

from app import db
from models import RepositoryConfig, RepositoryActivity
from flask import current_app
from utils import encrypt_data, decrypt_data

logger = logging.getLogger(__name__)


class RepositoryService:
    """Service for managing repository configurations and operations."""
    
    def __init__(self):
        self.base_path = None
        self.git_timeout = None
    
    def _init_config(self):
        """Initialize configuration from Flask app context."""
        if self.base_path is None:
            self.base_path = current_app.config.get('REPO_BASE_PATH', '/var/app/repositories')
            self.git_timeout = current_app.config.get('GIT_TIMEOUT_SECONDS', 30)
            # Ensure base path exists
            os.makedirs(self.base_path, exist_ok=True)
    
    def add_repository(self, tenant_id: str, user_id: str, repo_data: dict) -> RepositoryConfig:
        """Add a new repository configuration."""
        try:
            # Validate repository type
            if repo_data['repo_type'] not in ['github', 'bitbucket']:
                raise ValueError("Invalid repository type. Must be 'github' or 'bitbucket'")
            
            # Check if repository already exists
            existing_repo = RepositoryConfig.query.filter_by(
                tenant_id=tenant_id,
                repo_name=repo_data['repo_name']
            ).first()
            
            if existing_repo:
                raise ValueError(f"Repository '{repo_data['repo_name']}' already exists for this tenant")
            
            # Create local path
            local_path = self._get_local_path(tenant_id, repo_data['repo_name'])
            
            # Encrypt credentials
            credentials = {
                'token': repo_data.get('token'),
                'username': repo_data.get('username'),  # For Bitbucket
                'app_password': repo_data.get('app_password')  # For Bitbucket
            }
            # encrypt_data returns a tuple (nonce, ciphertext)
            nonce, ciphertext = encrypt_data(json.dumps(credentials))
            encrypted_credentials = nonce + ciphertext  # Combine them as expected by the model
            
            # Create repository configuration
            repo_config = RepositoryConfig(
                tenant_id=uuid.UUID(tenant_id) if isinstance(tenant_id, str) else tenant_id,
                repo_name=repo_data['repo_name'],
                repo_type=repo_data['repo_type'],
                local_path=local_path,
                remote_url=repo_data['remote_url'],
                default_branch=repo_data.get('default_branch', 'main'),
                credentials_encrypted=encrypted_credentials,
                created_by=uuid.UUID(user_id) if isinstance(user_id, str) else user_id,
                is_active=True
            )
            
            db.session.add(repo_config)
            db.session.commit()
            
            # Clone repository
            self._clone_repository(repo_config)
            
            # Log activity
            self._log_activity(
                tenant_id=tenant_id,
                repo_config_id=repo_config.id,
                activity_type='clone',
                status='success',
                user_id=user_id,
                details={'message': 'Repository cloned successfully'}
            )
            
            return repo_config
            
        except Exception as e:
            logger.error(f"Failed to add repository: {str(e)}")
            db.session.rollback()
            
            # Log failed activity if repo_config was created
            if 'repo_config' in locals():
                self._log_activity(
                    tenant_id=tenant_id,
                    repo_config_id=repo_config.id,
                    activity_type='clone',
                    status='failed',
                    user_id=user_id,
                    details={'error': str(e)}
                )
            raise
    
    def get_repositories(self, tenant_id: str) -> List[RepositoryConfig]:
        """Get all repositories for a tenant."""
        return RepositoryConfig.query.filter_by(
            tenant_id=tenant_id,
            is_active=True
        ).order_by(RepositoryConfig.created_at.desc()).all()
    
    def get_repository(self, tenant_id: str, repo_id: str) -> Optional[RepositoryConfig]:
        """Get a specific repository configuration."""
        return RepositoryConfig.query.filter_by(
            id=repo_id,
            tenant_id=tenant_id
        ).first()
    
    def update_repository(self, tenant_id: str, repo_id: str, user_id: str, update_data: dict) -> RepositoryConfig:
        """Update repository configuration."""
        try:
            repo_config = self.get_repository(tenant_id, repo_id)
            if not repo_config:
                raise ValueError("Repository not found")
            
            # Update fields if provided
            if 'default_branch' in update_data:
                repo_config.default_branch = update_data['default_branch']
            
            if 'token' in update_data or 'username' in update_data or 'app_password' in update_data:
                # Get existing credentials
                existing_creds = json.loads(decrypt_data(repo_config.credentials_encrypted))
                
                # Update credentials
                if 'token' in update_data:
                    existing_creds['token'] = update_data['token']
                if 'username' in update_data:
                    existing_creds['username'] = update_data['username']
                if 'app_password' in update_data:
                    existing_creds['app_password'] = update_data['app_password']
                
                # Re-encrypt
                nonce, ciphertext = encrypt_data(json.dumps(existing_creds))
                repo_config.credentials_encrypted = nonce + ciphertext
            
            repo_config.updated_at = datetime.utcnow()
            db.session.commit()
            
            # Log activity
            self._log_activity(
                tenant_id=tenant_id,
                repo_config_id=repo_config.id,
                activity_type='update',
                status='success',
                user_id=user_id,
                details={'updated_fields': list(update_data.keys())}
            )
            
            return repo_config
            
        except Exception as e:
            logger.error(f"Failed to update repository: {str(e)}")
            db.session.rollback()
            raise
    
    def delete_repository(self, tenant_id: str, repo_id: str, user_id: str) -> bool:
        """Delete (deactivate) a repository."""
        try:
            repo_config = self.get_repository(tenant_id, repo_id)
            if not repo_config:
                raise ValueError("Repository not found")
            
            # Soft delete
            repo_config.is_active = False
            db.session.commit()
            
            # Log activity
            self._log_activity(
                tenant_id=tenant_id,
                repo_config_id=repo_config.id,
                activity_type='delete',
                status='success',
                user_id=user_id,
                details={'message': 'Repository deactivated'}
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete repository: {str(e)}")
            db.session.rollback()
            raise
    
    def sync_repository(self, tenant_id: str, repo_id: str, user_id: str) -> bool:
        """Sync (pull) repository from remote."""
        try:
            self._init_config()
            
            repo_config = self.get_repository(tenant_id, repo_id)
            if not repo_config:
                raise ValueError("Repository not found")
            
            # Pull from remote
            self._pull_repository(repo_config)
            
            # Log activity
            self._log_activity(
                tenant_id=tenant_id,
                repo_config_id=repo_config.id,
                activity_type='sync',
                status='success',
                user_id=user_id,
                details={'message': 'Repository synchronized successfully'}
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to sync repository: {str(e)}")
            # Log failure
            self._log_activity(
                tenant_id=tenant_id,
                repo_config_id=repo_config.id,
                activity_type='sync',
                status='failed',
                user_id=user_id,
                details={'error': str(e)}
            )
            raise
    
    def test_connection(self, tenant_id: str, repo_id: str, user_id: str) -> Dict[str, bool]:
        """Test repository connection and credentials."""
        try:
            repo_config = self.get_repository(tenant_id, repo_id)
            if not repo_config:
                raise ValueError("Repository not found")
            
            # Test based on repository type
            if repo_config.repo_type == 'github':
                valid = self._validate_github_credentials(repo_config)
            else:
                valid = self._validate_bitbucket_credentials(repo_config)
            
            # Log activity
            self._log_activity(
                tenant_id=tenant_id,
                repo_config_id=repo_config.id,
                activity_type='test_connection',
                status='success' if valid else 'failed',
                user_id=user_id,
                details={'valid': valid}
            )
            
            return {'valid': valid}
            
        except Exception as e:
            logger.error(f"Failed to test connection: {str(e)}")
            raise
    
    def get_repository_activity(self, tenant_id: str, repo_id: str, limit: int = 50) -> List[RepositoryActivity]:
        """Get repository activity logs."""
        return RepositoryActivity.query.filter_by(
            tenant_id=tenant_id,
            repo_config_id=repo_id
        ).order_by(RepositoryActivity.created_at.desc()).limit(limit).all()
    
    def _get_local_path(self, tenant_id: str, repo_name: str) -> str:
        """Generate local path for repository."""
        self._init_config()
        tenant_path = os.path.join(self.base_path, str(tenant_id))
        os.makedirs(tenant_path, exist_ok=True)
        return os.path.join(tenant_path, repo_name)
    
    def _clone_repository(self, repo_config: RepositoryConfig) -> None:
        """Clone repository from remote."""
        # TODO: Implement actual git clone operation
        # For now, just create the directory
        os.makedirs(repo_config.local_path, exist_ok=True)
    
    def _pull_repository(self, repo_config: RepositoryConfig) -> None:
        """Pull latest changes from remote."""
        # TODO: Implement actual git pull operation
        pass
    
    def _validate_github_credentials(self, repo_config: RepositoryConfig) -> bool:
        """Validate GitHub credentials."""
        # TODO: Implement actual GitHub API validation
        # For now, check if token exists
        credentials = json.loads(decrypt_data(repo_config.credentials_encrypted))
        return bool(credentials.get('token'))
    
    def _validate_bitbucket_credentials(self, repo_config: RepositoryConfig) -> bool:
        """Validate Bitbucket credentials."""
        # TODO: Implement actual Bitbucket API validation
        # For now, check if username and app_password exist
        credentials = json.loads(decrypt_data(repo_config.credentials_encrypted))
        username = credentials.get('username')
        app_password = credentials.get('app_password')
        return bool(username and app_password)
    
    def _log_activity(self, tenant_id: str, repo_config_id: str, activity_type: str, 
                     status: str, user_id: str, details: dict) -> None:
        """Log repository activity."""
        try:
            activity = RepositoryActivity(
                tenant_id=uuid.UUID(tenant_id) if isinstance(tenant_id, str) else tenant_id,
                repo_config_id=repo_config_id,
                activity_type=activity_type,
                status=status,
                user_id=uuid.UUID(user_id) if isinstance(user_id, str) else user_id,
                details=details
            )
            db.session.add(activity)
            db.session.commit()
        except Exception as e:
            logger.error(f"Failed to log activity: {str(e)}")
            # Don't raise - this is auxiliary functionality
