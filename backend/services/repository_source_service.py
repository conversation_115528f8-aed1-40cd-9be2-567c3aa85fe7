"""Service for managing repository sources (Bitbucket, GitHub, etc.)."""

import json
import base64
import logging
from typing import List, Dict, Optional
from datetime import datetime
import requests
from uuid import UUID

from models import db, RepositorySource, Repository
from utils import validate_uuid, encrypt_data, decrypt_data


logger = logging.getLogger(__name__)


class RepositorySourceService:
    """Manages repository sources and discovers repositories from them."""
    
    @staticmethod
    def create_source(
        tenant_id: UUID,
        name: str,
        source_type: str,
        auth_config: Dict,
        settings: Dict,
        created_by: UUID
    ) -> RepositorySource:
        """Create a new repository source."""
        tenant_id = validate_uuid(tenant_id)
        created_by = validate_uuid(created_by)
        
        # Check if source already exists
        existing = RepositorySource.query.filter_by(
            tenant_id=tenant_id,
            name=name
        ).first()
        
        if existing:
            raise ValueError(f"Repository source '{name}' already exists")
        
        # Encrypt authentication configuration
        nonce, ciphertext = encrypt_data(json.dumps(auth_config))
        # Store as base64-encoded string
        auth_config_encrypted = base64.b64encode(nonce + ciphertext).decode('utf-8')
        
        source = RepositorySource(
            tenant_id=tenant_id,
            name=name,
            source_type=source_type,
            auth_config_encrypted=auth_config_encrypted,
            settings_json=settings,
            created_by=created_by
        )
        
        db.session.add(source)
        db.session.commit()
        return source
    
    @staticmethod
    def list_sources(tenant_id: UUID) -> List[RepositorySource]:
        """List all repository sources for a tenant."""
        from sqlalchemy.orm import joinedload
        tenant_id = validate_uuid(tenant_id)
        return RepositorySource.query.filter_by(
            tenant_id=tenant_id,
            is_active=True
        ).options(joinedload(RepositorySource.discovered_repositories)).order_by(RepositorySource.name).all()
    
    @staticmethod
    def get_cached_repositories(source_id: UUID, tenant_id: UUID) -> List[Repository]:
        """Get cached repositories without syncing.
        
        Args:
            source_id: The repository source ID
            tenant_id: The tenant ID
            
        Returns:
            List of cached repositories (empty if never synced)
        """
        source = RepositorySourceService.get_source(source_id, tenant_id)
        if not source:
            raise ValueError("Repository source not found")
        
        return list(source.discovered_repositories)
    
    @staticmethod
    def get_source(source_id: UUID, tenant_id: UUID) -> Optional[RepositorySource]:
        """Get a specific repository source."""
        source_id = validate_uuid(source_id)
        tenant_id = validate_uuid(tenant_id)
        
        return RepositorySource.query.filter_by(
            id=source_id,
            tenant_id=tenant_id
        ).first()
    
    @staticmethod
    def sync_repositories(source_id: UUID, tenant_id: UUID, force: bool = False) -> List[Repository]:
        """Sync repositories from a source with caching.
        
        Args:
            source_id: The repository source ID
            tenant_id: The tenant ID
            force: Force sync even if cache is fresh (default: False)
            
        Returns:
            List of repositories
        """
        source = RepositorySourceService.get_source(source_id, tenant_id)
        if not source:
            raise ValueError("Repository source not found")
        
        # Check if we need to sync (cache expiry: 1 hour)
        if not force and source.last_sync_at:
            time_since_sync = datetime.utcnow() - source.last_sync_at
            if time_since_sync.total_seconds() < 3600:  # 1 hour cache
                logger.info(f"Using cached repositories for source {source_id} (last sync: {source.last_sync_at})")
                return list(source.discovered_repositories)
        
        logger.info(f"Syncing repositories from source {source_id}")
        
        # Decrypt authentication configuration
        # Decode from base64 and separate nonce from ciphertext
        encrypted_data = base64.b64decode(source.auth_config_encrypted.encode('utf-8'))
        nonce = encrypted_data[:12]  # First 12 bytes are the nonce
        ciphertext = encrypted_data[12:]  # Rest is the ciphertext
        auth_config = json.loads(decrypt_data(nonce, ciphertext))
        
        if source.source_type == 'bitbucket':
            repositories = RepositorySourceService._sync_bitbucket_repos(source, auth_config)
        elif source.source_type == 'github':
            repositories = RepositorySourceService._sync_github_repos(source, auth_config)
        elif source.source_type == 'gitlab':
            repositories = RepositorySourceService._sync_gitlab_repos(source, auth_config)
        else:
            raise ValueError(f"Unsupported source type: {source.source_type}")
        
        # Update last sync timestamp
        source.last_sync_at = datetime.utcnow()
        db.session.commit()
        
        logger.info(f"Synced {len(repositories)} repositories for source {source_id}")
        
        return repositories
    
    @staticmethod
    def _sync_bitbucket_repos(source: RepositorySource, auth_config: Dict) -> List[Repository]:
        """Sync repositories from Bitbucket."""
        workspace_id = source.settings_json.get('workspace_id')
        if not workspace_id:
            raise ValueError("Bitbucket workspace_id not configured")
        
        username = auth_config.get('username')
        app_password = auth_config.get('app_password')
        
        if not username or not app_password:
            raise ValueError("Bitbucket credentials not configured")
        
        # Fetch repositories from Bitbucket API
        url = f"https://api.bitbucket.org/2.0/repositories/{workspace_id}"
        
        discovered_repos = []
        page = 1
        
        while url:
            response = requests.get(url, auth=(username, app_password))
            response.raise_for_status()
            data = response.json()
            
            for repo_data in data.get('values', []):
                # Check if repository already exists
                existing = Repository.query.filter_by(
                    source_id=source.id,
                    external_id=repo_data['uuid']
                ).first()
                
                if existing:
                    # Update existing repository
                    existing.name = repo_data['slug']
                    existing.full_name = repo_data['full_name']
                    existing.description = repo_data.get('description', '')
                    existing.clone_url = repo_data['links']['clone'][0]['href']  # HTTPS
                    existing.ssh_url = repo_data['links']['clone'][1]['href']    # SSH
                    existing.default_branch = repo_data.get('mainbranch', {}).get('name', 'main')
                    existing.metadata_json = repo_data
                    existing.last_updated = datetime.utcnow()
                    discovered_repos.append(existing)
                else:
                    # Create new repository
                    new_repo = Repository(
                        source_id=source.id,
                        tenant_id=source.tenant_id,
                        external_id=repo_data['uuid'],
                        name=repo_data['slug'],
                        full_name=repo_data['full_name'],
                        description=repo_data.get('description', ''),
                        clone_url=repo_data['links']['clone'][0]['href'],  # HTTPS
                        ssh_url=repo_data['links']['clone'][1]['href'],    # SSH
                        default_branch=repo_data.get('mainbranch', {}).get('name', 'main'),
                        metadata_json=repo_data
                    )
                    db.session.add(new_repo)
                    discovered_repos.append(new_repo)
            
            # Get next page URL
            url = data.get('next')
        
        db.session.commit()
        return discovered_repos
    
    @staticmethod
    def _sync_github_repos(source: RepositorySource, auth_config: Dict) -> List[Repository]:
        """Sync repositories from GitHub."""
        org_name = source.settings_json.get('org_name')
        if not org_name:
            raise ValueError("GitHub org_name not configured")
        
        token = auth_config.get('token')
        if not token:
            raise ValueError("GitHub token not configured")
        
        # Fetch repositories from GitHub API
        headers = {
            'Authorization': f'token {token}',
            'Accept': 'application/vnd.github.v3+json'
        }
        
        discovered_repos = []
        page = 1
        
        while True:
            url = f"https://api.github.com/orgs/{org_name}/repos"
            response = requests.get(url, headers=headers, params={'page': page, 'per_page': 100})
            response.raise_for_status()
            data = response.json()
            
            if not data:
                break
            
            for repo_data in data:
                # Check if repository already exists
                existing = Repository.query.filter_by(
                    source_id=source.id,
                    external_id=str(repo_data['id'])
                ).first()
                
                if existing:
                    # Update existing repository
                    existing.name = repo_data['name']
                    existing.full_name = repo_data['full_name']
                    existing.description = repo_data.get('description', '')
                    existing.clone_url = repo_data['clone_url']
                    existing.ssh_url = repo_data['ssh_url']
                    existing.default_branch = repo_data.get('default_branch', 'main')
                    existing.metadata_json = repo_data
                    existing.last_updated = datetime.utcnow()
                    discovered_repos.append(existing)
                else:
                    # Create new repository
                    new_repo = Repository(
                        source_id=source.id,
                        tenant_id=source.tenant_id,
                        external_id=str(repo_data['id']),
                        name=repo_data['name'],
                        full_name=repo_data['full_name'],
                        description=repo_data.get('description', ''),
                        clone_url=repo_data['clone_url'],
                        ssh_url=repo_data['ssh_url'],
                        default_branch=repo_data.get('default_branch', 'main'),
                        metadata_json=repo_data
                    )
                    db.session.add(new_repo)
                    discovered_repos.append(new_repo)
            
            page += 1
        
        db.session.commit()
        return discovered_repos
    
    @staticmethod
    def _sync_gitlab_repos(source: RepositorySource, auth_config: Dict) -> List[Repository]:
        """Sync repositories from GitLab."""
        # Similar implementation for GitLab
        # This would use GitLab's API to fetch repositories
        raise NotImplementedError("GitLab sync not yet implemented")
    
    @staticmethod
    def delete_source(tenant_id: UUID, source_id: UUID) -> bool:
        """Delete a repository source and its discovered repositories."""
        tenant_id = validate_uuid(tenant_id)
        source_id = validate_uuid(source_id)
        
        # Get the source
        source = RepositorySource.query.filter_by(
            id=source_id,
            tenant_id=tenant_id
        ).first()
        
        if not source:
            raise ValueError("Repository source not found")
        
        # Delete all discovered repositories for this source
        Repository.query.filter_by(source_id=source_id).delete()
        
        # Delete the source itself
        db.session.delete(source)
        db.session.commit()
        return True