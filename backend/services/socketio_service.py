"""
Socket.IO Service - Handles Socket.IO connections, namespaces and messaging
"""

import time
import logging
import threading
from datetime import datetime, timezone
from flask_socketio import emit
from extensions import socketio
from flask import request, current_app

logger = logging.getLogger(__name__)

class SocketIOService:
    """
    Service class for Socket.IO operations including namespace management,
    message broadcasting, and connection handling.

    This service centralizes all Socket.IO operations to ensure consistent
    behavior and error handling across the application.
    """

    def __init__(self):
        self.active_namespaces = {}  # Track active namespaces and their users

    def make_serializable(self, obj):
        """
        Convert a dictionary that might contain non-serializable objects like sets
        into a JSON-serializable format.

        Args:
            obj: Object to convert (typically a dict)

        Returns:
            JSON-serializable version of the object
        """
        if isinstance(obj, dict):
            result = {}
            for key, value in obj.items():
                result[key] = self.make_serializable(value)
            return result
        elif isinstance(obj, set):
            return list(obj)
        elif isinstance(obj, list):
            return [self.make_serializable(item) for item in obj]
        else:
            return obj

    def create_namespace(self, namespace_id, metadata=None):
        """
        Create a new namespace or reset an existing namespace.

        Args:
            namespace_id (str): Unique identifier for the namespace
            metadata (dict, optional): Additional data to store with the namespace

        Returns:
            str: The namespace path
        """
        timestamp = datetime.now(timezone.utc).isoformat()

        if metadata is None:
            metadata = {}

        # Convert namespace_id to a proper namespace path
        namespace_path = f"/{namespace_id}" if not namespace_id.startswith('/') else namespace_id

        self.active_namespaces[namespace_path] = {
            'created_at': timestamp,
            'users': set(),
            'message_count': 0,
            'last_activity': timestamp,
            'metadata': metadata
        }

        logger.info(f"Created namespace: {namespace_path}")
        return namespace_path

    def join_namespace(self, namespace_id, user_id=None):
        """
        Register a user to a namespace. Creates the namespace if it doesn't exist.

        Args:
            namespace_id (str): Namespace identifier
            user_id (str, optional): User identifier (can be a session ID)
        """
        # Convert namespace_id to a proper namespace path
        namespace_path = f"/{namespace_id}" if not namespace_id.startswith('/') else namespace_id

        # Log beginning of join operation
        print(f"SocketIOService - JOIN NAMESPACE START: {namespace_path} for user {user_id}")
        logger.info(f"SocketIOService - JOIN NAMESPACE START: {namespace_path} for user {user_id}")

        # Create namespace if it doesn't exist
        if namespace_path not in self.active_namespaces:
            logger.warning(f"Namespace {namespace_path} does not exist, creating it now")
            print(f"Namespace {namespace_path} does not exist, creating it now")
            self.create_namespace(namespace_id, {'created_by': user_id})

        # Add user to namespace tracking
        if user_id:
            self.active_namespaces[namespace_path]['users'].add(user_id)

        # Update last activity
        self.active_namespaces[namespace_path]['last_activity'] = datetime.now(timezone.utc).isoformat()

        try:
            # Log current thread for debugging
            thread_name = threading.current_thread().name
            print(f"Joining namespace {namespace_path} in thread: {thread_name}")

            # No need to call join_room since we're using namespaces
            logger.info(f"User {user_id or 'anonymous'} joined namespace: {namespace_path}")

            # Try to get client information
            client_info = "unknown"
            try:
                if request:
                    client_info = f"{request.remote_addr}:{request.environ.get('HTTP_USER_AGENT', 'N/A')[:30]}"
            except Exception as e:
                logger.debug(f"Could not get client info: {str(e)}")
                pass

            # Emit welcome message
            print(f"Sending system_message to namespace {namespace_path} (client: {client_info})")
            success = self.emit_to_namespace(namespace_path, 'agent_output', {
                'type': 'system',
                'content': f"User {user_id or 'anonymous'} connected to namespace {namespace_path}",
                'timestamp': datetime.now(timezone.utc).isoformat()
            })
            print(f"Emit to namespace successful: {success}")

            # Return confirmation to the requester - emit to the client directly, not to the namespace
            print("Emitting joined_namespace event to client")
            emit('joined_namespace', {
                'namespace': namespace_path,
                'timestamp': datetime.now(timezone.utc).isoformat()
            })  # Removed namespace parameter to emit to the client directly
            print(f"SocketIOService - JOIN NAMESPACE COMPLETE: {namespace_path} for user {user_id}")

            # Send multiple test messages to verify connection
            for i in range(3):
                # Short delay between messages
                time.sleep(0.2)
                self.send_message(namespace_path, f"Test message #{i+1} - namespace join completed for {namespace_path}", "system")

        except Exception as e:
            logger.error(f"Error in join_namespace: {str(e)}")
            print(f"ERROR in join_namespace: {str(e)}")
            # Try to emit error to client
            try:
                emit('error', {'message': f"Failed to join namespace: {str(e)}"}, namespace=namespace_path)
            except Exception:
                pass
            raise

    def leave_namespace(self, namespace_id, user_id=None):
        """
        Remove a user from a namespace tracking.

        Args:
            namespace_id (str): Namespace identifier
            user_id (str, optional): User identifier
        """
        # Convert namespace_id to a proper namespace path
        namespace_path = f"/{namespace_id}" if not namespace_id.startswith('/') else namespace_id

        # Track that the user left
        if namespace_path in self.active_namespaces and user_id:
            if user_id in self.active_namespaces[namespace_path]['users']:
                self.active_namespaces[namespace_path]['users'].remove(user_id)

        # Update last activity
        if namespace_path in self.active_namespaces:
            self.active_namespaces[namespace_path]['last_activity'] = datetime.now(timezone.utc).isoformat()

        # No need to call leave_room since we're using namespaces
        logger.info(f"User {user_id or 'anonymous'} left namespace: {namespace_path}")

        # Emit notification to namespace
        self.emit_to_namespace(namespace_path, 'system_message', {
            'type': 'leave',
            'message': f"User disconnected from namespace {namespace_path}",
            'timestamp': datetime.now(timezone.utc).isoformat()
        })

        # Return confirmation to the requester - emit to the client directly, not to the namespace
        emit('left_namespace', {'namespace': namespace_path})

    def emit_to_namespace(self, namespace_id, event_name, data, retries=1):
        """
        Emit an event to all clients in a namespace with retry capability.

        Args:
            namespace_id (str): Namespace identifier
            event_name (str): Socket.IO event name
            data (dict): Data to send with the event
            retries (int): Number of retry attempts for failed emissions

        Returns:
            bool: True if emission was successful, False otherwise
        """
        # Convert namespace_id to a proper namespace path
        namespace_path = f"/{namespace_id}" if not namespace_id.startswith('/') else namespace_id

        # Log the attempt
        print(f"emit_to_namespace: Sending '{event_name}' to namespace '{namespace_path}'")

        # Update namespace activity if it exists
        if namespace_path in self.active_namespaces:
            self.active_namespaces[namespace_path]['last_activity'] = datetime.now(timezone.utc).isoformat()
            self.active_namespaces[namespace_path]['message_count'] += 1
        else:
            print(f"WARNING: Namespace {namespace_path} not found in active_namespaces when emitting {event_name}")
            # Create the namespace if it doesn't exist to prevent errors
            self.create_namespace(namespace_id, {'auto_created': True})

        # Attempt to emit the event
        success = False
        for attempt in range(retries):
            try:
                # Use a direct call to ensure proper delivery
                event_data = data.copy() if isinstance(data, dict) else data
                if isinstance(event_data, dict) and 'timestamp' not in event_data:
                    event_data['timestamp'] = datetime.now(timezone.utc).isoformat()

                # Add extra information for debugging
                print(f"emit_to_namespace attempt {attempt+1}: Emitting '{event_name}' to namespace '{namespace_path}'")
                print(f"Event data type: {type(event_data)}")

                # Add thread info for debugging threading issues
                try:
                    import threading
                    print(f"Current thread: {threading.current_thread().name}")
                except ImportError:
                    pass

                # Perform the actual emit with namespace parameter
                socketio.emit(
                    event_name,
                    event_data,
                    namespace=namespace_path
                )
                success = True
                print(f"emit_to_namespace: Successfully emitted '{event_name}' to namespace '{namespace_path}'")
                break
            except Exception as e:
                error_msg = f"Error emitting '{event_name}' to namespace '{namespace_path}' (attempt {attempt+1}/{retries}): {str(e)}"
                logger.error(error_msg)
                print(error_msg)
                if attempt < retries - 1:
                    time.sleep(0.1)  # Brief delay before retry

        if not success:
            print(f"FAILED: Could not emit '{event_name}' to namespace '{namespace_path}' after {retries} attempts")

        return success

    # Keep the old emit_to_room method for backward compatibility during transition
    def emit_to_room(self, room_id, event_name, data, namespace='/', retries=1):
        """
        Emit an event to all clients in a room with retry capability.
        This method is deprecated and will be removed in the future.
        Use emit_to_namespace instead.

        Args:
            room_id (str): Room identifier (will be treated as namespace ID)
            event_name (str): Socket.IO event name
            data (dict): Data to send with the event
            namespace (str): Socket.IO namespace (ignored, using room_id as namespace)
            retries (int): Number of retry attempts for failed emissions

        Returns:
            bool: True if emission was successful, False otherwise
        """
        # For backward compatibility, we'll treat room_id as namespace_id
        return self.emit_to_namespace(room_id, event_name, data, retries)

    def send_message(self, namespace_id, message, message_type="system", event_name="agent_output"):
        """
        Helper method to send a standard message to a namespace.

        Args:
            namespace_id (str): Namespace identifier
            message (str): Message content
            message_type (str): Message type (system, agent, error, success, etc.)
            event_name (str): Socket.IO event name

        Returns:
            bool: True if message was sent successfully
        """
        data = {
            'type': message_type,
            'content': message,
            'timestamp': datetime.now(timezone.utc).isoformat()
        }

        # Log the message (truncated if very long)
        log_message = message
        if len(log_message) > 100:
            log_message = f"{log_message[:100]}..."
        logger.debug(f"[{namespace_id}] {message_type}: {log_message}")

        return self.emit_to_namespace(namespace_id, event_name, data)

    def broadcast_system_message(self, message, include_namespaces=None, exclude_namespaces=None):
        """
        Broadcast a system message to multiple namespaces.

        Args:
            message (str): Message content
            include_namespaces (list): Specific namespaces to include (None for all)
            exclude_namespaces (list): Namespaces to exclude from the broadcast

        Returns:
            int: Number of namespaces the message was sent to
        """
        if include_namespaces is None:
            # Target all active namespaces
            include_namespaces = list(self.active_namespaces.keys())

        if exclude_namespaces is None:
            exclude_namespaces = []

        target_namespaces = [n for n in include_namespaces if n not in exclude_namespaces]
        success_count = 0

        for namespace in target_namespaces:
            if self.send_message(namespace, message, "system"):
                success_count += 1

        return success_count

    def get_namespace_info(self, namespace_id):
        """
        Get information about a namespace.

        Args:
            namespace_id (str): Namespace identifier

        Returns:
            dict: Namespace information or None if namespace doesn't exist, with all values
                 converted to JSON-serializable types.
        """
        # Convert namespace_id to a proper namespace path
        namespace_path = f"/{namespace_id}" if not namespace_id.startswith('/') else namespace_id

        namespace_data = self.active_namespaces.get(namespace_path)
        if namespace_data:
            return self.make_serializable(namespace_data)
        return None

    # Keep for backward compatibility
    def get_room_info(self, room_id):
        """
        Get information about a room (now namespace).
        This method is deprecated and will be removed in the future.
        Use get_namespace_info instead.

        Args:
            room_id (str): Room identifier (treated as namespace ID)

        Returns:
            dict: Room information or None if room doesn't exist
        """
        return self.get_namespace_info(room_id)

    def get_active_namespaces(self):
        """
        Get a list of all active namespaces.

        Returns:
            dict: All active namespaces with all values converted to JSON-serializable types.
        """
        return self.make_serializable(self.active_namespaces)

    # Keep for backward compatibility
    def get_active_rooms(self):
        """
        Get a list of all active rooms (now namespaces).
        This method is deprecated and will be removed in the future.
        Use get_active_namespaces instead.

        Returns:
            dict: All active namespaces with all values converted to JSON-serializable types.
        """
        return self.get_active_namespaces()

    def clean_inactive_namespaces(self, max_age_seconds=3600):
        """
        Clean up namespaces that have been inactive for a specified time.

        Args:
            max_age_seconds (int): Maximum age in seconds before a namespace is considered inactive

        Returns:
            int: Number of namespaces cleaned
        """
        now = datetime.now(timezone.utc)
        namespaces_to_remove = []

        for namespace_path, namespace_data in self.active_namespaces.items():
            last_activity = datetime.fromisoformat(namespace_data['last_activity'])
            age_seconds = (now - last_activity).total_seconds()

            if age_seconds > max_age_seconds:
                namespaces_to_remove.append(namespace_path)

        for namespace_path in namespaces_to_remove:
            del self.active_namespaces[namespace_path]
            logger.info(f"Cleaned inactive namespace: {namespace_path}")

        return len(namespaces_to_remove)

    # Keep for backward compatibility
    def clean_inactive_rooms(self, max_age_seconds=3600):
        """
        Clean up rooms (now namespaces) that have been inactive for a specified time.
        This method is deprecated and will be removed in the future.
        Use clean_inactive_namespaces instead.

        Args:
            max_age_seconds (int): Maximum age in seconds before a room is considered inactive

        Returns:
            int: Number of rooms cleaned
        """
        return self.clean_inactive_namespaces(max_age_seconds)

# Create a singleton instance for use throughout the application
socketio_service = SocketIOService()

# Set up a ping handler to help with debugging connections
# Using socketio from extensions to avoid circular imports

@socketio.on('ping', namespace='/')
def handle_ping(data):
    """Handler for connection testing ping"""
    print(f"Received ping from client: {data}")
    emit('pong', {
        'server_time': datetime.now(timezone.utc).isoformat(),
        'received_at': data.get('timestamp', 'unknown')
    })


@socketio.on('connect', namespace='/')
def handle_log_connect():
    print('Client connected to / namespace')
    emit('connection_ack', {'data': 'Connected to / namespace!'})


@socketio.on('disconnect', namespace='/')
def handle_log_disconnect():
    print('Client disconnected from / namespace')


@socketio.on('client_message', namespace='/')
def handle_client_message(json_data):
    print('Received message from client on /: ' + str(json_data))
    emit('server_response', {'data': f"Server received: {json_data.get('message', '')}"})