"""Service for managing user project paths."""

from typing import List, Optional
from uuid import UUID
from models import db, UserProjectPath
from utils import validate_uuid


class UserProjectPathService:
    @staticmethod
    def create_or_update_path(
        tenant_id: UUID,
        user_id: UUID,
        project_key: str,
        local_path: str
    ) -> UserProjectPath:
        """Create or update a user's project path mapping."""
        tenant_id = validate_uuid(tenant_id)
        user_id = validate_uuid(user_id)
        
        # Check if mapping already exists
        existing = UserProjectPath.query.filter_by(
            tenant_id=tenant_id,
            user_id=user_id,
            project_key=project_key
        ).first()
        
        if existing:
            existing.local_path = local_path
            existing.is_active = True
            db.session.commit()
            return existing
        
        # Create new mapping
        path_mapping = UserProjectPath(
            tenant_id=tenant_id,
            user_id=user_id,
            project_key=project_key,
            local_path=local_path
        )
        
        db.session.add(path_mapping)
        db.session.commit()
        return path_mapping
    
    @staticmethod
    def get_path(
        tenant_id: UUID,
        user_id: UUID,
        project_key: str
    ) -> Optional[str]:
        """Get the local path for a user's project."""
        tenant_id = validate_uuid(tenant_id)
        user_id = validate_uuid(user_id)
        
        path_mapping = UserProjectPath.query.filter_by(
            tenant_id=tenant_id,
            user_id=user_id,
            project_key=project_key,
            is_active=True
        ).first()
        
        return path_mapping.local_path if path_mapping else None
    
    @staticmethod
    def list_user_paths(
        tenant_id: UUID,
        user_id: UUID
    ) -> List[UserProjectPath]:
        """List all project path mappings for a user."""
        tenant_id = validate_uuid(tenant_id)
        user_id = validate_uuid(user_id)
        
        return UserProjectPath.query.filter_by(
            tenant_id=tenant_id,
            user_id=user_id,
            is_active=True
        ).order_by(UserProjectPath.project_key).all()
    
    @staticmethod
    def delete_path(
        tenant_id: UUID,
        user_id: UUID,
        project_key: str
    ) -> bool:
        """Delete (soft delete) a user's project path mapping."""
        tenant_id = validate_uuid(tenant_id)
        user_id = validate_uuid(user_id)
        
        path_mapping = UserProjectPath.query.filter_by(
            tenant_id=tenant_id,
            user_id=user_id,
            project_key=project_key
        ).first()
        
        if path_mapping:
            path_mapping.is_active = False
            db.session.commit()
            return True
        
        return False