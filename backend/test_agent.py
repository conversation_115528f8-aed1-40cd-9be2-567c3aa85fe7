import uuid
import os
from agents.developer_agent import create_dynamic_developer_agent

from google.adk.runners import Runner
from google.adk.sessions import InMemorySessionService
from google.genai import types as genai_types


os.environ["JIRA_USER"] = "<EMAIL>"
os.environ["GOOGLE_GENAI_USE_VERTEXAI"] = "TRUE"
os.environ["GOOGLE_CLOUD_PROJECT"] = "apex-dev-377304"
os.environ["GOOGLE_CLOUD_LOCATION"] = "us-central1"
os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = "/home/<USER>/pemFiles/vertex_ai_keyfile.json"


USER_ID = "user_1"
SESSION_ID = str(uuid.uuid4())
APP_NAME = "developer_agent"
session_service = InMemorySessionService()
# Create session in the session service
session_service.create_session_sync(
    app_name=APP_NAME, user_id=USER_ID, session_id=SESSION_ID
)

enriched_repositories = [
    {
        "id": "1",
        "name": "ui-component",
        "full_name": "ui-component-library",
        "description": "",
        "source_type": "git",
        "local_path": "/home/<USER>/Work/UI/ui-component-library",
        'agent_instruction': '''UI component library repo. It contains the code for the ui components.
            Contains the source code for all components which are imported from @atomic, @basic, @composite.
            Always execute the web UI tester tool after making the code chages
        ''',
        'agent_description': "ui component library agent",
        'enabled_tools': ['web_ui_tester'],
        'tool_configs': {'web_ui_tester': {'URL': 'http://localhost:4400'}}
    }
]

jira_credentials = {
    "JIRA_KEY": "ATATT3xFfGF0sb1lqJFqU75oUfyAVbs9xH6DpKsiY8NVN0CxHgzul3-pt4eJMQPPME0T1K8beZRg2gBb_9yEnXQgqKKPxdm0lFx_UBs6DNo9cjxFIUQUXAtwJ1Uf3FwlwmJfL7TC5z-Y9wmMF8U1HWXBANmr4-jmo8PmfiZGjuVEwfQ1g_nm6SA=8380D94E",
    "JIRA_USER": "<EMAIL>",
    "JIRA_URL": "https://jiffy-ai.atlassian.net",
}

developer_agent = create_dynamic_developer_agent(
    enriched_repositories, jira_credentials
)
agent_runner = Runner(
    agent=developer_agent, app_name=APP_NAME, session_service=session_service
)
content = genai_types.Content(
    role="user", parts=[genai_types.Part(text="Fix bug APP-9325")]
)
for event in agent_runner.run(
    user_id=USER_ID, session_id=SESSION_ID, new_message=content
):
    print(event)
