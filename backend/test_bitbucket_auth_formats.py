#!/usr/bin/env python3
"""Test different authentication formats for Bitbucket."""

import requests
import base64
import sys

def test_auth(username, password):
    """Test authentication with given credentials."""
    credentials = f"{username}:{password}"
    encoded = base64.b64encode(credentials.encode()).decode()
    headers = {
        "Authorization": f"Basic {encoded}",
        "Accept": "application/json"
    }
    
    try:
        response = requests.get("https://api.bitbucket.org/2.0/user", headers=headers, timeout=5)
        return response.status_code, response.text
    except Exception as e:
        return None, str(e)

def main():
    if len(sys.argv) != 2:
        print("Usage: python test_bitbucket_auth_formats.py <app_password>")
        sys.exit(1)
    
    app_password = sys.argv[1]
    
    # Test different username formats
    test_formats = [
        "<EMAIL>",
        "rajmohan.h",
        "raj<PERSON>han_h",
        "rajmo<PERSON><PERSON>",
        "jiffy_bb_admin",
        "jiffy_ws",
        "jiffy"
    ]
    
    print("Testing different username formats...")
    print("=" * 50)
    
    for username in test_formats:
        print(f"\nTrying: {username}")
        status, response = test_auth(username, app_password)
        
        if status == 200:
            print(f"✓ SUCCESS with username: {username}")
            try:
                import json
                data = json.loads(response)
                print(f"  Display name: {data.get('display_name')}")
                print(f"  Username: {data.get('username')}")
                print(f"  Account ID: {data.get('account_id')}")
                break
            except:
                pass
        elif status == 401:
            print(f"✗ Authentication failed")
        else:
            print(f"✗ Error: {status}")

if __name__ == "__main__":
    main()