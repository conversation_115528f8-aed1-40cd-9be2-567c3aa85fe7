#!/usr/bin/env python3
"""Test script for Bitbucket credentials and repository listing."""

import requests
import json
import sys
import argparse
from datetime import datetime
import base64

# Color codes for terminal output
GREEN = '\033[92m'
YELLOW = '\033[93m'
RED = '\033[91m'
RESET = '\033[0m'

def log(message, color=RESET):
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"{color}[{timestamp}] {message}{RESET}")

def test_bitbucket_credentials(workspace_id, username, app_password):
    """Test Bitbucket credentials and list repositories."""
    
    # Create basic auth header
    credentials = f"{username}:{app_password}"
    encoded_credentials = base64.b64encode(credentials.encode()).decode()
    headers = {
        "Authorization": f"Basic {encoded_credentials}",
        "Accept": "application/json"
    }
    
    log("Testing Bitbucket credentials...", YELLOW)
    
    # First, test authentication with user endpoint
    user_url = "https://api.bitbucket.org/2.0/user"
    try:
        response = requests.get(user_url, headers=headers, timeout=5)
        
        if response.status_code == 401:
            log("Authentication failed. Please check your username and app password.", RED)
            return False
        elif response.status_code != 200:
            log(f"Failed to authenticate: {response.status_code} - {response.text}", RED)
            return False
            
        user_data = response.json()
        log(f"Authenticated as: {user_data.get('display_name', 'Unknown')} ({user_data.get('username', 'Unknown')})", GREEN)
    except Exception as e:
        log(f"Error testing authentication: {e}", RED)
        return False
    
    # Test workspace access
    log(f"\nTesting access to workspace '{workspace_id}'...", YELLOW)
    workspace_url = f"https://api.bitbucket.org/2.0/workspaces/{workspace_id}"
    
    try:
        response = requests.get(workspace_url, headers=headers, timeout=5)
        
        if response.status_code == 404:
            log(f"Workspace '{workspace_id}' not found.", RED)
            return False
        elif response.status_code == 403:
            log(f"No access to workspace '{workspace_id}'. Make sure your app password has the necessary permissions.", RED)
            return False
        elif response.status_code != 200:
            log(f"Failed to access workspace: {response.status_code} - {response.text}", RED)
            return False
            
        workspace_data = response.json()
        log(f"Workspace found: {workspace_data.get('name', 'Unknown')} ({workspace_data.get('slug', workspace_id)})", GREEN)
    except Exception as e:
        log(f"Error accessing workspace: {e}", RED)
        return False
    
    # List repositories
    log(f"\nListing repositories in workspace '{workspace_id}'...", YELLOW)
    repos_url = f"https://api.bitbucket.org/2.0/repositories/{workspace_id}"
    
    try:
        page = 1
        all_repositories = []
        
        while True:
            params = {"page": page, "pagelen": 10}
            response = requests.get(repos_url, headers=headers, params=params, timeout=5)
            
            if response.status_code == 403:
                log("No access to list repositories. Check app password permissions.", RED)
                return False
            elif response.status_code != 200:
                log(f"Failed to list repositories: {response.status_code} - {response.text}", RED)
                return False
                
            data = response.json()
            repositories = data.get('values', [])
            
            if not repositories:
                break
                
            all_repositories.extend(repositories)
            
            # Check if there are more pages
            if 'next' not in data:
                break
                
            page += 1
            
            # Limit to first 50 repositories for testing
            if len(all_repositories) >= 50:
                log("Limiting to first 50 repositories for testing", YELLOW)
                break
        
        if not all_repositories:
            log("No repositories found in this workspace.", YELLOW)
            return True
            
        log(f"\nFound {len(all_repositories)} repositories:", GREEN)
        log("=" * 50, GREEN)
        
        for i, repo in enumerate(all_repositories, 1):
            name = repo.get('name', 'Unknown')
            full_name = repo.get('full_name', 'Unknown')
            description = repo.get('description', 'No description')
            is_private = repo.get('is_private', False)
            language = repo.get('language', 'Unknown')
            created_on = repo.get('created_on', 'Unknown')
            
            log(f"\n{i}. {name}", GREEN)
            log(f"   Full name: {full_name}")
            log(f"   Description: {description[:60]}..." if len(description) > 60 else f"   Description: {description}")
            log(f"   Private: {is_private}")
            log(f"   Language: {language}")
            log(f"   Created: {created_on}")
            log(f"   Clone URL: {repo.get('links', {}).get('clone', [{}])[0].get('href', 'N/A')}")
        
        log(f"\n{len(all_repositories)} repositories listed successfully!", GREEN)
        
        # Test configuration for the backend
        log("\n" + "=" * 50, YELLOW)
        log("Configuration for backend repository source:", YELLOW)
        log(json.dumps({
            "source_name": f"{workspace_id} Workspace",
            "source_type": "bitbucket",
            "auth_config": {
                "username": username,
                "app_password": app_password
            },
            "settings": {
                "workspace": workspace_id
            }
        }, indent=2), GREEN)
        
        return True
        
    except Exception as e:
        log(f"Error listing repositories: {e}", RED)
        return False

def main():
    parser = argparse.ArgumentParser(description='Test Bitbucket credentials and list repositories')
    parser.add_argument('workspace_id', help='Bitbucket workspace ID (e.g., jiffy_bb_admin)')
    parser.add_argument('username', help='Bitbucket username')
    parser.add_argument('app_password', help='Bitbucket app password')
    
    args = parser.parse_args()
    
    log("Bitbucket Credentials Test", YELLOW)
    log("=" * 50, YELLOW)
    
    success = test_bitbucket_credentials(args.workspace_id, args.username, args.app_password)
    
    log("=" * 50, YELLOW)
    if success:
        log("TEST PASSED", GREEN)
        log("Your credentials are valid and can access the repositories.", GREEN)
    else:
        log("TEST FAILED", RED)
        log("Please check your credentials and permissions.", RED)
        sys.exit(1)

if __name__ == "__main__":
    main()