#!/usr/bin/env python3
"""Debug version of Bitbucket credentials test."""

import requests
import json
import sys
import base64
from datetime import datetime

# Color codes for terminal output
GREEN = '\033[92m'
YELLOW = '\033[93m'
RED = '\033[91m'
RESET = '\033[0m'

def log(message, color=RESET):
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"{color}[{timestamp}] {message}{RESET}")

def test_bitbucket_debug(workspace_id, username, app_password):
    """Test Bitbucket credentials with detailed debugging."""
    
    log("Bitbucket Credentials Debug Test", YELLOW)
    log("=" * 50, YELLOW)
    
    # Show what we're testing with (without showing the actual password)
    log(f"Workspace ID: {workspace_id}")
    log(f"Username: {username}")
    log(f"App Password: {'*' * len(app_password)} ({len(app_password)} chars)")
    
    # Create basic auth header
    credentials = f"{username}:{app_password}"
    encoded_credentials = base64.b64encode(credentials.encode()).decode()
    headers = {
        "Authorization": f"Basic {encoded_credentials}",
        "Accept": "application/json"
    }
    
    log("\nHeaders (auth token hidden):", YELLOW)
    log(f"Authorization: Basic {'*' * 20}...")
    log(f"Accept: {headers['Accept']}")
    
    # First, test authentication with user endpoint
    user_url = "https://api.bitbucket.org/2.0/user"
    log(f"\nTesting URL: {user_url}", YELLOW)
    
    try:
        log("Sending request...", YELLOW)
        response = requests.get(user_url, headers=headers, timeout=10)
        
        log(f"Response Status: {response.status_code}", YELLOW)
        log(f"Response Headers: {dict(response.headers)}", YELLOW)
        
        if response.status_code == 401:
            log("Authentication failed (401).", RED)
            log("Response body:", RED)
            try:
                error_data = response.json()
                log(json.dumps(error_data, indent=2), RED)
            except:
                log(response.text, RED)
            
            log("\nPossible issues:", YELLOW)
            log("1. Username might be incorrect (try without @jiffy.ai)")
            log("2. App password might be expired or incorrect")
            log("3. App password might not have required permissions")
            log("4. The account might use 2FA without app password")
            
            # Try alternate auth format
            log("\nTrying alternate username format...", YELLOW)
            alt_username = username.split('@')[0] if '@' in username else username + '@jiffy.ai'
            alt_credentials = f"{alt_username}:{app_password}"
            alt_encoded = base64.b64encode(alt_credentials.encode()).decode()
            alt_headers = {
                "Authorization": f"Basic {alt_encoded}",
                "Accept": "application/json"
            }
            
            log(f"Testing with username: {alt_username}", YELLOW)
            alt_response = requests.get(user_url, headers=alt_headers, timeout=5)
            log(f"Alternate Response Status: {alt_response.status_code}", YELLOW)
            
            if alt_response.status_code == 200:
                user_data = alt_response.json()
                log(f"SUCCESS with username: {alt_username}", GREEN)
                log(f"Display name: {user_data.get('display_name')}", GREEN)
                log(f"Username: {user_data.get('username')}", GREEN)
                log(f"\nUse this username instead: {alt_username}", GREEN)
            
            return False
            
        elif response.status_code == 200:
            user_data = response.json()
            log("Authentication successful!", GREEN)
            log(f"Display name: {user_data.get('display_name')}", GREEN)
            log(f"Username: {user_data.get('username')}", GREEN)
            log(f"Account ID: {user_data.get('account_id')}", GREEN)
            log(f"Type: {user_data.get('type')}", GREEN)
            
            return True
        else:
            log(f"Unexpected status: {response.status_code}", RED)
            log(f"Response: {response.text}", RED)
            return False
            
    except requests.Timeout:
        log("Request timed out", RED)
        return False
    except Exception as e:
        log(f"Error: {type(e).__name__}: {e}", RED)
        return False

if __name__ == "__main__":
    if len(sys.argv) != 4:
        print("Usage: python test_bitbucket_debug.py <workspace_id> <username> <app_password>")
        sys.exit(1)
    
    workspace_id = sys.argv[1]
    username = sys.argv[2]
    app_password = sys.argv[3]
    
    test_bitbucket_debug(workspace_id, username, app_password)