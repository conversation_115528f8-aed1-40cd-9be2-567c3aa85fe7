#!/usr/bin/env python3
"""Interactive test script for Bitbucket credentials."""

import requests
import json
import getpass
from datetime import datetime
import base64

# Color codes for terminal output
GREEN = '\033[92m'
YELLOW = '\033[93m'
RED = '\033[91m'
RESET = '\033[0m'

def log(message, color=RESET):
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"{color}[{timestamp}] {message}{RESET}")

def test_bitbucket_connection():
    """Interactive test for Bitbucket credentials."""
    
    log("Bitbucket Credentials Test (Interactive)", YELLOW)
    log("=" * 50, YELLOW)
    
    # Get inputs
    workspace_id = input("\nWorkspace ID (e.g., jiffy_bb_admin): ").strip()
    username = input("Username (e.g., <EMAIL>): ").strip()
    app_password = getpass.getpass("App Password: ").strip()
    
    print()
    log("Testing credentials...", YELLOW)
    
    # Create auth header
    credentials = f"{username}:{app_password}"
    encoded_credentials = base64.b64encode(credentials.encode()).decode()
    headers = {
        "Authorization": f"Basic {encoded_credentials}",
        "Accept": "application/json"
    }
    
    # Test authentication
    try:
        response = requests.get("https://api.bitbucket.org/2.0/user", headers=headers, timeout=5)
        
        if response.status_code == 401:
            log("Authentication failed. Please check your credentials.", RED)
            return
        elif response.status_code != 200:
            log(f"Error: {response.status_code} - {response.text}", RED)
            return
            
        user_data = response.json()
        log(f"✓ Authenticated as: {user_data.get('display_name')} ({user_data.get('username')})", GREEN)
    except Exception as e:
        log(f"Error: {e}", RED)
        return
    
    # Test workspace access
    try:
        response = requests.get(f"https://api.bitbucket.org/2.0/workspaces/{workspace_id}", headers=headers, timeout=5)
        
        if response.status_code == 404:
            log(f"Workspace '{workspace_id}' not found.", RED)
            return
        elif response.status_code == 403:
            log(f"No access to workspace '{workspace_id}'.", RED)
            return
        elif response.status_code != 200:
            log(f"Error: {response.status_code} - {response.text}", RED)
            return
            
        workspace_data = response.json()
        log(f"✓ Workspace: {workspace_data.get('name')} ({workspace_data.get('slug')})", GREEN)
    except Exception as e:
        log(f"Error: {e}", RED)
        return
    
    # List first few repositories
    try:
        response = requests.get(
            f"https://api.bitbucket.org/2.0/repositories/{workspace_id}",
            headers=headers,
            params={"pagelen": 5},
            timeout=5
        )
        
        if response.status_code != 200:
            log(f"Failed to list repositories: {response.status_code}", RED)
            return
            
        data = response.json()
        repos = data.get('values', [])
        
        if repos:
            log(f"\n✓ Found {len(repos)} repositories (showing first 5):", GREEN)
            for i, repo in enumerate(repos, 1):
                log(f"  {i}. {repo.get('name')} - {repo.get('description', 'No description')[:50]}...")
        else:
            log("No repositories found.", YELLOW)
        
        log("\n✓ All tests passed! Your credentials are valid.", GREEN)
        
        # Show configuration
        print("\n" + "=" * 50)
        log("Use these values in the UI form:", YELLOW)
        print(f"  Source Name: {workspace_id} Workspace")
        print(f"  Source Type: Bitbucket")
        print(f"  Workspace ID: {workspace_id}")
        print(f"  Username: {username}")
        print(f"  App Password: [use the password you just entered]")
        
    except Exception as e:
        log(f"Error: {e}", RED)

if __name__ == "__main__":
    test_bitbucket_connection()