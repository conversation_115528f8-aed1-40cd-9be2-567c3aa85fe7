"""Debug test to check what's failing."""
import sys
import os
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

from app import create_app
from models import db

def test_app_setup():
    """Test if app can be created."""
    app = create_app()
    assert app is not None
    
    with app.app_context():
        # Check if database can be initialized
        db.create_all()
        print("Database tables created")
        
        # Check registered routes
        print("\nRegistered routes:")
        for rule in app.url_map.iter_rules():
            print(f"{rule.endpoint}: {rule}")

if __name__ == "__main__":
    test_app_setup()