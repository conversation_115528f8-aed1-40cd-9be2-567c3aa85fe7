"""Test script for repository deletion functionality."""

import requests
import json
import sys
from uuid import uuid4

# Configuration
BASE_URL = "http://localhost:5045"
TENANT_ID = "550e8400-e29b-41d4-a716-************"  # Replace with your tenant ID

def login_and_get_token():
    """Lo<PERSON> as admin user and get JWT token."""
    login_url = f"{BASE_URL}/tenants/{TENANT_ID}/auth/login"
    credentials = {
        "email": "<EMAIL>",
        "password": "admin123",
        "tenant_id": TENANT_ID
    }
    
    response = requests.post(login_url, json=credentials)
    if response.status_code == 200:
        data = response.json()
        return data.get("access_token")
    else:
        print(f"Login failed: {response.status_code} - {response.text}")
        sys.exit(1)

def list_repositories(token):
    """List all repositories."""
    url = f"{BASE_URL}/tenants/{TENANT_ID}/repositories"
    headers = {"Authorization": f"Bearer {token}"}
    
    response = requests.get(url, headers=headers)
    if response.status_code == 200:
        return response.json().get("repositories", [])
    else:
        print(f"Failed to list repositories: {response.status_code} - {response.text}")
        return []

def create_test_repository(token):
    """Create a test repository."""
    url = f"{BASE_URL}/tenants/{TENANT_ID}/repositories"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    repo_data = {
        "name": f"test-repo-{uuid4().hex[:8]}",
        "display_name": "Test Repository for Deletion",
        "repository_type": "github",
        "remote_url": "https://github.com/test/repo.git",
        "default_branch": "main",
        "description": "Test repository for delete functionality"
    }
    
    response = requests.post(url, json=repo_data, headers=headers)
    if response.status_code == 201:
        return response.json()
    else:
        print(f"Failed to create repository: {response.status_code} - {response.text}")
        return None

def soft_delete_repository(token, repo_id):
    """Soft delete a repository."""
    url = f"{BASE_URL}/tenants/{TENANT_ID}/repositories/{repo_id}"
    headers = {"Authorization": f"Bearer {token}"}
    
    response = requests.delete(url, headers=headers)
    if response.status_code == 200:
        return response.json()
    else:
        print(f"Failed to soft delete repository: {response.status_code} - {response.text}")
        return None

def hard_delete_repository(token, repo_id):
    """Hard delete a repository."""
    url = f"{BASE_URL}/tenants/{TENANT_ID}/repositories/{repo_id}?force=true"
    headers = {"Authorization": f"Bearer {token}"}
    
    response = requests.delete(url, headers=headers)
    if response.status_code == 200:
        return response.json()
    else:
        print(f"Failed to hard delete repository: {response.status_code} - {response.text}")
        return None

def main():
    """Main test function."""
    # Login
    print("Logging in...")
    token = login_and_get_token()
    print("Login successful!")
    
    # List existing repositories
    print("\nListing existing repositories...")
    repos = list_repositories(token)
    for repo in repos:
        print(f"- {repo['name']} ({repo['id']}) - Active: {repo['is_active']}")
    
    # Create a test repository
    print("\nCreating test repository...")
    test_repo = create_test_repository(token)
    if test_repo:
        print(f"Created repository: {test_repo['name']} ({test_repo['id']})")
    else:
        print("Failed to create test repository")
        return
    
    # Soft delete the repository
    print(f"\nSoft deleting repository {test_repo['id']}...")
    result = soft_delete_repository(token, test_repo['id'])
    if result:
        print(f"Soft delete successful: {result['message']}")
        print(f"Repository is now: {result['repository']}")
    
    # List repositories again to see the change
    print("\nListing repositories after soft delete...")
    repos = list_repositories(token)
    for repo in repos:
        if repo['id'] == test_repo['id']:
            print(f"- {repo['name']} ({repo['id']}) - Active: {repo['is_active']} <-- SOFT DELETED")
        else:
            print(f"- {repo['name']} ({repo['id']}) - Active: {repo['is_active']}")
    
    # Create another test repository for hard delete
    print("\nCreating another test repository for hard delete...")
    test_repo2 = create_test_repository(token)
    if test_repo2:
        print(f"Created repository: {test_repo2['name']} ({test_repo2['id']})")
    else:
        print("Failed to create test repository")
        return
    
    # Hard delete the repository
    print(f"\nHard deleting repository {test_repo2['id']}...")
    result = hard_delete_repository(token, test_repo2['id'])
    if result:
        print(f"Hard delete successful: {result['message']}")
    
    # List repositories one final time
    print("\nListing repositories after hard delete...")
    repos = list_repositories(token)
    found_hard_deleted = False
    for repo in repos:
        if repo['id'] == test_repo2['id']:
            found_hard_deleted = True
            print(f"- {repo['name']} ({repo['id']}) - Active: {repo['is_active']}")
        else:
            print(f"- {repo['name']} ({repo['id']}) - Active: {repo['is_active']}")
    
    if not found_hard_deleted:
        print(f"Repository {test_repo2['id']} was successfully hard deleted (not in list)")

if __name__ == "__main__":
    main()