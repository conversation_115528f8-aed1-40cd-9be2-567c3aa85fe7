#!/usr/bin/env python3
"""Test script for repository source management functionality."""

import requests
import json
import sys
from datetime import datetime

# Base URL for API
BASE_URL = "http://localhost:5045"
TENANT_NAME = "DefaultTenant"
USER_EMAIL = "<EMAIL>"
USER_PASSWORD = "password123"

# Color codes for terminal output
GREEN = '\033[92m'
YELLOW = '\033[93m'
RED = '\033[91m'
RESET = '\033[0m'

def log(message, color=RESET):
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    print(f"{color}[{timestamp}] {message}{RESET}")

def test_repository_sources():
    """Test the complete repository source management workflow."""
    
    # Step 1: Get tenant info
    log("Step 1: Getting tenant info...", YELLOW)
    response = requests.get(f"{BASE_URL}/api/public/tenants/resolve?name={TENANT_NAME}")
    if response.status_code != 200:
        log(f"Failed to get tenant info: {response.text}", RED)
        return False
    
    tenant_data = response.json()
    tenant_id = tenant_data['id']
    log(f"Found tenant: {tenant_id}", GREEN)
    
    # Step 2: Login
    log("\nStep 2: Logging in...", YELLOW)
    login_data = {
        "email": USER_EMAIL,
        "password": USER_PASSWORD,
        "tenant_id": tenant_id
    }
    response = requests.post(f"{BASE_URL}/tenants/{tenant_id}/auth/login", json=login_data)
    if response.status_code != 200:
        log(f"Failed to login: {response.text}", RED)
        return False
    
    auth_data = response.json()
    access_token = auth_data['access_token']
    headers = {"Authorization": f"Bearer {access_token}"}
    log("Login successful", GREEN)
    
    # Step 3: Create a repository source
    log("\nStep 3: Creating a repository source...", YELLOW)
    source_data = {
        "name": "Test Bitbucket Workspace",
        "source_type": "bitbucket",
        "auth_config": {
            "username": "test-user",
            "app_password": "test-app-password"
        },
        "settings": {
            "workspace": "test-workspace"
        }
    }
    response = requests.post(
        f"{BASE_URL}/tenants/{tenant_id}/repository-sources",
        json=source_data,
        headers=headers
    )
    if response.status_code != 201:
        log(f"Failed to create repository source: {response.text}", RED)
        return False
    
    source = response.json()
    source_id = source['id']
    log(f"Created repository source: {source_id}", GREEN)
    
    # Step 4: List repository sources
    log("\nStep 4: Listing repository sources...", YELLOW)
    response = requests.get(
        f"{BASE_URL}/tenants/{tenant_id}/repository-sources",
        headers=headers
    )
    if response.status_code != 200:
        log(f"Failed to list sources: {response.text}", RED)
        return False
    
    sources = response.json()
    log(f"Found {len(sources)} repository sources", GREEN)
    
    # Step 5: Get specific repository source
    log("\nStep 5: Getting specific repository source...", YELLOW)
    response = requests.get(
        f"{BASE_URL}/tenants/{tenant_id}/repository-sources/{source_id}",
        headers=headers
    )
    if response.status_code != 200:
        log(f"Failed to get source: {response.text}", RED)
        return False
    
    source_details = response.json()
    log(f"Retrieved source: {source_details['name']}", GREEN)
    
    # Step 6: Update repository source
    log("\nStep 6: Updating repository source...", YELLOW)
    update_data = {
        "name": "Updated Bitbucket Workspace",
        "is_active": True
    }
    response = requests.put(
        f"{BASE_URL}/tenants/{tenant_id}/repository-sources/{source_id}",
        json=update_data,
        headers=headers
    )
    if response.status_code != 200:
        log(f"Failed to update source: {response.text}", RED)
        return False
    
    log("Repository source updated successfully", GREEN)
    
    # Step 7: Mock sync repositories (would fail with real API calls)
    log("\nStep 7: Attempting to sync repositories (mock)...", YELLOW)
    response = requests.post(
        f"{BASE_URL}/tenants/{tenant_id}/repository-sources/{source_id}/sync",
        headers=headers
    )
    # This will likely fail since we're using mock credentials
    if response.status_code == 500:
        log("Sync failed as expected with mock credentials", YELLOW)
    else:
        log(f"Sync response: {response.status_code} - {response.text}", GREEN)
    
    # Step 8: Delete repository source
    log("\nStep 8: Deleting repository source...", YELLOW)
    response = requests.delete(
        f"{BASE_URL}/tenants/{tenant_id}/repository-sources/{source_id}",
        headers=headers
    )
    if response.status_code != 204:
        log(f"Failed to delete source: {response.text}", RED)
        return False
    
    log("Repository source deleted successfully", GREEN)
    
    # Step 9: Verify deletion
    log("\nStep 9: Verifying deletion...", YELLOW)
    response = requests.get(
        f"{BASE_URL}/tenants/{tenant_id}/repository-sources/{source_id}",
        headers=headers
    )
    if response.status_code == 404:
        log("Source deletion confirmed", GREEN)
    else:
        log(f"Source still exists: {response.status_code}", RED)
        return False
    
    log("\nAll tests completed successfully!", GREEN)
    return True

if __name__ == "__main__":
    log("Starting Repository Source Management Test", YELLOW)
    log("=" * 50, YELLOW)
    
    # Ensure the server is running
    try:
        response = requests.get(f"{BASE_URL}/")
        if response.status_code != 200:
            log("Backend server is not running. Please start it first.", RED)
            sys.exit(1)
    except requests.ConnectionError:
        log("Backend server is not running. Please start it first.", RED)
        sys.exit(1)
    
    # Run the tests
    success = test_repository_sources()
    
    log("=" * 50, YELLOW)
    if success:
        log("TEST PASSED", GREEN)
    else:
        log("TEST FAILED", RED)
        sys.exit(1)