# Backend Tests

This directory contains tests for the Flask backend of the JIRA Bug Browser application.

## Test Coverage

The test suite covers all major components of the application:

- **Authentication**: Login, token refresh, and logout functionality
- **Tenant Resolution**: Public tenant resolution by name
- **Project Management**: Project listing and filtering
- **Issue Management**: Issue listing, filtering, and details
- **JIRA Integration**: JIRA credentials management
- **Tenant Administration**: Tenant creation and management
- **CORS**: Cross-Origin Resource Sharing policy verification
- **Security**: Authentication enforcement and tenant isolation

## Test Organization

- `conftest.py`: Test fixtures and data setup
- `test_app.py`: Core application functionality (root route, health check, error handlers)
- `test_auth.py`: Authentication endpoint tests
- `test_public_tenants.py`: Public tenant resolution endpoint tests
- `test_projects.py`: Project endpoint tests
- `test_issues.py`: Issue endpoint tests
- `test_jira.py`: JIRA credentials endpoint tests
- `test_tenants.py`: Tenant administration endpoint tests
- `test_cors.py`: CORS configuration and header tests
- `test_basic_auth.py`: Basic authentication tests
- `test_utils.py`: Utility functions for tests

## Key Testing Approaches

### Database Testing

- Uses in-memory SQLite database for isolated testing
- Creates test fixtures with predefined data
- Verifies database operations against expected results
- Tests tenant isolation at the database level

### Authentication Testing

- Tests login with valid and invalid credentials
- Verifies JWT token claims and expiration
- Tests token refresh functionality
- Tests logout and token invalidation
- Verifies authentication is required for protected endpoints

### API Testing

- Tests all API endpoints with expected inputs
- Verifies response status codes and content
- Tests error handling for invalid inputs
- Tests pagination and filtering
- Tests tenant isolation at the API level

### CORS Testing

- Verifies CORS headers are present
- Tests preflight requests with OPTIONS method
- Verifies allowed origins are correctly limited
- Tests CORS behavior with different origins

## Running Tests

### Basic Usage

```bash
# Run all tests
python -m pytest

# Run specific test file
python -m pytest tests/test_auth.py

# Run specific test function
python -m pytest tests/test_auth.py::test_login_success
```

### Advanced Usage

```bash
# Run with verbose output
python -m pytest -v

# Run with detailed output for failures
python -m pytest -vxs

# Run with coverage report
python -m pytest --cov=. --cov-report=term-missing

# Run with specific markers (if any)
python -m pytest -m "auth"
```

## Adding New Tests

When adding new tests:

1. Follow the existing naming patterns: `test_*.py` for files, `test_*` for functions
2. Use fixtures from `conftest.py` where possible
3. Focus on testing one thing per test function
4. Include both positive and negative test cases
5. Verify tenant isolation for all endpoint tests
6. Use descriptive function names that explain what's being tested
7. Add docstrings to test functions to explain the test purpose

## Test Data

The test data is created in `conftest.py` and includes:

- Test tenants with predefined IDs
- Test users with different roles
- Test JIRA credentials
- Test project cache entries
- Test issue cache entries

This data is created fresh for each test run to ensure test isolation.