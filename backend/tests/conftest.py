import os
import pytest
import tempfile
import uuid
import pathlib
import logging
import json
from dotenv import load_dotenv
from app import create_app, db
from models import Tenant, User, JiraCredential, ProjectCache, IssueCache, RefreshToken, RepositoryConfig
from utils import hash_password, encrypt_data
from flask_jwt_extended import create_access_token

# Load test environment variables
test_env_path = pathlib.Path(__file__).parent.parent / '.env.test'
load_dotenv(test_env_path)

# Set TESTING environment variable
os.environ['TESTING'] = 'True'

@pytest.fixture(scope='function')
def app():
    """Create and configure a Flask app for testing."""
    # Import and use the TestConfig that's already configured
    from config import TestConfig
    
    # Create app with test config
    
    app = create_app(TestConfig)
    
    # Create the database and load test data
    with app.app_context():
        db.create_all()
        _load_test_data()
    
    # Yield app for tests
    yield app

@pytest.fixture(scope='function')
def client(app):
    """A test client for the app."""
    return app.test_client()

@pytest.fixture(scope='module')
def runner(app):
    """A test CLI runner for the app."""
    return app.test_cli_runner()

def _load_test_data():
    """Load test data into the database."""
    # Create test tenants
    tenant_id = uuid.uuid4()  # Generate a new UUID for each test run
    test_tenant = Tenant(
        id=tenant_id,
        name='DefaultTenant',
        plan='basic'
    )
    db.session.add(test_tenant)

    # Create test users
    user_id = uuid.uuid4()
    test_user = User(
        id=user_id,
        tenant_id=tenant_id,
        email='<EMAIL>',
        password_hash='$argon2id$v=19$m=65536,t=3,p=4$rzXGuMeY0xrj3FvrvTfGmA$ewR0myTdPb5VWGrND/bbgKjI3mBUCZjAQuKVp5jpuXM',  # Pre-hashed 'password123'
        role='user'
    )
    admin_user = User(
        tenant_id=tenant_id,
        email='<EMAIL>',
        password_hash='$argon2id$v=19$m=65536,t=3,p=4$U7mDkUsUqqOXaf9XJ2Tw8Q$u0Hi/jUHmyDBrxFEaxQ7CmBXv5RhhomVfu7Hzg9XZ0M',  # Pre-hashed 'admin123'
        role='admin'
    )
    db.session.add(test_user)
    db.session.add(admin_user)

    # Create test JIRA credentials
    jira_credential = JiraCredential(
        tenant_id=test_tenant.id,
        user_id=test_user.id,
        base_url='https://testjira.atlassian.net',
        api_key_encrypted='dummy-encrypted-key',  # In a real scenario, this would be properly encrypted
        email='<EMAIL>'  # Added email field for JIRA API authentication
    )
    db.session.add(jira_credential)

    # Create test project cache
    project_cache = ProjectCache(
        tenant_id=test_tenant.id,
        project_key='TEST',
        json_data={
            'id': 'TEST-123',
            'name': 'Test Project',
            'key': 'TEST',
            'description': 'This is a test project'
        }
    )
    db.session.add(project_cache)

    # Create test issue cache
    issue_cache = IssueCache(
        tenant_id=test_tenant.id,
        issue_key='TEST-1',
        project_key='TEST',
        json_data={
            'id': 'TEST-1',
            'key': 'TEST-1',
            'summary': 'Test Issue',
            'description': 'This is a test issue',
            'status': 'Open',
            'priority': 'Medium'
        }
    )
    db.session.add(issue_cache)

    db.session.commit()

@pytest.fixture
def auth_headers(client, app):
    """Get auth headers with a valid JWT token."""
    # Log in to get tokens
    with app.app_context():
        from .test_utils import get_tenant_id
        tenant_id = get_tenant_id()
        
        response = client.post(f'/tenants/{tenant_id}/auth/login', 
                            json={
                                'email': '<EMAIL>',
                                'password': 'password123',
                                'tenant_id': tenant_id
                            })
        tokens = response.get_json()
        
        # Return headers with the access token
        return {
            'Authorization': f'Bearer {tokens["access_token"]}'
        }

@pytest.fixture
def admin_auth_headers(client, app):
    """Get auth headers with a valid admin JWT token."""
    # Log in to get tokens
    with app.app_context():
        from .test_utils import get_tenant_id
        tenant_id = get_tenant_id()
        
        response = client.post(f'/tenants/{tenant_id}/auth/login', 
                            json={
                                'email': '<EMAIL>',
                                'password': 'admin123',
                                'tenant_id': tenant_id
                            })
        tokens = response.get_json()
        
        # Return headers with the access token
        return {
            'Authorization': f'Bearer {tokens["access_token"]}'
        }

# New fixtures for PR automation

@pytest.fixture
def test_client(client):
    """Alias for the client fixture for compatibility."""
    return client

@pytest.fixture
def test_tenant(app):
    """Get the test tenant."""
    with app.app_context():
        return Tenant.query.filter_by(name='DefaultTenant').first()

@pytest.fixture
def test_tenant_2(app):
    """Create a second test tenant for cross-tenant testing."""
    with app.app_context():
        tenant_2 = Tenant(
            id=uuid.uuid4(),
            name='TenantTwo',
            plan='basic'
        )
        db.session.add(tenant_2)
        db.session.commit()
        return tenant_2

@pytest.fixture
def auth_headers_admin(app, test_tenant):
    """Get auth headers with admin role and includes tenant_id and user_id."""
    with app.app_context():
        admin_user = User.query.filter_by(
            email='<EMAIL>',
            tenant_id=test_tenant.id
        ).first()
        
        # Create access token with proper claims
        access_token = create_access_token(
            identity=str(admin_user.id),
            additional_claims={
                'tenant_id': str(test_tenant.id),
                'roles': ['admin'],
                'email': admin_user.email
            }
        )
        
        return {
            'tenant_id': str(test_tenant.id),
            'user_id': str(admin_user.id),
            'headers': {
                'Authorization': f'Bearer {access_token}'
            }
        }

@pytest.fixture
def auth_headers(app, test_tenant):
    """Get auth headers with user role and includes tenant_id and user_id."""
    with app.app_context():
        user = User.query.filter_by(
            email='<EMAIL>',
            tenant_id=test_tenant.id
        ).first()
        
        # Create access token with proper claims
        access_token = create_access_token(
            identity=str(user.id),
            additional_claims={
                'tenant_id': str(test_tenant.id),
                'roles': ['user'],
                'email': user.email
            }
        )
        
        return {
            'tenant_id': str(test_tenant.id),
            'user_id': str(user.id),
            'headers': {
                'Authorization': f'Bearer {access_token}'
            }
        }

@pytest.fixture
def test_repository(app, test_tenant, auth_headers_admin):
    """Create a test repository for PR tests."""
    with app.app_context():
        # Create encrypted credentials
        credentials = {
            'token': 'ghp_test_token_123456789'
        }
        # encrypt_data returns a tuple (nonce, ciphertext)
        nonce, ciphertext = encrypt_data(json.dumps(credentials))
        encrypted_creds = nonce + ciphertext  # Combine them as expected by the model
        
        repo = RepositoryConfig(
            id=uuid.uuid4(),
            tenant_id=test_tenant.id,
            repo_name='test-repository',
            repo_type='github',
            local_path='/tmp/test-repo',
            remote_url='https://github.com/example/test-repo.git',
            default_branch='main',
            credentials_encrypted=encrypted_creds,
            created_by=uuid.UUID(auth_headers_admin['user_id']),
            is_active=True
        )
        db.session.add(repo)
        db.session.commit()
        return repo