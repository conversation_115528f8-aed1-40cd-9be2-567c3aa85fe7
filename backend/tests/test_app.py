import pytest
import json

def test_root_endpoint(client):
    """Test the root endpoint."""
    response = client.get('/')
    
    assert response.status_code == 200
    data = json.loads(response.data)
    assert 'name' in data
    assert 'version' in data
    assert 'description' in data
    # The root endpoint should provide basic API information

def test_health_check(client):
    """Test the health check endpoint."""
    response = client.get('/health')
    
    assert response.status_code == 200
    data = json.loads(response.data)
    assert data['status'] == 'healthy'

def test_404_error_handler(client):
    """Test the 404 error handler."""
    response = client.get('/nonexistent-endpoint')
    
    assert response.status_code == 404
    data = json.loads(response.data)
    assert data['title'] == 'Not Found'
    assert data['status'] == 404

def test_400_error_handler(client):
    """Test the 400 error handler with malformed JSON."""
    response = client.post('/tenants/4e24f01c-ba6b-4527-bb45-3b92da0c1282/auth/login', 
                         data='not valid json',
                         content_type='application/json')
    
    assert response.status_code == 400
    data = json.loads(response.data)
    assert data['title'] == 'Bad Request'
    assert data['status'] == 400