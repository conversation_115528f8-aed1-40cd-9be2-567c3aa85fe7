import pytest
import json
import time
from flask_jwt_extended import decode_token
from flask import current_app
from .test_utils import get_tenant_id

def test_login_success(client, app):
    """Test successful login."""
    with app.app_context():
        tenant_id = get_tenant_id()
        
        response = client.post(f'/tenants/{tenant_id}/auth/login', 
                            json={
                                'email': '<EMAIL>',
                                'password': 'password123',
                                'tenant_id': tenant_id
                            })
    
    assert response.status_code == 200
    data = json.loads(response.data)
    assert 'access_token' in data
    assert 'refresh_token' in data

def test_login_missing_fields(client, app):
    """Test login with missing fields."""
    with app.app_context():
        tenant_id = get_tenant_id()
        
        # Missing email
        response = client.post(f'/tenants/{tenant_id}/auth/login', 
                            json={
                                'password': 'password123',
                                'tenant_id': tenant_id
                            })
        assert response.status_code == 400
        
        # Missing password
        response = client.post(f'/tenants/{tenant_id}/auth/login', 
                            json={
                                'email': '<EMAIL>',
                                'tenant_id': tenant_id
                            })
        assert response.status_code == 400
        
        # Missing tenant_id
        response = client.post(f'/tenants/{tenant_id}/auth/login', 
                            json={
                                'email': '<EMAIL>',
                                'password': 'password123'
                            })
        assert response.status_code == 400

def test_login_invalid_credentials(client, app):
    """Test login with invalid credentials."""
    with app.app_context():
        tenant_id = get_tenant_id()
        
        # Invalid password
        response = client.post(f'/tenants/{tenant_id}/auth/login', 
                            json={
                                'email': '<EMAIL>',
                                'password': 'wrongpassword',
                                'tenant_id': tenant_id
                            })
        assert response.status_code == 401
        
        # Invalid email
        response = client.post(f'/tenants/{tenant_id}/auth/login', 
                            json={
                                'email': '<EMAIL>',
                                'password': 'password123',
                                'tenant_id': tenant_id
                            })
        assert response.status_code == 401
        
        # Invalid tenant
        response = client.post('/tenants/00000000-0000-0000-0000-000000000000/auth/login', 
                            json={
                                'email': '<EMAIL>',
                                'password': 'password123',
                                'tenant_id': '00000000-0000-0000-0000-000000000000'
                            })
        assert response.status_code == 401

def test_token_claims(client, app):
    """Test that JWT tokens contain the expected claims."""
    with app.app_context():
        tenant_id = get_tenant_id()
        
        response = client.post(f'/tenants/{tenant_id}/auth/login', 
                            json={
                                'email': '<EMAIL>',
                                'password': 'password123',
                                'tenant_id': tenant_id
                            })
        
        data = json.loads(response.data)
        access_token = data['access_token']
        
        decoded = decode_token(access_token)
        assert decoded['tenant_id'] == tenant_id
        assert decoded['email'] == '<EMAIL>'
        assert 'user' in decoded['roles']

def test_refresh_token(client, app):
    """Test refreshing tokens."""
    with app.app_context():
        tenant_id = get_tenant_id()
        
        # Login to get tokens
        response = client.post(f'/tenants/{tenant_id}/auth/login', 
                            json={
                                'email': '<EMAIL>',
                                'password': 'password123',
                                'tenant_id': tenant_id
                            })
        
        data = json.loads(response.data)
        refresh_token = data['refresh_token']
        
        # Use refresh token to get a new access token
        response = client.post(f'/tenants/{tenant_id}/auth/refresh', 
                            headers={'Authorization': f'Bearer {refresh_token}'})
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert 'access_token' in data

def test_logout(client, auth_headers, app):
    """Test logging out."""
    with app.app_context():
        tenant_id = get_tenant_id()
        
        response = client.post(f'/tenants/{tenant_id}/auth/logout', 
                            headers=auth_headers)
        
        assert response.status_code == 200
        
        # Verify that the refresh token was invalidated by trying to use it
        response = client.post(f'/tenants/{tenant_id}/auth/login', 
                            json={
                                'email': '<EMAIL>',
                                'password': 'password123',
                                'tenant_id': tenant_id
                            })
        data = json.loads(response.data)
        refresh_token = data['refresh_token']
        
        # Log out
        response = client.post(f'/tenants/{tenant_id}/auth/logout', 
                            headers={'Authorization': f'Bearer {data["access_token"]}'})
        
        # Try to use the refresh token after logout
        response = client.post(f'/tenants/{tenant_id}/auth/refresh', 
                            headers={'Authorization': f'Bearer {refresh_token}'})
        
        assert response.status_code == 401  # Should fail after logout