import unittest
import os
import sys
import tempfile
import json
import uuid
from app import create_app, db
from models import Tenant, User

# Basic test class that doesn't depend on pytest
class BasicAppTestCase(unittest.TestCase):
    def setUp(self):
        # Create a temporary database
        self.db_fd, self.db_path = tempfile.mkstemp()
        
        # Set the database URI in environment variable
        os.environ['DATABASE_URL'] = f'sqlite:///{self.db_path}'
        
        # Set TESTING environment variable for password verification bypass
        os.environ['TESTING'] = 'True'
        
        # Configure the app for testing
        class TestConfig:
            TESTING = True
            SQLALCHEMY_DATABASE_URI = f'sqlite:///{self.db_path}'
            SQLALCHEMY_TRACK_MODIFICATIONS = False
            JWT_SECRET_KEY = 'test-secret-key'
        
        self.app = create_app(TestConfig)
        
        # Create a test client
        self.client = self.app.test_client()
        
        # Create the database and load test data
        with self.app.app_context():
            db.create_all()
            self._load_test_data()
    
    def tearDown(self):
        # Close and remove the temporary database
        os.close(self.db_fd)
        os.unlink(self.db_path)
    
    def _load_test_data(self):
        # Create a test tenant
        test_tenant = Tenant(
            id=uuid.UUID('4e24f01c-ba6b-4527-bb45-3b92da0c1282'),
            name='DefaultTenant',
            plan='basic'
        )
        db.session.add(test_tenant)
        
        # Create a test user
        test_user = User(
            id=uuid.UUID('5e67d912-bb8c-5638-cc56-4c93eb1d2393'),
            tenant_id=test_tenant.id,
            email='<EMAIL>',
            password_hash='$argon2id$v=19$m=65536,t=3,p=4$rzXGuMeY0xrj3FvrvTfGmA$ewR0myTdPb5VWGrND/bbgKjI3mBUCZjAQuKVp5jpuXM',  # Pre-hashed 'password123'
            role='user'
        )
        db.session.add(test_user)
        db.session.commit()
    
    def test_root_endpoint(self):
        """Test the root endpoint."""
        response = self.client.get('/')
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertIn('name', data)
        self.assertIn('version', data)
        
    def test_health_check(self):
        """Test the health check endpoint."""
        response = self.client.get('/health')
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertEqual(data['status'], 'healthy')
    
    def test_public_tenant_resolve(self):
        """Test tenant resolution."""
        response = self.client.get('/api/public/tenants/resolve?name=DefaultTenant')
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertEqual(data['id'], '4e24f01c-ba6b-4527-bb45-3b92da0c1282')
        self.assertEqual(data['name'], 'DefaultTenant')
    
    def test_login(self):
        """Test user login."""
        response = self.client.post('/tenants/4e24f01c-ba6b-4527-bb45-3b92da0c1282/auth/login', 
                                 json={
                                     'email': '<EMAIL>',
                                     'password': 'password123',
                                     'tenant_id': '4e24f01c-ba6b-4527-bb45-3b92da0c1282'
                                 })
        self.assertEqual(response.status_code, 200)
        data = json.loads(response.data)
        self.assertIn('access_token', data)
        self.assertIn('refresh_token', data)

if __name__ == '__main__':
    unittest.main()