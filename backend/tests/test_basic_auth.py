import pytest
import json
from flask_jwt_extended import jwt_required, create_access_token

def test_auth_required_endpoints(client, app):
    """Test that authentication is required for protected endpoints."""
    with app.app_context():
        # Create a test token
        test_token = create_access_token(
            identity="test-user",
            additional_claims={
                "tenant_id": "00000000-0000-0000-0000-000000000001",
                "roles": ["user"],
                "email": "<EMAIL>"
            }
        )
        
        # Test headers
        auth_headers = {"Authorization": f"Bearer {test_token}"}
        
        # Test protected endpoints
        endpoints = [
            "/tenants/00000000-0000-0000-0000-000000000001/projects",
            "/tenants/00000000-0000-0000-0000-000000000001/issues/TEST-1",
            "/tenants/00000000-0000-0000-0000-000000000001/jira/credentials",
            "/admin/tenants/00000000-0000-0000-0000-000000000001"
        ]
        
        # First test without authentication - should be unauthorized
        for endpoint in endpoints:
            response = client.get(endpoint)
            assert response.status_code in [401, 404, 308], f"Expected 401/404/308 for {endpoint} without auth, got {response.status_code}"
        
        # Then test with authentication - should be authorized or not found
        for endpoint in endpoints:
            response = client.get(endpoint, headers=auth_headers)
            assert response.status_code in [200, 404, 308], f"Expected 200/404/308 for {endpoint} with auth, got {response.status_code}"