import pytest

def test_cors_headers(client):
    """Test that CORS headers are included in responses."""
    # Test a simple endpoint
    headers = {
        'Origin': 'http://localhost:3000',
        'Access-Control-Request-Method': 'GET',
        'Access-Control-Request-Headers': 'Authorization'
    }
    
    # Make a normal request
    response = client.get('/health', headers=headers)
    
    # Check for CORS headers
    assert 'Access-Control-Allow-Origin' in response.headers
    
    # Make an OPTIONS request to check CORS preflight with the proper headers
    preflight_resp = client.options('/health', headers=headers)
    assert preflight_resp.status_code == 200
    assert 'Access-Control-Allow-Origin' in preflight_resp.headers
    
    # For Flask-CORS, verify that we get at least the correct origin
    origins = response.headers.get('Access-Control-Allow-Origin')
    assert origins in ['http://localhost:3000', 'http://127.0.0.1:3000', '*']