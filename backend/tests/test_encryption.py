"""Test file for encryption/decryption utilities."""
import pytest
import os
from utils import encrypt_data, decrypt_data, _get_aes_key

def test_aes_key_loading():
    """Test that the AES key can be loaded from environment variables."""
    # The test environment should have APPLICATION_AES_KEY set in .env.test
    key = _get_aes_key()
    
    # Should be 32 bytes (256 bits)
    assert len(key) == 32
    
    # Type should be bytes
    assert isinstance(key, bytes)

def test_encryption_decryption():
    """Test that encryption and decryption work properly."""
    # Test data
    test_data = "This is a test API key: AT_12345abcdef"
    
    # Encrypt the data
    nonce, ciphertext = encrypt_data(test_data)
    
    # Make sure we got the expected types
    assert isinstance(nonce, bytes)
    assert isinstance(ciphertext, bytes)
    
    # Make sure the nonce is 12 bytes (96 bits, standard for GCM)
    assert len(nonce) == 12
    
    # Decrypt the data
    decrypted = decrypt_data(nonce, ciphertext)
    
    # Make sure we got back the original text
    assert decrypted == test_data
    
    # Try with a different nonce, should fail
    with pytest.raises(Exception):
        fake_nonce = os.urandom(12)
        decrypt_data(fake_nonce, ciphertext)

def test_encryption_with_special_characters():
    """Test that encryption works with special characters."""
    # Test data with special characters
    test_data = "JIRA API Key: AT_123!@#$%^&*()_+=-[]{}|;':,./<>?"
    
    # Encrypt the data
    nonce, ciphertext = encrypt_data(test_data)
    
    # Decrypt the data
    decrypted = decrypt_data(nonce, ciphertext)
    
    # Make sure we got back the original text
    assert decrypted == test_data

def test_encryption_base64_format():
    """Test the base64 encoding format used for storage."""
    import base64
    
    # Test data
    test_data = "JIRA API Key: AT_123abc"
    
    # Encrypt the data
    nonce, ciphertext = encrypt_data(test_data)
    
    # Convert to base64 for storage
    nonce_b64 = base64.b64encode(nonce).decode('utf-8')
    ciphertext_b64 = base64.b64encode(ciphertext).decode('utf-8')
    
    # Combined format
    encrypted_data = f"{nonce_b64}:{ciphertext_b64}"
    
    # Should be a string with a colon separator
    assert ':' in encrypted_data
    assert encrypted_data.count(':') == 1
    
    # Split and decode
    n_b64, c_b64 = encrypted_data.split(':')
    n = base64.b64decode(n_b64)
    c = base64.b64decode(c_b64)
    
    # Decrypt
    decrypted = decrypt_data(n, c)
    
    # Make sure we got back the original text
    assert decrypted == test_data