import pytest
import json
from .test_utils import get_tenant_id

def test_list_issues_for_project(client, auth_headers, app):
    """Test listing issues for a project."""
    with app.app_context():
        tenant_id = get_tenant_id()
        response = client.get(f'/tenants/{tenant_id}/projects/TEST/issues', 
                           headers=auth_headers)
    
    assert response.status_code == 200
    data = json.loads(response.data)
    assert 'issues' in data
    assert isinstance(data['issues'], list)
    # Should return at least our test issue
    assert len(data['issues']) >= 1

def test_list_issues_with_filters(client, auth_headers, app):
    """Test listing issues with filters."""
    with app.app_context():
        tenant_id = get_tenant_id()
        
        # Test with type filter
        response = client.get(f'/tenants/{tenant_id}/projects/TEST/issues?type=Bug', 
                            headers=auth_headers)
        
        assert response.status_code == 200
        
        # Test with pagination
        response = client.get(f'/tenants/{tenant_id}/projects/TEST/issues?page=1&per_page=10', 
                            headers=auth_headers)
        
        assert response.status_code == 200

def test_get_issue_details(client, auth_headers, app):
    """Test getting details for a specific issue."""
    with app.app_context():
        tenant_id = get_tenant_id()
        response = client.get(f'/tenants/{tenant_id}/issues/TEST-1', 
                            headers=auth_headers)
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['key'] == 'TEST-1'
        assert 'summary' in data
        assert 'description' in data

def test_get_nonexistent_issue(client, auth_headers, app):
    """Test getting details for a non-existent issue."""
    with app.app_context():
        tenant_id = get_tenant_id()
        response = client.get(f'/tenants/{tenant_id}/issues/NONEXISTENT-999', 
                            headers=auth_headers)
        
        assert response.status_code == 404

def test_issues_unauthenticated(client, app):
    """Test accessing issues endpoints without authentication."""
    with app.app_context():
        tenant_id = get_tenant_id()
        
        # List issues
        response = client.get(f'/tenants/{tenant_id}/projects/TEST/issues')
        assert response.status_code == 401
        
        # Get issue details
        response = client.get(f'/tenants/{tenant_id}/issues/TEST-1')
        assert response.status_code == 401