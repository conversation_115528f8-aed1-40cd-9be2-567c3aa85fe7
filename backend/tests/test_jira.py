import pytest
import json
import uuid
from .test_utils import get_tenant_id
from unittest.mock import patch

# Mock the verify_jira_credentials function to always return True in tests
@pytest.fixture
def mock_verify_jira():
    with patch('utils.verify_jira_credentials', return_value=True):
        yield

def test_get_jira_credentials_status(client, auth_headers, app):
    """Test getting JIRA credentials status."""
    with app.app_context():
        tenant_id = get_tenant_id()
        response = client.get(f'/tenants/{tenant_id}/jira/credentials', 
                           headers=auth_headers)
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert 'configured' in data
        # Initially, credentials might not be set up
        # assert data['configured'] is True

def test_manage_jira_credentials(client, auth_headers, app, mock_verify_jira):
    """Test managing JIRA credentials."""
    with app.app_context():
        tenant_id = get_tenant_id()
        
        # Update credentials
        response = client.post(f'/tenants/{tenant_id}/jira/credentials', 
                             json={
                                 'base_url': 'https://updatedjira.atlassian.net',
                                 'api_key': 'updated-api-key'
                             },
                             headers=auth_headers)
        
        assert response.status_code == 201
        data = json.loads(response.data)
        assert data['success'] is True
        assert data['base_url'] == 'https://updatedjira.atlassian.net'
        
        # Verify the update with GET request
        response = client.get(f'/tenants/{tenant_id}/jira/credentials', 
                            headers=auth_headers)
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['configured'] is True
        assert data['base_url'] == 'https://updatedjira.atlassian.net'
        
        # Test deletion of credentials
        response = client.delete(f'/tenants/{tenant_id}/jira/credentials',
                              headers=auth_headers)
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['success'] is True
        
        # Verify deletion
        response = client.get(f'/tenants/{tenant_id}/jira/credentials', 
                           headers=auth_headers)
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['configured'] is False

def test_manage_jira_credentials_invalid(client, auth_headers, app, mock_verify_jira):
    """Test managing JIRA credentials with invalid data."""
    with app.app_context():
        tenant_id = get_tenant_id()
        
        # Missing base_url
        response = client.post(f'/tenants/{tenant_id}/jira/credentials', 
                             json={
                                 'api_key': 'some-api-key'
                             },
                             headers=auth_headers)
        
        assert response.status_code == 400
        
        # Missing api_key
        response = client.post(f'/tenants/{tenant_id}/jira/credentials', 
                             json={
                                 'base_url': 'https://somejira.atlassian.net'
                             },
                             headers=auth_headers)
        
        assert response.status_code == 400
        
        # Empty request body
        response = client.post(f'/tenants/{tenant_id}/jira/credentials', 
                             json={},
                             headers=auth_headers)
        
        assert response.status_code == 400
        
        # Invalid tenant_id format
        response = client.post('/tenants/not-a-uuid/jira/credentials', 
                             json={
                                 'base_url': 'https://somejira.atlassian.net',
                                 'api_key': 'some-api-key'
                             },
                             headers=auth_headers)
        
        # Either 400 or 404 is acceptable (depends on how routing is set up)
        assert response.status_code in [400, 404]

def test_jira_endpoints_unauthenticated(client, app):
    """Test accessing JIRA endpoints without authentication."""
    with app.app_context():
        tenant_id = get_tenant_id()
        
        # Get credentials status
        response = client.get(f'/tenants/{tenant_id}/jira/credentials')
        assert response.status_code == 401
        
        # Update credentials
        response = client.post(f'/tenants/{tenant_id}/jira/credentials', 
                             json={
                                 'base_url': 'https://somejira.atlassian.net',
                                 'api_key': 'some-api-key'
                             })
        assert response.status_code == 401
        
        # Delete credentials
        response = client.delete(f'/tenants/{tenant_id}/jira/credentials')
        assert response.status_code == 401

def test_access_cross_tenant_jira(client, auth_headers, app):
    """Test accessing JIRA endpoints for another tenant."""
    with app.app_context():
        # Create a different tenant UUID
        different_tenant_id = str(uuid.uuid4())
        
        # Try to access the credentials of another tenant
        response = client.get(f'/tenants/{different_tenant_id}/jira/credentials', 
                           headers=auth_headers)
        
        # Should be forbidden unless the user is admin
        assert response.status_code in [403, 200]  # 200 if admin, 403 otherwise