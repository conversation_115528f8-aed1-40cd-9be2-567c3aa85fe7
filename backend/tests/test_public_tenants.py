import pytest
import json
from .test_utils import get_tenant_id

def test_resolve_tenant_success(client, app):
    """Test successfully resolving a tenant by name."""
    with app.app_context():
        tenant_id = get_tenant_id()
    
        response = client.get('/api/public/tenants/resolve?name=DefaultTenant')
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['name'] == 'DefaultTenant'
        assert data['plan'] == 'basic'

def test_resolve_tenant_case_insensitive(client, app):
    """Test case-insensitive tenant name resolution."""
    with app.app_context():
        tenant_id = get_tenant_id()
        
        response = client.get('/api/public/tenants/resolve?name=defaulttenant')
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['name'].lower() == 'defaulttenant'

def test_resolve_tenant_not_found(client):
    """Test resolving a non-existent tenant."""
    response = client.get('/api/public/tenants/resolve?name=NonExistentTenant')
    
    assert response.status_code == 404
    data = json.loads(response.data)
    assert data['title'] == 'Not Found'
    assert 'NonExistentTenant' in data['detail']

def test_resolve_tenant_missing_name(client):
    """Test resolving a tenant without providing a name."""
    response = client.get('/api/public/tenants/resolve')
    
    assert response.status_code == 400
    data = json.loads(response.data)
    assert data['title'] == 'Bad Request'
    assert 'required' in data['detail'].lower()