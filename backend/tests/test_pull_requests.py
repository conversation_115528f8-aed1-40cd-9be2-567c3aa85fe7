"""Tests for pull request management endpoints."""
import pytest
import json
from uuid import uuid4
from unittest.mock import patch, MagicMock
# Add parent directory to path for imports
import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from models import PullRequest, RepositoryConfig, IssueCache, db


class TestPullRequestEndpoints:
    """Test suite for pull request management endpoints."""
    
    @pytest.fixture
    def test_repository(self, test_client, auth_headers_admin):
        """Create a test repository for PR tests."""
        with patch('services.repository_service.RepositoryService._clone_repository'):
            repo_data = {
                'repo_name': 'pr-test-repo',
                'repo_type': 'github',
                'remote_url': 'https://github.com/example/pr-test-repo.git',
                'token': 'ghp_test_token_123456789',
                'default_branch': 'main'
            }
            
            response = test_client.post(
                f'/tenants/{auth_headers_admin["tenant_id"]}/repositories',
                headers=auth_headers_admin['headers'],
                json=repo_data
            )
            assert response.status_code == 201
            return json.loads(response.data)
    
    @patch('services.pr_service.PullRequestService._create_platform_pr')
    @patch('services.pr_service.PullRequestService._create_feature_branch')
    @patch('services.pr_service.PullRequestService._get_issue_data')
    def test_create_pull_request(self, mock_issue_data, mock_create_branch, mock_create_pr, 
                                test_client, auth_headers, test_repository):
        """Test creating a pull request."""
        # Mock the issue data
        mock_issue_data.return_value = {
            'key': 'TEST-123',
            'summary': 'Test issue summary',
            'description': 'Test issue description'
        }
        
        # Mock branch creation
        mock_create_branch.return_value = 'feature/test-123'
        
        # Mock PR creation on platform
        mock_create_pr.return_value = {
            'number': 1,
            'url': 'https://github.com/example/repo/pull/1',
            'state': 'open'
        }
        
        pr_data = {
            'repo_config_id': test_repository['id'],
            'issue_key': 'TEST-123',
            'title': 'Fix: Test issue resolution',
            'target_branch': 'main'
        }
        
        response = test_client.post(
            f'/tenants/{auth_headers["tenant_id"]}/pull-requests/create',
            headers=auth_headers['headers'],
            json=pr_data
        )
        assert response.status_code == 201
        data = json.loads(response.data)
        assert data['issue_key'] == 'TEST-123'
        assert data['status'] == 'created'
        assert 'source_branch' in data
        assert 'pr_url' in data
    
    def test_create_pr_missing_fields(self, test_client, auth_headers):
        """Test creating a PR with missing required fields."""
        pr_data = {
            'issue_key': 'TEST-123'
            # Missing repo_config_id
        }
        
        response = test_client.post(
            f'/tenants/{auth_headers["tenant_id"]}/pull-requests/create',
            headers=auth_headers['headers'],
            json=pr_data
        )
        assert response.status_code == 400
        data = json.loads(response.data)
        assert 'Missing required field' in data['error']
    
    @patch('services.pr_service.PullRequestService._create_platform_pr')
    @patch('services.pr_service.PullRequestService._create_feature_branch')
    @patch('services.pr_service.PullRequestService._get_issue_data')
    def test_list_pull_requests(self, mock_issue_data, mock_create_branch, mock_create_pr,
                               test_client, auth_headers, test_repository):
        """Test listing pull requests."""
        # Mock data for PR creation
        mock_issue_data.return_value = {'key': 'TEST-100', 'summary': 'Test issue'}
        mock_create_branch.return_value = 'feature/test-100'
        mock_create_pr.return_value = {'number': 1, 'url': 'https://example.com/pr/1', 'state': 'open'}
        
        # Create a few PRs
        for i in range(3):
            pr_data = {
                'repo_config_id': test_repository['id'],
                'issue_key': f'TEST-{100 + i}',
                'title': f'Fix: Test issue {i}'
            }
            
            response = test_client.post(
                f'/tenants/{auth_headers["tenant_id"]}/pull-requests/create',
                headers=auth_headers['headers'],
                json=pr_data
            )
            assert response.status_code == 201
        
        # List all PRs
        response = test_client.get(
            f'/tenants/{auth_headers["tenant_id"]}/pull-requests',
            headers=auth_headers['headers']
        )
        assert response.status_code == 200
        data = json.loads(response.data)
        assert 'pull_requests' in data
        assert len(data['pull_requests']) >= 3
    
    def test_list_prs_with_filters(self, test_client, auth_headers, test_tenant):
        """Test listing PRs with filters."""
        # Create a PR manually in database for filtering
        pr = PullRequest(
            tenant_id=test_tenant.id,
            repo_config_id=uuid4(),
            issue_key='TEST-200',
            title='Fix: Specific issue',
            source_branch='feature/test-200',
            target_branch='main',
            status='created',
            created_by=auth_headers['user_id']
        )
        db.session.add(pr)
        db.session.commit()
        
        # List PRs filtered by issue key
        response = test_client.get(
            f'/tenants/{auth_headers["tenant_id"]}/pull-requests?issue_key=TEST-200',
            headers=auth_headers['headers']
        )
        assert response.status_code == 200
        data = json.loads(response.data)
        assert len(data['pull_requests']) >= 1
        
        # Find our PR in the results
        found_pr = next((p for p in data['pull_requests'] if p['issue_key'] == 'TEST-200'), None)
        assert found_pr is not None
        assert found_pr['title'] == 'Fix: Specific issue'
    
    def test_get_pull_request_details(self, test_client, auth_headers, test_tenant):
        """Test getting PR details."""
        # Create a PR in database
        pr = PullRequest(
            tenant_id=test_tenant.id,
            repo_config_id=uuid4(),
            issue_key='TEST-300',
            title='Fix: Detailed test issue',
            description='Detailed description',
            source_branch='feature/test-300',
            target_branch='main',
            status='created',
            created_by=auth_headers['user_id'],
            pr_metadata={'custom': 'data'}
        )
        db.session.add(pr)
        db.session.commit()
        
        # Get PR details
        response = test_client.get(
            f'/tenants/{auth_headers["tenant_id"]}/pull-requests/{pr.id}',
            headers=auth_headers['headers']
        )
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['issue_key'] == 'TEST-300'
        assert data['description'] == 'Detailed description'
        assert 'pr_metadata' in data
    
    def test_get_issue_pull_requests(self, test_client, auth_headers, test_tenant):
        """Test getting all PRs for a specific issue."""
        issue_key = 'TEST-400'
        
        # Create multiple PRs for the same issue
        for i in range(2):
            pr = PullRequest(
                tenant_id=test_tenant.id,
                repo_config_id=uuid4(),
                issue_key=issue_key,
                title=f'Fix: Attempt {i+1}',
                source_branch=f'fix/test-400-attempt-{i+1}',
                target_branch='main',
                status='created',
                created_by=auth_headers['user_id']
            )
            db.session.add(pr)
        db.session.commit()
        
        # Get PRs for the issue
        response = test_client.get(
            f'/tenants/{auth_headers["tenant_id"]}/pull-requests/issues/{issue_key}/pull-requests',
            headers=auth_headers['headers']
        )
        assert response.status_code == 200
        data = json.loads(response.data)
        assert len(data['pull_requests']) >= 2
        assert all(pr['issue_key'] == issue_key for pr in data['pull_requests'])
    
    @patch('services.pr_service.PullRequestService._create_platform_pr')
    @patch('services.pr_service.PullRequestService._create_feature_branch')
    @patch('services.pr_service.PullRequestService._get_issue_data')
    def test_create_pr_for_issue_endpoint(self, mock_issue_data, mock_create_branch, mock_create_pr,
                                        test_client, auth_headers, test_repository):
        """Test creating a PR using the issue-specific endpoint."""
        # Mock data
        issue_key = 'TEST-500'
        mock_issue_data.return_value = {
            'key': issue_key,
            'summary': 'Issue via specific endpoint'
        }
        mock_create_branch.return_value = f'feature/{issue_key.lower()}'
        mock_create_pr.return_value = {
            'number': 5,
            'url': 'https://github.com/example/repo/pull/5',
            'state': 'open'
        }
        
        pr_data = {
            'repo_config_id': test_repository['id'],
            'title': 'Fix: Issue via specific endpoint'
        }
        
        response = test_client.post(
            f'/tenants/{auth_headers["tenant_id"]}/pull-requests/issues/{issue_key}/create-pr',
            headers=auth_headers['headers'],
            json=pr_data
        )
        assert response.status_code == 201
        data = json.loads(response.data)
        assert data['issue_key'] == issue_key
        assert data['title'] == 'Fix: Issue via specific endpoint'
    
    @patch('services.pr_service.PullRequestService._get_platform_pr_status')
    def test_sync_pull_request_status(self, mock_get_status, test_client, auth_headers, test_tenant):
        """Test syncing PR status with platform."""
        # Create a PR in database
        pr = PullRequest(
            tenant_id=test_tenant.id,
            repo_config_id=uuid4(),
            issue_key='TEST-600',
            title='Fix: Sync test issue',
            source_branch='feature/test-600',
            target_branch='main',
            status='created',
            pr_number=1,
            created_by=auth_headers['user_id']
        )
        db.session.add(pr)
        db.session.commit()
        
        # Mock platform status response
        mock_get_status.return_value = {
            'state': 'merged',
            'merged': True,
            'updated_at': '2023-05-01T12:00:00Z'
        }
        
        # Sync PR status
        response = test_client.post(
            f'/tenants/{auth_headers["tenant_id"]}/pull-requests/{pr.id}/sync',
            headers=auth_headers['headers']
        )
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['status'] == 'merged'
        assert 'pr_metadata' in data
    
    def test_pr_not_found(self, test_client, auth_headers):
        """Test accessing non-existent PR."""
        fake_pr_id = str(uuid4())
        
        response = test_client.get(
            f'/tenants/{auth_headers["tenant_id"]}/pull-requests/{fake_pr_id}',
            headers=auth_headers['headers']
        )
        assert response.status_code == 404
        data = json.loads(response.data)
        assert 'not found' in data['error'].lower()
    
    def test_cross_tenant_pr_access_denied(self, test_client, auth_headers, test_tenant_2):
        """Test that users cannot access PRs from other tenants."""
        # Try to create PR with tenant 2 context using tenant 1 auth
        response = test_client.post(
            f'/tenants/{test_tenant_2.id}/pull-requests/create',
            headers=auth_headers['headers'],
            json={'repo_config_id': str(uuid4()), 'issue_key': 'TEST-700'}
        )
        assert response.status_code == 403
        data = json.loads(response.data)
        assert 'Tenant context mismatch' in data['error']
    
    def test_pr_status_update(self, test_client, auth_headers, test_tenant):
        """Test that PR status transitions work correctly."""
        # Create a PR in pending status
        pr = PullRequest(
            tenant_id=test_tenant.id,
            repo_config_id=uuid4(),
            issue_key='TEST-800',
            title='Fix: Status test',
            source_branch='feature/test-800',
            target_branch='main',
            status='pending',
            created_by=auth_headers['user_id']
        )
        db.session.add(pr)
        db.session.commit()
        
        # Verify initial status
        assert pr.status == 'pending'
        
        # Mock sync to update status
        with patch('services.pr_service.PullRequestService._get_platform_pr_status') as mock_status:
            mock_status.return_value = {'state': 'open'}
            
            response = test_client.post(
                f'/tenants/{auth_headers["tenant_id"]}/pull-requests/{pr.id}/sync',
                headers=auth_headers['headers']
            )
            assert response.status_code == 200
            
            # Verify status was updated
            updated_pr = PullRequest.query.get(pr.id)
            assert updated_pr.status == 'open'
    
    def test_pr_repository_relationship(self, test_client, auth_headers, test_repository):
        """Test that PRs are properly linked to repositories."""
        # Create a PR with specific repository
        with patch('services.pr_service.PullRequestService._create_platform_pr'), \
             patch('services.pr_service.PullRequestService._create_feature_branch'), \
             patch('services.pr_service.PullRequestService._get_issue_data'):
            
            pr_data = {
                'repo_config_id': test_repository['id'],
                'issue_key': 'TEST-900',
                'title': 'Fix: Repo relationship test'
            }
            
            response = test_client.post(
                f'/tenants/{auth_headers["tenant_id"]}/pull-requests/create',
                headers=auth_headers['headers'],
                json=pr_data
            )
            assert response.status_code == 201
            
            created_pr_id = json.loads(response.data)['id']
            
            # Verify PR is linked to correct repository
            pr = PullRequest.query.get(created_pr_id)
            assert str(pr.repo_config_id) == test_repository['id']
            assert pr.repository_config is not None