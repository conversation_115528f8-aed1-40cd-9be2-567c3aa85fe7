"""Tests for repository management endpoints."""
import pytest
import json
from uuid import uuid4
from unittest.mock import patch, MagicMock
# Add parent directory to path for imports
import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from models import RepositoryConfig, Tenant, User, db


class TestRepositoryEndpoints:
    """Test suite for repository management endpoints."""
    
    def test_list_repositories_empty(self, test_client, auth_headers):
        """Test listing repositories when none exist."""
        response = test_client.get(
            f'/tenants/{auth_headers["tenant_id"]}/repositories',
            headers=auth_headers['headers']
        )
        assert response.status_code == 200
        data = json.loads(response.data)
        assert 'repositories' in data
        assert data['repositories'] == []
    
    @patch('services.repository_service.RepositoryService._clone_repository')
    @patch('services.repository_service.RepositoryService._validate_github_credentials')
    def test_add_repository_github(self, mock_validate, mock_clone, test_client, auth_headers_admin):
        """Test adding a GitHub repository."""
        mock_validate.return_value = True
        mock_clone.return_value = None
        
        repo_data = {
            'repo_name': 'test-repo',
            'repo_type': 'github',
            'remote_url': 'https://github.com/example/test-repo.git',
            'token': 'ghp_test_token_123456789',
            'default_branch': 'main'
        }
        
        response = test_client.post(
            f'/tenants/{auth_headers_admin["tenant_id"]}/repositories',
            headers=auth_headers_admin['headers'],
            json=repo_data
        )
        assert response.status_code == 201
        data = json.loads(response.data)
        assert data['repo_name'] == 'test-repo'
        assert data['repo_type'] == 'github'
        assert data['is_active'] is True
    
    def test_add_repository_missing_token(self, test_client, auth_headers_admin):
        """Test adding a GitHub repository without token fails."""
        repo_data = {
            'repo_name': 'test-repo',
            'repo_type': 'github',
            'remote_url': 'https://github.com/example/test-repo.git'
        }
        
        response = test_client.post(
            f'/tenants/{auth_headers_admin["tenant_id"]}/repositories',
            headers=auth_headers_admin['headers'],
            json=repo_data
        )
        assert response.status_code == 400
        data = json.loads(response.data)
        assert 'error' in data
        assert 'token' in data['error'].lower()
    
    @patch('services.repository_service.RepositoryService._clone_repository')
    @patch('services.repository_service.RepositoryService._validate_bitbucket_credentials')
    def test_add_repository_bitbucket(self, mock_validate, mock_clone, test_client, auth_headers_admin):
        """Test adding a Bitbucket repository."""
        mock_validate.return_value = True
        mock_clone.return_value = None
        
        repo_data = {
            'repo_name': 'test-repo-bb',
            'repo_type': 'bitbucket',
            'remote_url': 'https://bitbucket.org/example/test-repo.git',
            'username': 'test_user',
            'app_password': 'app_password_123',
            'default_branch': 'develop'
        }
        
        response = test_client.post(
            f'/tenants/{auth_headers_admin["tenant_id"]}/repositories',
            headers=auth_headers_admin['headers'],
            json=repo_data
        )
        assert response.status_code == 201
        data = json.loads(response.data)
        assert data['repo_name'] == 'test-repo-bb'
        assert data['repo_type'] == 'bitbucket'
        assert data['default_branch'] == 'develop'
    
    @patch('services.repository_service.RepositoryService._clone_repository')
    def test_add_duplicate_repository(self, mock_clone, test_client, auth_headers_admin):
        """Test adding a duplicate repository fails."""
        mock_clone.return_value = None
        
        repo_data = {
            'repo_name': 'duplicate-repo',
            'repo_type': 'github',
            'remote_url': 'https://github.com/example/duplicate-repo.git',
            'token': 'ghp_test_token_123456789'
        }
        
        # Add first repository
        response = test_client.post(
            f'/tenants/{auth_headers_admin["tenant_id"]}/repositories',
            headers=auth_headers_admin['headers'],
            json=repo_data
        )
        assert response.status_code == 201
        
        # Try to add duplicate
        response = test_client.post(
            f'/tenants/{auth_headers_admin["tenant_id"]}/repositories',
            headers=auth_headers_admin['headers'],
            json=repo_data
        )
        assert response.status_code == 400
        data = json.loads(response.data)
        assert 'already exists' in data['error']
    
    @patch('services.repository_service.RepositoryService._clone_repository')
    def test_get_repository_details(self, mock_clone, test_client, auth_headers_admin):
        """Test getting repository details."""
        mock_clone.return_value = None
        
        # First add a repository
        repo_data = {
            'repo_name': 'details-repo',
            'repo_type': 'github',
            'remote_url': 'https://github.com/example/details-repo.git',
            'token': 'ghp_test_token_123456789'
        }
        
        add_response = test_client.post(
            f'/tenants/{auth_headers_admin["tenant_id"]}/repositories',
            headers=auth_headers_admin['headers'],
            json=repo_data
        )
        assert add_response.status_code == 201
        repo_id = json.loads(add_response.data)['id']
        
        # Get repository details
        response = test_client.get(
            f'/tenants/{auth_headers_admin["tenant_id"]}/repositories/{repo_id}',
            headers=auth_headers_admin['headers']
        )
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['repo_name'] == 'details-repo'
        assert data['is_active'] is True
    
    @patch('services.repository_service.RepositoryService._git_pull')
    def test_sync_repository(self, mock_git_pull, test_client, auth_headers, test_repository):
        """Test syncing repository with remote."""
        mock_git_pull.return_value = "Already up to date."
        
        response = test_client.post(
            f'/tenants/{auth_headers["tenant_id"]}/repositories/{test_repository.id}/sync',
            headers=auth_headers['headers']
        )
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['status'] == 'success'
    
    def test_delete_repository(self, test_client, auth_headers_admin, test_repository):
        """Test deleting (deactivating) a repository."""
        response = test_client.delete(
            f'/tenants/{auth_headers_admin["tenant_id"]}/repositories/{test_repository.id}',
            headers=auth_headers_admin['headers']
        )
        assert response.status_code == 204
        
        # Verify it's deactivated
        repo = RepositoryConfig.query.get(test_repository.id)
        assert repo.is_active is False
    
    def test_list_repositories_with_data(self, test_client, auth_headers, test_repository):
        """Test listing repositories when some exist."""
        response = test_client.get(
            f'/tenants/{auth_headers["tenant_id"]}/repositories',
            headers=auth_headers['headers']
        )
        assert response.status_code == 200
        data = json.loads(response.data)
        assert len(data['repositories']) >= 1
        
        # Check the repository data
        repo_data = next(r for r in data['repositories'] if r['id'] == str(test_repository.id))
        assert repo_data['repo_name'] == test_repository.repo_name
        assert repo_data['repo_type'] == test_repository.repo_type
    
    def test_update_repository(self, test_client, auth_headers_admin, test_repository):
        """Test updating repository configuration."""
        update_data = {
            'default_branch': 'develop',
            'is_active': False
        }
        
        response = test_client.put(
            f'/tenants/{auth_headers_admin["tenant_id"]}/repositories/{test_repository.id}',
            headers=auth_headers_admin['headers'],
            json=update_data
        )
        assert response.status_code == 200
        data = json.loads(response.data)
        assert data['default_branch'] == 'develop'
        assert data['is_active'] is False
    
    def test_repository_activity(self, test_client, auth_headers, test_repository):
        """Test getting repository activity logs."""
        response = test_client.get(
            f'/tenants/{auth_headers["tenant_id"]}/repositories/{test_repository.id}/activity',
            headers=auth_headers['headers']
        )
        assert response.status_code == 200
        data = json.loads(response.data)
        assert 'activities' in data
        assert isinstance(data['activities'], list)
    
    def test_test_repository_connection(self, test_client, auth_headers, test_repository):
        """Test repository connection validation."""
        with patch('services.repository_service.RepositoryService.sync_repository') as mock_sync:
            mock_sync.return_value = {'status': 'success'}
            
            response = test_client.post(
                f'/tenants/{auth_headers["tenant_id"]}/repositories/{test_repository.id}/test',
                headers=auth_headers['headers']
            )
            assert response.status_code == 200
            data = json.loads(response.data)
            assert data['status'] == 'success'
    
    def test_cross_tenant_access_denied(self, test_client, auth_headers, test_tenant_2):
        """Test that users cannot access repositories from other tenants."""
        response = test_client.get(
            f'/tenants/{test_tenant_2.id}/repositories',
            headers=auth_headers['headers']
        )
        assert response.status_code == 403
        data = json.loads(response.data)
        assert 'Tenant context mismatch' in data['error']
    
    def test_non_admin_cannot_add_repository(self, test_client, auth_headers):
        """Test that non-admin users cannot add repositories."""
        repo_data = {
            'repo_name': 'non-admin-repo',
            'repo_type': 'github',
            'remote_url': 'https://github.com/example/non-admin-repo.git',
            'token': 'ghp_test_token_123456789'
        }
        
        response = test_client.post(
            f'/tenants/{auth_headers["tenant_id"]}/repositories',
            headers=auth_headers['headers'],
            json=repo_data
        )
        assert response.status_code == 403
        data = json.loads(response.data)
        assert 'Admin role required' in data['error']
    
    def test_invalid_repo_type(self, test_client, auth_headers_admin):
        """Test that invalid repository types are rejected."""
        repo_data = {
            'repo_name': 'invalid-type-repo',
            'repo_type': 'gitlab',  # Not supported
            'remote_url': 'https://gitlab.com/example/repo.git',
            'token': 'test_token'
        }
        
        response = test_client.post(
            f'/tenants/{auth_headers_admin["tenant_id"]}/repositories',
            headers=auth_headers_admin['headers'],
            json=repo_data
        )
        assert response.status_code == 400
        data = json.loads(response.data)
        assert 'Invalid repository type' in data['error']
    
    def test_missing_required_fields(self, test_client, auth_headers_admin):
        """Test that missing required fields are caught."""
        repo_data = {
            'repo_name': 'incomplete-repo'
            # Missing repo_type and remote_url
        }
        
        response = test_client.post(
            f'/tenants/{auth_headers_admin["tenant_id"]}/repositories',
            headers=auth_headers_admin['headers'],
            json=repo_data
        )
        assert response.status_code == 400
        data = json.loads(response.data)
        assert 'Missing required field' in data['error']