"""Simple test for repository management endpoints."""
import pytest
import json
import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from app import create_app, db
from models import Tenant, User, RepositoryConfig
from flask_jwt_extended import create_access_token


def test_list_repositories_empty():
    """Test listing repositories when none exist."""
    # Import test config
    from config import TestConfig
    
    # Create app with test config
    app = create_app(TestConfig)
    
    with app.app_context():
        # Create test database
        db.create_all()
        
        # Create test tenant and user
        tenant = Tenant(name='TestTenant', plan='basic')
        db.session.add(tenant)
        db.session.commit()
        
        user = User(
            tenant_id=tenant.id,
            email='<EMAIL>',
            password_hash='hash',
            role='admin'
        )
        db.session.add(user)
        db.session.commit()
        
        # Create auth token - identity must be a string
        access_token = create_access_token(
            identity=str(user.id),
            additional_claims={
                'tenant_id': str(tenant.id),
                'roles': ['admin']
            }
        )
        
        # Test the endpoint
        client = app.test_client()
        response = client.get(
            f'/tenants/{tenant.id}/repositories',
            headers={'Authorization': f'Bearer {access_token}'}
        )
        
        print(f"Response status: {response.status_code}")
        print(f"Response data: {response.data}")
        
        assert response.status_code == 200
        data = json.loads(response.data)
        assert 'repositories' in data
        assert data['repositories'] == []


if __name__ == "__main__":
    test_list_repositories_empty()