import pytest
import json
import uuid
from .test_utils import get_tenant_id

def test_get_tenant_details(client, admin_auth_headers, app):
    """Test getting tenant details as an admin."""
    with app.app_context():
        tenant_id = get_tenant_id()
        response = client.get(f'/admin/tenants/{tenant_id}', 
                            headers=admin_auth_headers)
        
        assert response.status_code == 200
        data = json.loads(response.data)
        # For the placeholder implementation, we can't verify specific tenant details
        assert isinstance(data, dict)
        assert 'message' in data

def test_get_tenant_details_non_admin(client, auth_headers, app):
    """Test getting tenant details as a non-admin user."""
    with app.app_context():
        tenant_id = get_tenant_id()
        # Regular users shouldn't be able to access admin endpoints
        response = client.get(f'/admin/tenants/{tenant_id}', 
                            headers=auth_headers)
        
        assert response.status_code in [401, 403]  # Either unauthorized or forbidden

def test_create_tenant(client, admin_auth_headers):
    """Test creating a new tenant as an admin."""
    new_tenant_name = f'TestTenant-{uuid.uuid4()}'
    response = client.post('/admin/tenants/', 
                         json={
                             'name': new_tenant_name,
                             'plan': 'premium'
                         },
                         headers=admin_auth_headers)
    
    assert response.status_code == 201
    data = json.loads(response.data)
    assert data['name'] == new_tenant_name
    assert data['plan'] == 'premium'
    assert 'id' in data
    
    # Verify we can resolve the new tenant
    response = client.get(f'/api/public/tenants/resolve?name={new_tenant_name}')
    assert response.status_code == 200

def test_create_tenant_invalid(client, admin_auth_headers):
    """Test creating a tenant with invalid data."""
    # Missing name
    response = client.post('/admin/tenants/', 
                         json={
                             'plan': 'basic'
                         },
                         headers=admin_auth_headers)
    
    assert response.status_code == 400
    
    # Duplicate name (DefaultTenant already exists)
    response = client.post('/admin/tenants/', 
                         json={
                             'name': 'DefaultTenant',
                             'plan': 'basic'
                         },
                         headers=admin_auth_headers)
    
    assert response.status_code in [400, 409]  # Bad request or conflict

def test_tenants_unauthenticated(client, app):
    """Test accessing tenant endpoints without authentication."""
    with app.app_context():
        tenant_id = get_tenant_id()
        
        # Get tenant details
        response = client.get(f'/admin/tenants/{tenant_id}')
        assert response.status_code == 401
        
        # Create tenant
        response = client.post('/admin/tenants/', 
                            json={
                                'name': 'SomeTenant',
                                'plan': 'basic'
                            })
        assert response.status_code == 401