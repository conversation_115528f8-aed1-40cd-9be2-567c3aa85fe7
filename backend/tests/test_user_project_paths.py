"""Test cases for user project paths API."""

import pytest
import json
from uuid import uuid4
from models import UserP<PERSON>ject<PERSON>ath, Tenant, User, db
import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))


class TestUserProjectPaths:
    """Test suite for user project paths endpoints."""
    
    def test_create_project_path(self, test_client, auth_headers):
        """Test creating a new project path mapping."""
        response = test_client.post(
            f'/tenants/{auth_headers["tenant_id"]}/user-project-paths',
            headers=auth_headers['headers'],
            json={
                'project_key': 'PROJ-1',
                'local_path': '/Users/<USER>/projects/project1'
            }
        )
        
        assert response.status_code == 200
        data = response.get_json()
        assert data['project_key'] == 'PROJ-1'
        assert data['local_path'] == '/Users/<USER>/projects/project1'
    
    def test_update_existing_path(self, test_client, auth_headers):
        """Test updating an existing project path mapping."""
        # Create initial path
        response = test_client.post(
            f'/tenants/{auth_headers["tenant_id"]}/user-project-paths',
            headers=auth_headers['headers'],
            json={
                'project_key': 'PROJ-1',
                'local_path': '/Users/<USER>/projects/project1'
            }
        )
        assert response.status_code == 200
        
        # Update the path
        response = test_client.post(
            f'/tenants/{auth_headers["tenant_id"]}/user-project-paths',
            headers=auth_headers['headers'],
            json={
                'project_key': 'PROJ-1',
                'local_path': '/Users/<USER>/projects/project1-updated'
            }
        )
        
        assert response.status_code == 200
        data = response.get_json()
        assert data['project_key'] == 'PROJ-1'
        assert data['local_path'] == '/Users/<USER>/projects/project1-updated'
    
    def test_get_project_path(self, test_client, auth_headers):
        """Test retrieving a project path."""
        # Create a path
        test_client.post(
            f'/tenants/{auth_headers["tenant_id"]}/user-project-paths',
            headers=auth_headers['headers'],
            json={
                'project_key': 'PROJ-1',
                'local_path': '/Users/<USER>/projects/project1'
            }
        )
        
        # Get the path
        response = test_client.get(
            f'/tenants/{auth_headers["tenant_id"]}/user-project-paths/PROJ-1',
            headers=auth_headers['headers']
        )
        
        assert response.status_code == 200
        data = response.get_json()
        assert data['project_key'] == 'PROJ-1'
        assert data['local_path'] == '/Users/<USER>/projects/project1'
    
    def test_get_nonexistent_path(self, test_client, auth_headers):
        """Test retrieving a non-existent project path."""
        response = test_client.get(
            f'/tenants/{auth_headers["tenant_id"]}/user-project-paths/NONEXISTENT',
            headers=auth_headers['headers']
        )
        
        assert response.status_code == 404
    
    def test_list_user_paths(self, test_client, auth_headers):
        """Test listing all user's project paths."""
        # Create multiple paths
        paths = [
            ('PROJ-1', '/Users/<USER>/projects/project1'),
            ('PROJ-2', '/Users/<USER>/projects/project2'),
            ('PROJ-3', '/Users/<USER>/projects/project3')
        ]
        
        for project_key, local_path in paths:
            test_client.post(
                f'/tenants/{auth_headers["tenant_id"]}/user-project-paths',
                headers=auth_headers['headers'],
                json={
                    'project_key': project_key,
                    'local_path': local_path
                }
            )
        
        # List all paths
        response = test_client.get(
            f'/tenants/{auth_headers["tenant_id"]}/user-project-paths',
            headers=auth_headers['headers']
        )
        
        assert response.status_code == 200
        data = response.get_json()
        assert len(data['project_paths']) == 3
        
        # Verify they're sorted by project_key
        project_keys = [p['project_key'] for p in data['project_paths']]
        assert project_keys == ['PROJ-1', 'PROJ-2', 'PROJ-3']
    
    def test_delete_project_path(self, test_client, auth_headers):
        """Test deleting a project path."""
        # Create a path
        test_client.post(
            f'/tenants/{auth_headers["tenant_id"]}/user-project-paths',
            headers=auth_headers['headers'],
            json={
                'project_key': 'PROJ-1',
                'local_path': '/Users/<USER>/projects/project1'
            }
        )
        
        # Delete the path
        response = test_client.delete(
            f'/tenants/{auth_headers["tenant_id"]}/user-project-paths/PROJ-1',
            headers=auth_headers['headers']
        )
        
        assert response.status_code == 200
        
        # Verify it's deleted (soft delete)
        response = test_client.get(
            f'/tenants/{auth_headers["tenant_id"]}/user-project-paths/PROJ-1',
            headers=auth_headers['headers']
        )
        assert response.status_code == 404
    
    def test_delete_nonexistent_path(self, test_client, auth_headers):
        """Test deleting a non-existent project path."""
        response = test_client.delete(
            f'/tenants/{auth_headers["tenant_id"]}/user-project-paths/NONEXISTENT',
            headers=auth_headers['headers']
        )
        
        assert response.status_code == 404
    
    def test_cross_tenant_access_denied(self, test_client, auth_headers):
        """Test that users cannot access other tenant's paths."""
        # Create another tenant
        other_tenant_id = str(uuid4())
        
        # Try to access other tenant's paths
        response = test_client.get(
            f'/tenants/{other_tenant_id}/user-project-paths',
            headers=auth_headers['headers']
        )
        
        assert response.status_code == 403
        assert response.get_json()['error'] == 'Unauthorized tenant access'
    
    def test_missing_required_fields(self, test_client, auth_headers):
        """Test creating path with missing required fields."""
        # Missing project_key
        response = test_client.post(
            f'/tenants/{auth_headers["tenant_id"]}/user-project-paths',
            headers=auth_headers['headers'],
            json={
                'local_path': '/Users/<USER>/projects/project1'
            }
        )
        assert response.status_code == 400
        
        # Missing local_path
        response = test_client.post(
            f'/tenants/{auth_headers["tenant_id"]}/user-project-paths',
            headers=auth_headers['headers'],
            json={
                'project_key': 'PROJ-1'
            }
        )
        assert response.status_code == 400
    
    def test_no_authentication(self, test_client, test_tenant):
        """Test that paths cannot be accessed without authentication."""
        response = test_client.get(
            f'/tenants/{test_tenant.id}/user-project-paths'
        )
        assert response.status_code == 401