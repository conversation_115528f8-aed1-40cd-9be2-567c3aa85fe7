import uuid
from flask import current_app
from models import Tenant

def get_tenant_id():
    """Get the ID of the test tenant from the database."""
    tenant = Tenant.query.filter_by(name='DefaultTenant').first()
    if tenant:
        return str(tenant.id)
    # If no tenant exists, create a hardcoded UUID for testing
    return str(uuid.uuid4())

def get_existing_tenant_id():
    """Get the ID of an existing tenant in the database."""
    tenant = Tenant.query.filter_by(name='DefaultTenant').first()
    if tenant:
        return str(tenant.id)
    # If no tenant exists, create a hardcoded UUID for testing
    return "4e24f01c-ba6b-4527-bb45-3b92da0c1282"