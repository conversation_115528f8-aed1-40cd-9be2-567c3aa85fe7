"""Test file specifically for the UUID handling fix.

This test file contains tests to verify that the fixes for UUID handling work correctly.
The tests validate:
1. Proper conversion from string UUIDs to UUID objects
2. Correct error handling for invalid UUID formats
3. Appropriate HTTP status codes in the login endpoint for both valid and invalid UUIDs
4. Ensuring a 400 Bad Request is returned for invalid UUIDs rather than a 500 Server Error

These tests are critical for ensuring that the application correctly handles UUIDs in API calls
and prevents the 'str' object has no attribute 'hex' error that previously occurred when
SQLAlchemy tried to process string UUIDs that weren't properly converted to UUID objects.
"""
import pytest
import uuid
import json

def test_string_uuid_conversion_to_object():
    """Test that a string UUID is correctly converted to a UUID object."""
    # Generate a UUID string
    test_uuid_str = "4e24f01c-ba6b-4527-bb45-3b92da0c1282"
    
    # Convert to UUID object
    test_uuid_obj = uuid.UUID(test_uuid_str)
    
    # Verify the conversion worked correctly
    assert str(test_uuid_obj) == test_uuid_str
    assert isinstance(test_uuid_obj, uuid.UUID)

def test_invalid_uuid_handling():
    """Test that invalid UUIDs are properly handled."""
    # Try to convert an invalid UUID string
    with pytest.raises(ValueError):
        uuid.UUID("not-a-valid-uuid")
        
def test_uuid_login_handling(client):
    """Test UUID handling in login endpoint with fixed code."""
    # Generate a valid UUID
    valid_uuid = str(uuid.uuid4())
    
    # Attempt login with valid UUID format
    response = client.post(f'/tenants/{valid_uuid}/auth/login', 
                        json={
                            'email': '<EMAIL>',
                            'password': 'password123',
                            'tenant_id': valid_uuid
                        })
    
    # Should be 401 Unauthorized (not 500 error) since tenant doesn't exist
    assert response.status_code == 401
    
    # Try invalid UUID in request body
    response = client.post(f'/tenants/{valid_uuid}/auth/login',
                        json={
                            'email': '<EMAIL>',
                            'password': 'password123',
                            'tenant_id': 'not-a-valid-uuid'
                        })
    
    # Should be 400 Bad Request for invalid UUID format
    assert response.status_code == 400
    data = json.loads(response.data)
    assert 'Invalid' in data['detail']