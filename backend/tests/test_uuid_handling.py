import pytest
import json
import uuid
from flask import current_app

def test_uuid_string_to_object_conversion(client):
    """Test that string UUID is properly converted to UUID object for database operations."""
    # Generate a valid UUID string
    tenant_id_str = str(uuid.uuid4())
    
    # Try to post with a string UUID - should be handled correctly now
    response = client.post(f'/tenants/{tenant_id_str}/auth/login', 
                        json={
                            'email': '<EMAIL>',
                            'password': 'password123',
                            'tenant_id': tenant_id_str
                        })
    
    # We expect a 401 "tenant not found" rather than a 500 error
    assert response.status_code == 401
    data = json.loads(response.data)
    assert data['title'] == 'Unauthorized'
    assert 'Invalid tenant or credentials' in data['detail']

def test_invalid_uuid_format_handling(client):
    """Test that invalid UUID format is properly handled."""
    # Invalid UUID format
    invalid_uuid = "not-a-valid-uuid"
    
    # Try to post with an invalid UUID
    response = client.post(f'/tenants/{invalid_uuid}/auth/login', 
                        json={
                            'email': '<EMAIL>',
                            'password': 'password123',
                            'tenant_id': invalid_uuid
                        })
    
    # For routes with <uuid:tenant_id>, Flask will return 404 for invalid UUIDs
    # This is expected behavior and actually a first line of defense
    assert response.status_code == 404
    data = json.loads(response.data)
    assert data['title'] == 'Not Found'
    
    # Now try with a valid UUID in URL but invalid in request body
    valid_uuid = str(uuid.uuid4())
    response = client.post(f'/tenants/{valid_uuid}/auth/login', 
                        json={
                            'email': '<EMAIL>',
                            'password': 'password123',
                            'tenant_id': 'not-valid-uuid'
                        })
    
    # Our code should catch this and return 400
    assert response.status_code == 400
    data = json.loads(response.data)
    assert data['title'] == 'Bad Request'
    assert 'Invalid' in data['detail']

def test_refresh_token_uuid_handling(client, app):
    """Test UUID handling in refresh token endpoint."""
    # Create a mock token manually to test the UUID conversion
    with app.app_context():
        from flask_jwt_extended import create_refresh_token
        
        # Valid UUID
        user_id = str(uuid.uuid4())
        tenant_id = str(uuid.uuid4())
        
        # Create a refresh token with valid UUIDs as strings
        token = create_refresh_token(
            identity=user_id,
            additional_claims={"tenant_id": tenant_id}
        )
        
        # Test with invalid UUID format in URL
        response = client.post('/tenants/invalid-uuid/auth/refresh',
                            headers={'Authorization': f'Bearer {token}'})
        
        # For routes with <uuid:tenant_id>, Flask will return 404 for invalid UUIDs
        assert response.status_code == 404
        
        # Test with JWT validation error (skipping for now due to complexity with mocking)
        # The main concern is that we properly handle UUIDs, which is tested above
        