# Authentication Debugging Guide

This guide will help you troubleshoot authentication issues with the JIRA credentials API.

## 401 Unauthorized Error

If you're receiving a `401 Unauthorized` error when trying to access the `/tenants/{tid}/jira/credentials` endpoint, follow these steps:

### 1. Check Server Status

First, verify the server is running properly:

```bash
python tools/test_jira_credentials.py status
```

### 2. Test Login

Try to get a fresh JWT token by logging in:

```bash
python tools/test_jira_credentials.py login --tenant 4e24f01c-ba6b-4527-bb45-3b92da0c1282 --email <EMAIL> --password your_password
```

If login succeeds, it will display an access token. Copy this token for the next steps.

### 3. Analyze Your JWT Token

Use the JWT debugging tool to analyze your token:

```bash
python tools/debug_jwt.py "your_jwt_token" "4e24f01c-ba6b-4527-bb45-3b92da0c1282"
```

This will check:
- If the token is valid
- If it has expired
- If the tenant ID in the token matches the requested tenant ID
- If all required claims are present

### 4. Test JIRA Credentials Endpoint

Use the test tool to check if you can access the JIRA credentials endpoint:

```bash
python tools/test_jira_credentials.py get --tenant 4e24f01c-ba6b-4527-bb45-3b92da0c1282 --token "your_jwt_token"
```

### 5. Try Adding Credentials

Test adding JIRA credentials:

```bash
python tools/test_jira_credentials.py add --tenant 4e24f01c-ba6b-4527-bb45-3b92da0c1282 --token "your_jwt_token" --base-url "https://your-jira.atlassian.net" --api-key "your-api-key"
```

## Common Issues and Solutions

### Token Expired

JWT tokens are valid for 15 minutes by default. If your token has expired:
1. Login again to get a fresh token
2. Use the new token for your requests

### Tenant ID Mismatch

The tenant ID in the JWT token must match the tenant ID in the URL:
1. Make sure you're using the same tenant ID in both places
2. Check the token claims with the debug_jwt.py tool

### Missing Authorization Header

Make sure your frontend is sending the token in the correct format:
```
Authorization: Bearer your_jwt_token
```

### CORS Issues

If you're accessing the API from a different domain:
1. Check the browser console for CORS errors
2. The API already allows requests from http://localhost:3000 and http://127.0.0.1:3000

## Checking JWT Configuration

Verify the JWT configuration in the application:

1. Make sure JWT_SECRET_KEY is set in your .env file
2. Check if the JWT is being properly integrated in app.py
3. Verify the @jwt_required() decorator is being applied correctly in the blueprints

## Looking at Logs

Check the application logs for authentication errors:

```bash
tail -f /Users/<USER>/Work/Jiffy/poc/ai-bug-fix/backend/logs/app.log | grep -i auth
```

Or filter for JWT-related logs:

```bash
tail -f /Users/<USER>/Work/Jiffy/poc/ai-bug-fix/backend/logs/app.log | grep -i jwt
```

## Testing with Curl

You can also test the API directly with curl:

```bash
# Login and get token
curl -X POST http://localhost:5045/tenants/4e24f01c-ba6b-4527-bb45-3b92da0c1282/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"your_password","tenant_id":"4e24f01c-ba6b-4527-bb45-3b92da0c1282"}'

# Get JIRA credentials status  
curl -X GET http://localhost:5045/tenants/4e24f01c-ba6b-4527-bb45-3b92da0c1282/jira/credentials \
  -H "Authorization: Bearer your_jwt_token"

# Add JIRA credentials
curl -X POST http://localhost:5045/tenants/4e24f01c-ba6b-4527-bb45-3b92da0c1282/jira/credentials \
  -H "Authorization: Bearer your_jwt_token" \
  -H "Content-Type: application/json" \
  -d '{"base_url":"https://your-jira.atlassian.net","api_key":"your-api-key"}'
```