# Debugging JIRA Credentials - Quick Start Guide

This guide provides a step-by-step approach to diagnose and fix issues with JIRA credential management in the application.

## Common Error Messages

### 1. "non-hexadecimal number found in fromhex() arg at position 0"

This occurs when the APPLICATION_AES_KEY environment variable contains non-hex characters. The key must be exactly 64 hex characters (0-9, a-f, A-F) representing 32 bytes.

### 2. "Error encrypting API key"

This generic error can have several causes, from missing environment variables to invalid key formats or encryption library issues.

## Step-by-Step Debugging

### Step 1: Check Environment Variables

```bash
# Run the environment check tool
python tools/environment_check.py
```

This tool will check all environment variables and identify issues with the AES key.

### Step 2: Verify AES Key Format

```bash
# Run the AES key validation tool
python tools/check_aes_key.py
```

This will verify that your AES key is properly formatted as a hex string of the correct length.

### Step 3: Enable Debug Logging

Edit your `.env` file and add:

```
LOG_LEVEL=DEBUG
SQLALCHEMY_ECHO=True
```

### Step 4: Generate a New Key (if needed)

If your key is invalid, generate a new one:

```bash
python tools/generate_aes_key.py
```

Add the generated key to your `.env` file.

### Step 5: Run the Application with Debug Logging

```bash
flask run --port=5045
```

### Step 6: Reproduce the Issue

Try to add JIRA credentials through the frontend interface or with a curl command:

```bash
curl -X POST http://localhost:5045/tenants/YOUR_TENANT_ID/jira/credentials \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"base_url":"https://your-jira-instance.atlassian.net", "api_key":"your-api-key"}'
```

### Step 7: Check the Logs

```bash
# Check the application log file
cat backend/logs/app.log | grep -i jira
cat backend/logs/app.log | grep -i aes
cat backend/logs/app.log | grep -i error
```

### Step 8: Database Debugging

If the encryption works but database storage fails, check the database structure:

```bash
# Enter SQLite shell (if using SQLite)
sqlite3 instance/jira_browser_dev.db

# Show database schema
.schema jira_credentials

# Check existing credentials
SELECT id, tenant_id, base_url, LENGTH(api_key_encrypted) FROM jira_credentials;
```

## Common Solutions

### 1. Invalid AES Key Format

Solution: Generate a new key with `generate_aes_key.py` and update your `.env` file.

### 2. Missing Environment Variable

Solution: Add `APPLICATION_AES_KEY=your-hex-key` to your `.env` file.

### 3. Database Field Length Limit

If the encrypted data exceeds the database field length:

Solution: Check if the `api_key_encrypted` column in the database has sufficient length (should be at least 512 characters).

### 4. Encryption Library Issues

Solution: Verify that `cryptography` is installed with the correct version:

```bash
pip show cryptography
pip install -U cryptography
```

## Additional Checks

### Base64 Encoding Format Check

The application uses base64 encoding for storing the encryption components. To check a valid format:

```python
import base64

# This format should work
nonce_b64 = "YourBase64EncodedNonce"
ciphertext_b64 = "YourBase64EncodedCiphertext"
stored_format = f"{nonce_b64}:{ciphertext_b64}"

# Valid format looks like: "abc123==:xyz789=="
# It should be a string with a colon separator
assert ':' in stored_format
assert stored_format.count(':') == 1

# Decoding should succeed
nonce_b64, ciphertext_b64 = stored_format.split(':')
nonce = base64.b64decode(nonce_b64)
ciphertext = base64.b64decode(ciphertext_b64)
```

### Testing Encryption Directly

You can test encryption directly using the Python shell:

```bash
python -c "
from utils import encrypt_data, decrypt_data
test_data = 'test-api-key'
nonce, ciphertext = encrypt_data(test_data)
decrypted = decrypt_data(nonce, ciphertext)
print(f'Original: {test_data}')
print(f'Decrypted: {decrypted}')
print(f'Match: {test_data == decrypted}')
"
```