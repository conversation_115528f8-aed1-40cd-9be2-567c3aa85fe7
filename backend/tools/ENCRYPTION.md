# Encryption System Documentation

This document describes the encryption system used in the JIRA Bug Browser application, including how to set it up correctly and troubleshoot common issues.

## Overview

The application uses **AES-256-GCM** (Galois/Counter Mode) for encrypting sensitive data such as JIRA API keys. This is a strong authenticated encryption algorithm that provides:

- Confidentiality: Data cannot be read without the key
- Integrity: Data cannot be modified without detection
- Authentication: Ensures the data comes from the expected source

## Key Management

### Environment Variable

The encryption key is stored in the `APPLICATION_AES_KEY` environment variable. This key must be:

- 32 bytes (256 bits) in length
- Hex-encoded, resulting in a 64-character string
- Containing only valid hexadecimal characters (0-9, a-f, A-F)

### Generating a Key

Use the provided utility script to generate a proper key:

```bash
python tools/generate_aes_key.py
```

This will generate a secure random key and show you how to add it to your `.env` file.

### Key Validation

To check if your current key is valid, use:

```bash
python tools/check_aes_key.py
```

This will validate the key in your environment and provide guidance for fixing any issues.

## Common Issues and Solutions

### "non-hexadecimal number found in fromhex() arg"

This error indicates your key contains characters that are not valid hexadecimal digits.

**Possible causes:**
1. Key contains spaces, tabs, or newlines
2. Key contains special characters or punctuation
3. Key was copied from a rich text format with invisible characters
4. Key has been truncated or corrupted

**Solutions:**
1. Run `check_aes_key.py` to identify the problematic characters
2. Generate a fresh key using `generate_aes_key.py`
3. Ensure no whitespace is added when setting the environment variable

### Key Length Issues

If your key is not exactly 64 characters (representing 32 bytes), you'll get length errors.

**Solutions:**
1. Verify there are no extra or missing characters
2. Ensure the key wasn't truncated when saving to the `.env` file
3. Generate a new key with the utility script

### Development vs. Production

- In development, if the key is missing, a default key is used (automatically logged as a warning)
- In production, always set a proper unique key
- Never use the development default key in production environments

## Implementation Details

The encryption system uses three main functions:

1. `_get_aes_key()`: Retrieves and validates the encryption key from environment variables
2. `encrypt_data()`: Encrypts a string and returns the nonce and ciphertext
3. `decrypt_data()`: Decrypts the data using the nonce and ciphertext

For database storage, the encrypted data is stored as:
```
base64(nonce):base64(ciphertext)
```

This format allows the nonce and ciphertext to be stored together but easily separated when needed for decryption.

## Debugging Encryption Issues

If you're experiencing encryption problems:

1. Set `LOG_LEVEL=DEBUG` in your environment
2. Check the application logs for detailed encryption/decryption information
3. Look for warnings or errors related to the AES key
4. Validate your key with `check_aes_key.py`
5. If needed, generate a new key with `generate_aes_key.py`

## Security Recommendations

1. Use a different key for each environment (dev, staging, production)
2. Rotate keys periodically in production environments
3. Consider using a secret management service instead of environment variables in production
4. Never commit encryption keys to version control
5. Limit access to encryption keys to only those who need it

## Testing Your Encryption Setup

You can run the encryption tests to verify your setup:

```bash
pytest tests/test_encryption.py -v
```

This will verify that the key is loaded correctly and encryption/decryption work as expected.