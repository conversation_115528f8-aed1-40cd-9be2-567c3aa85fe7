#!/usr/bin/env python
"""
Utility script to check and validate an AES key from environment variables.

This script checks for the presence and validity of the APPLICATION_AES_KEY 
environment variable and provides guidance on fixing common issues.

Usage:
    python check_aes_key.py

Output:
    Diagnostic information about the current AES key configuration
"""

import os
import re
import sys

# Add parent directory to path so we can import from utils
parent_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, parent_dir)

# Now we can import from utils
from utils import AES_KEY_ENV_VAR

def check_aes_key():
    """Check the AES key environment variable and report any issues."""
    print("\nAES Key Validation Utility")
    print("=" * 50)
    
    # Check if the environment variable exists
    hex_key = os.environ.get(AES_KEY_ENV_VAR)
    
    if not hex_key:
        print(f"❌ ERROR: {AES_KEY_ENV_VAR} environment variable is not set!")
        print("\nTo fix this issue:")
        print(f"1. Set the {AES_KEY_ENV_VAR} environment variable in your .env file")
        print("2. You can generate a secure key with the generate_aes_key.py utility")
        print("   Example: python tools/generate_aes_key.py")
        return False
    
    print(f"✅ {AES_KEY_ENV_VAR} environment variable is set")
    
    # Check the length
    key_len = len(hex_key)
    print(f"- Key length: {key_len} characters")
    
    if key_len != 64:
        print(f"❌ ERROR: Key length is {key_len}, but should be 64 hex characters (32 bytes)")
        print("\nTo fix this issue:")
        print("1. Generate a proper 32-byte key using generate_aes_key.py")
        print("2. Some common issues that lead to incorrect length:")
        print("   - Extra whitespace or newlines")
        print("   - Missing characters")
        print("   - Key was encoded in a format other than hex")
        return False
    
    # Check if it only contains hex characters
    if not re.match(r'^[0-9a-fA-F]+$', hex_key):
        print("❌ ERROR: Key contains non-hexadecimal characters!")
        
        # Find the first non-hex character and its position
        for i, char in enumerate(hex_key):
            if not re.match(r'[0-9a-fA-F]', char):
                print(f"- First invalid character: '{char}' at position {i}")
                # Show a segment around the error
                segment_start = max(0, i - 5)
                segment_end = min(len(hex_key), i + 6)
                context = hex_key[segment_start:segment_end]
                indicator = " " * (i - segment_start) + "^"
                print(f"- Context: '{context}'")
                print(f"            {indicator}")
                break
        
        print("\nTo fix this issue:")
        print("1. Ensure your key only contains hexadecimal characters (0-9, a-f, A-F)")
        print("2. Common issues include:")
        print("   - Copied text with invisible characters")
        print("   - Spaces or newlines in the key")
        print("   - Using characters outside the hex range (0-9, a-f, A-F)")
        return False
    
    # Try to convert to bytes
    try:
        key_bytes = bytes.fromhex(hex_key)
        byte_length = len(key_bytes)
        
        if byte_length != 32:
            print(f"❌ ERROR: Key converts to {byte_length} bytes, but should be 32 bytes")
            return False
        
        print(f"✅ Key successfully converts to {byte_length} bytes (correct!)")
        print("✅ Key contains only valid hexadecimal characters")
        print("✅ Key validation successful!")
        return True
    
    except ValueError as e:
        print(f"❌ ERROR: Failed to convert key to bytes: {str(e)}")
        print("\nThis is unexpected since the hex check passed. Please report this issue.")
        return False

def main():
    """Main function to run the AES key check."""
    result = check_aes_key()
    
    print("\n" + "=" * 50)
    if result:
        print("🎉 Your AES key configuration looks correct!")
    else:
        print("Please fix the issues with your AES key configuration before continuing.")
    
    # Provide guidance regardless of result
    print("\nReminder: For production environments:")
    print("1. Never use the default development key")
    print("2. Generate a unique key with tools/generate_aes_key.py")
    print("3. Store the key securely (environment variables or secrets manager)")
    print("4. Never commit the key to version control")
    
if __name__ == "__main__":
    main()