#!/usr/bin/env python
"""
One-time debugging script for JIRA credential functionality.

This script performs a complete diagnostic of the JIRA credential system:
1. Validates the AES key configuration
2. Tests encryption and decryption directly
3. Checks for existing JIRA credentials in the database
4. Verifies database schema
5. Attempts a test encryption/storage/retrieval cycle

Usage:
    python tools/debug_jira_setup.py
"""

import os
import sys
import logging
import binascii
import base64
from pathlib import Path
from dotenv import load_dotenv

# Add parent directory to path so we can import from app modules
parent_dir = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(parent_dir))

# Configure basic logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s [%(levelname)s] %(name)s:%(lineno)d - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger('debug_jira')

# Try to load .env file first
dotenv_path = parent_dir / '.env'
if dotenv_path.exists():
    logger.info(f"Loading environment from: {dotenv_path}")
    load_dotenv(dotenv_path)
else:
    logger.warning("No .env file found. Using environment variables as is.")

def print_section(title):
    """Print a section header for better readability."""
    print("\n" + "=" * 80)
    print(f" {title} ".center(80, '='))
    print("=" * 80 + "\n")

def check_aes_key():
    """Check if AES key is properly configured."""
    from utils import AES_KEY_ENV_VAR
    
    print_section("AES KEY VALIDATION")
    
    key = os.environ.get(AES_KEY_ENV_VAR)
    if not key:
        print(f"❌ ERROR: {AES_KEY_ENV_VAR} environment variable is not set!")
        return False
    
    print(f"✅ {AES_KEY_ENV_VAR} environment variable is set")
    print(f"- Key length: {len(key)} characters")
    
    # Check for non-hex characters
    for i, char in enumerate(key):
        if char not in '0123456789abcdefABCDEF':
            segment_start = max(0, i - 5)
            segment_end = min(len(key), i + 6)
            context = key[segment_start:segment_end]
            print(f"❌ ERROR: Non-hex character '{char}' at position {i}")
            print(f"- Context: '{context}'")
            return False
    
    print("✅ Key contains only valid hexadecimal characters")
    
    # Try to convert to bytes
    try:
        key_bytes = bytes.fromhex(key)
        byte_length = len(key_bytes)
        
        if byte_length != 32:
            print(f"❌ ERROR: Key converts to {byte_length} bytes, but should be 32 bytes")
            return False
        
        print(f"✅ Key successfully converts to {byte_length} bytes (correct!)")
        return True
    except ValueError as e:
        print(f"❌ ERROR: Failed to convert key to bytes: {str(e)}")
        return False

def test_encryption():
    """Test encryption and decryption directly."""
    print_section("ENCRYPTION TEST")
    
    try:
        from utils import encrypt_data, decrypt_data
        
        # Test with a sample API key
        test_data = "JIRA-API-key-12345"
        print(f"Test data: {test_data}")
        
        # Encrypt the data
        print("\nEncrypting data...")
        nonce, ciphertext = encrypt_data(test_data)
        
        print(f"- Nonce length: {len(nonce)} bytes")
        print(f"- Ciphertext length: {len(ciphertext)} bytes")
        
        # Convert to base64 for storage
        nonce_b64 = base64.b64encode(nonce).decode('utf-8')
        ciphertext_b64 = base64.b64encode(ciphertext).decode('utf-8')
        
        print(f"- Nonce (base64): {nonce_b64}")
        print(f"- Ciphertext (base64, truncated): {ciphertext_b64[:20]}...")
        
        # Combined format for storage
        encrypted_data = f"{nonce_b64}:{ciphertext_b64}"
        print(f"- Combined format: {encrypted_data[:30]}...")
        
        # Decrypt the data
        print("\nDecrypting data...")
        decrypted = decrypt_data(nonce, ciphertext)
        
        print(f"- Decrypted result: {decrypted}")
        print(f"- Matches original: {'✅ Yes' if decrypted == test_data else '❌ No'}")
        
        return decrypted == test_data
    except Exception as e:
        print(f"❌ ERROR: Encryption test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def check_database():
    """Check database schema and existing credentials."""
    print_section("DATABASE CHECK")
    
    try:
        # Import Flask app and database
        from app import create_app, db
        from models import JiraCredential
        from flask import current_app
        
        # Create a test app context
        app = create_app()
        with app.app_context():
            # Check if JiraCredential table exists
            inspector = db.inspect(db.engine)
            if 'jira_credentials' not in inspector.get_table_names():
                print("❌ ERROR: jira_credentials table not found in database")
                return False
            
            print("✅ jira_credentials table exists in database")
            
            # Check table columns
            columns = {c['name']: c for c in inspector.get_columns('jira_credentials')}
            required_columns = ['id', 'tenant_id', 'user_id', 'base_url', 'api_key_encrypted', 'updated_at']
            
            missing = [c for c in required_columns if c not in columns]
            if missing:
                print(f"❌ ERROR: Missing columns in jira_credentials: {', '.join(missing)}")
                return False
            
            print("✅ All required columns exist in jira_credentials table")
            
            # Check api_key_encrypted column length
            api_key_column = columns.get('api_key_encrypted', {})
            column_type = str(api_key_column.get('type', ''))
            print(f"- api_key_encrypted column type: {column_type}")
            
            # Check for existing credentials
            credentials = JiraCredential.query.all()
            if credentials:
                print(f"- Found {len(credentials)} existing JIRA credential(s)")
                for cred in credentials:
                    print(f"  - ID: {cred.id}, Tenant: {cred.tenant_id}, Updated: {cred.updated_at}")
                    
                    # Check encrypted data format
                    encrypted_data = cred.api_key_encrypted
                    if ':' in encrypted_data:
                        parts = encrypted_data.split(':')
                        if len(parts) == 2:
                            try:
                                nonce_b64, ciphertext_b64 = parts
                                nonce = base64.b64decode(nonce_b64)
                                ciphertext = base64.b64decode(ciphertext_b64)
                                print(f"    - Valid encryption format with nonce ({len(nonce)} bytes) and ciphertext ({len(ciphertext)} bytes)")
                            except Exception as e:
                                print(f"    - ❌ ERROR: Invalid base64 encoding: {str(e)}")
                        else:
                            print(f"    - ❌ ERROR: Invalid format (found {len(parts)} parts, expected 2)")
                    else:
                        print("    - ❌ ERROR: Invalid format (missing separator)")
            else:
                print("- No existing JIRA credentials found")
            
            return True
    except Exception as e:
        print(f"❌ ERROR: Database check failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def simulate_credential_storage():
    """Simulate storing and retrieving JIRA credentials."""
    print_section("CREDENTIAL STORAGE SIMULATION")
    
    try:
        # This is a simulated test only - doesn't actually add to database
        from utils import encrypt_data, decrypt_data
        
        # Test credential data
        base_url = "https://example.atlassian.net"
        api_key = "test-api-key-12345"
        
        print(f"Test data:")
        print(f"- Base URL: {base_url}")
        print(f"- API Key: {api_key}")
        
        # Encrypt API key
        print("\nEncrypting API key...")
        nonce, ciphertext = encrypt_data(api_key)
        
        # Convert binary to base64 for storage
        nonce_b64 = base64.b64encode(nonce).decode('utf-8')
        ciphertext_b64 = base64.b64encode(ciphertext).decode('utf-8')
        
        # Combine for storage (can be separated later)
        encrypted_data = f"{nonce_b64}:{ciphertext_b64}"
        
        print(f"- Encrypted data format: {encrypted_data[:30]}...") 
        print(f"- Total encrypted data length: {len(encrypted_data)} characters")
        
        # Simulate retrieval and decryption
        print("\nSimulating credential retrieval...")
        try:
            # Split combined data
            retrieved_nonce_b64, retrieved_ciphertext_b64 = encrypted_data.split(':')
            
            # Decode from base64
            retrieved_nonce = base64.b64decode(retrieved_nonce_b64)
            retrieved_ciphertext = base64.b64decode(retrieved_ciphertext_b64)
            
            # Decrypt
            decrypted_api_key = decrypt_data(retrieved_nonce, retrieved_ciphertext)
            
            print(f"- Retrieved Base URL: {base_url}")
            print(f"- Decrypted API Key: {decrypted_api_key}")
            print(f"- API Key matches original: {'✅ Yes' if decrypted_api_key == api_key else '❌ No'}")
            
            return decrypted_api_key == api_key
        except Exception as e:
            print(f"❌ ERROR: Credential retrieval simulation failed: {str(e)}")
            return False
    except Exception as e:
        print(f"❌ ERROR: Credential storage simulation failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def check_flask_jira_blueprint():
    """Check if the JIRA blueprint is properly registered."""
    print_section("JIRA BLUEPRINT CHECK")
    
    try:
        from app import create_app
        
        app = create_app()
        print("Registered blueprints:")
        
        found_jira_bp = False
        for rule in app.url_map.iter_rules():
            if 'jira' in rule.endpoint:
                print(f"- {rule.endpoint}: {rule}")
                found_jira_bp = True
        
        if not found_jira_bp:
            print("❌ ERROR: No JIRA blueprint routes found")
            return False
        
        print("✅ JIRA blueprint is properly registered")
        return True
    except Exception as e:
        print(f"❌ ERROR: Blueprint check failed: {str(e)}")
        return False

def main():
    """Main function to run all diagnostics."""
    print("\n===== JIRA CREDENTIAL SYSTEM DIAGNOSTIC =====")
    print(f"Time: {import_time}")
    print(f"Working directory: {os.getcwd()}")
    
    # Run all diagnostics
    results = {
        "AES Key": check_aes_key(),
        "Encryption": test_encryption(),
        "Database": check_database(),
        "Credential Storage": simulate_credential_storage(),
        "JIRA Blueprint": check_flask_jira_blueprint()
    }
    
    # Print summary
    print_section("DIAGNOSTIC SUMMARY")
    
    all_passed = True
    for test, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test}: {status}")
        if not result:
            all_passed = False
    
    print("\n")
    if all_passed:
        print("🎉 All diagnostic tests passed! The JIRA credential system appears to be working correctly.")
        print("If you're still experiencing issues, check the application logs during actual usage.")
    else:
        print("❌ Some diagnostic tests failed. Please address the issues identified above.")
        print("You can run this script again after making changes to verify the fixes.")
    
    print("\nFor more detailed debugging during application use:")
    print("1. Set LOG_LEVEL=DEBUG in your .env file")
    print("2. Run the application and monitor logs/app.log for detailed logs")
    print("3. Check the specific error messages in your browser or API response")

if __name__ == "__main__":
    # Record import time
    from datetime import datetime
    import_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    # Run the main diagnostic function
    main()