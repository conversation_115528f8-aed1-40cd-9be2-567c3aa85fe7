#!/usr/bin/env python
"""
JWT Debugging Utility for JIRA Bug Browser

This script helps diagnose JWT authentication issues by:
1. Decoding and validating JWT tokens
2. Checking token expiration
3. Verifying tenant ID in the token matches the requested tenant
4. Inspecting token claims and headers

Usage:
    python tools/debug_jwt.py <jwt_token> <tenant_id>

Example:
    python tools/debug_jwt.py "eyJ0eXAiOiJKV..." "4e24f01c-ba6b-4527-bb45-3b92da0c1282"
"""

import sys
import os
import json
import datetime
import uuid
from pathlib import Path
from dotenv import load_dotenv

# Try to load .env file
parent_dir = Path(__file__).resolve().parent.parent
dotenv_path = parent_dir / '.env'
if dotenv_path.exists():
    load_dotenv(dotenv_path)

def decode_jwt(token):
    """Decode a JWT token without verification to inspect its contents."""
    import base64
    import json
    
    # JWT tokens have 3 parts: header.payload.signature
    try:
        parts = token.split('.')
        if len(parts) != 3:
            return None, "Invalid token format: Not a JWT token (expecting 3 parts separated by dots)"
        
        # Decode header and payload
        # Add padding if needed
        def fix_padding(data):
            needed = len(data) % 4
            if needed:
                data += '=' * (4 - needed)
            return data
        
        header_json = base64.b64decode(fix_padding(parts[0])).decode('utf-8')
        payload_json = base64.b64decode(fix_padding(parts[1])).decode('utf-8')
        
        header = json.loads(header_json)
        payload = json.loads(payload_json)
        
        return {
            'header': header,
            'payload': payload,
            'raw_signature': parts[2]
        }, None
    except Exception as e:
        return None, f"Error decoding token: {str(e)}"

def check_token(token_data, tenant_id=None):
    """Analyze the token data and check for common issues."""
    if not token_data:
        return False, "Could not decode token"
    
    issues = []
    
    # Check if it's an access token
    token_type = token_data['header'].get('typ', '')
    if token_type != 'JWT':
        issues.append(f"Warning: Unusual token type: {token_type}")
    
    # Check algorithm
    alg = token_data['header'].get('alg', '')
    if alg != 'HS256':
        issues.append(f"Warning: Unusual algorithm: {alg}")
    
    # Check expiration
    payload = token_data['payload']
    exp = payload.get('exp')
    if exp:
        exp_time = datetime.datetime.fromtimestamp(exp)
        now = datetime.datetime.now()
        if exp_time < now:
            issues.append(f"Error: Token expired on {exp_time.isoformat()}")
        else:
            time_left = exp_time - now
            issues.append(f"Token expires in {time_left.total_seconds():.1f} seconds")
    else:
        issues.append("Warning: Token has no expiration")
    
    # Check required claims
    required_claims = ['sub', 'iat', 'exp', 'tenant_id', 'roles']
    for claim in required_claims:
        if claim not in payload:
            issues.append(f"Error: Missing required claim: {claim}")
    
    # Check tenant_id if provided
    if tenant_id and 'tenant_id' in payload:
        if payload['tenant_id'] != tenant_id:
            issues.append(f"Error: Token tenant_id ({payload['tenant_id']}) doesn't match request tenant_id ({tenant_id})")
        else:
            issues.append(f"Tenant ID in token matches requested tenant ID: {tenant_id}")
    
    return len([i for i in issues if i.startswith("Error")]) == 0, issues

def verify_token(token):
    """Verify a token's signature against the application's JWT secret."""
    from flask_jwt_extended import decode_token
    import jwt
    
    jwt_secret = os.environ.get('JWT_SECRET_KEY')
    if not jwt_secret:
        return False, "JWT_SECRET_KEY environment variable not set"
    
    try:
        # Try to decode the token with the secret key
        jwt.decode(token, jwt_secret, algorithms=['HS256'])
        return True, "Token signature valid"
    except jwt.ExpiredSignatureError:
        return False, "Token signature valid but token has expired"
    except jwt.InvalidTokenError as e:
        return False, f"Invalid token: {str(e)}"
    except Exception as e:
        return False, f"Error verifying token: {str(e)}"

def main():
    if len(sys.argv) < 2:
        print("Usage: python debug_jwt.py <jwt_token> [tenant_id]")
        sys.exit(1)
    
    token = sys.argv[1]
    tenant_id = sys.argv[2] if len(sys.argv) > 2 else None
    
    print("\n===== JWT TOKEN ANALYSIS =====\n")
    
    # Decode the token
    token_data, error = decode_jwt(token)
    if error:
        print(f"Error: {error}")
        sys.exit(1)
    
    # Print token info
    print("Token Header:")
    print(json.dumps(token_data['header'], indent=2))
    print("\nToken Payload:")
    print(json.dumps(token_data['payload'], indent=2))
    
    # Check the token
    valid, issues = check_token(token_data, tenant_id)
    
    print("\nToken Analysis:")
    for issue in issues:
        print(f"- {issue}")
    
    # Verify signature
    signature_valid, signature_msg = verify_token(token)
    print(f"\nSignature Verification: {signature_msg}")
    
    print("\nSummary:")
    if valid and signature_valid:
        print("✅ Token appears to be valid and matches the tenant ID")
    else:
        print("❌ Token has issues that may prevent successful authentication")
    
    # Print tips for common issues
    print("\nCommon Solutions for 401 Unauthorized Errors:")
    print("1. Token expired - Generate a new token by logging in again")
    print("2. Invalid signature - Make sure JWT_SECRET_KEY is consistent across environments")
    print("3. Tenant ID mismatch - Make sure the token's tenant_id matches the URL path tenant_id")
    print("4. Missing claims - Ensure token was generated with all required claims")
    print("5. Token not included in request - Check that the Authorization header is set correctly")
    print("   Example: Authorization: Bearer <token>")

if __name__ == "__main__":
    main()