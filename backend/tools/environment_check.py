#!/usr/bin/env python
"""
Utility script to check and validate environment variables for the application.

This script examines the current environment configuration, checking for:
- Required variables
- Variable format and content validation
- Common configuration issues

Usage:
    python environment_check.py

Output:
    Diagnostic information about the current environment configuration
"""

import os
import re
import sys
from pathlib import Path
import logging
import binascii
from dotenv import load_dotenv, find_dotenv

# Add parent directory to path so we can import from app modules
parent_dir = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(parent_dir))

# Configure basic logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger('env_check')

# Define the variables to check
REQUIRED_VARS = [
    'APPLICATION_AES_KEY',
    'FLASK_APP'
]

OPTIONAL_VARS = [
    'FLASK_ENV', 
    'LOG_LEVEL',
    'SQLALCHEMY_ECHO',
    'DATABASE_URL',
    'JWT_SECRET_KEY',
    'TESTING'
]

def check_env_file():
    """Check if .env file exists and can be loaded."""
    dotenv_path = find_dotenv()
    if dotenv_path:
        logger.info(f"Found .env file at: {dotenv_path}")
        # Try to load it
        load_dotenv(dotenv_path)
        return True
    else:
        logger.warning("No .env file found. Checking for environment variables directly.")
        # Check if .env.example exists and suggest copying it
        example_path = parent_dir / '.env.example'
        if example_path.exists():
            logger.info(f"Found .env.example file. You may want to copy it: cp {example_path} {parent_dir / '.env'}")
        return False

def check_aes_key():
    """Check if AES key is properly formatted."""
    key = os.environ.get('APPLICATION_AES_KEY')
    if not key:
        logger.error("APPLICATION_AES_KEY is not set!")
        return False
    
    if len(key) != 64:
        logger.error(f"APPLICATION_AES_KEY has incorrect length: {len(key)} chars (expected 64)")
        return False
    
    if not re.match(r'^[0-9a-fA-F]+$', key):
        logger.error("APPLICATION_AES_KEY contains non-hexadecimal characters")
        for i, char in enumerate(key):
            if not re.match(r'[0-9a-fA-F]', char):
                context_start = max(0, i - 5)
                context_end = min(len(key), i + 6)
                context = key[context_start:context_end]
                logger.error(f"First invalid char at position {i}: '{char}' in context '...{context}...'")
                break
        return False
    
    try:
        # Try to convert to bytes
        key_bytes = bytes.fromhex(key)
        if len(key_bytes) != 32:
            logger.error(f"APPLICATION_AES_KEY converts to {len(key_bytes)} bytes (expected 32)")
            return False
    except ValueError as e:
        logger.error(f"Failed to convert APPLICATION_AES_KEY to bytes: {e}")
        return False
    
    logger.info("APPLICATION_AES_KEY is valid (correct length and format)")
    
    # Check if it's the default development key
    default_key = "00112233445566778899aabbccddeeff00112233445566778899aabbccddeeff"
    if key.lower() == default_key:
        logger.warning("Using the default development key. This should not be used in production!")
    
    return True

def check_flask_env():
    """Check Flask environment configuration."""
    env = os.environ.get('FLASK_ENV')
    if not env:
        logger.warning("FLASK_ENV is not set. Flask will use 'production' by default.")
    else:
        if env == 'development':
            logger.info("Running in development mode (FLASK_ENV=development)")
        elif env == 'production':
            logger.info("Running in production mode (FLASK_ENV=production)")
            
            # In production, we should have a secure AES key
            key = os.environ.get('APPLICATION_AES_KEY')
            default_key = "00112233445566778899aabbccddeeff00112233445566778899aabbccddeeff"
            if key and key.lower() == default_key:
                logger.error("SECURITY RISK: Using default development encryption key in production!")
        else:
            logger.warning(f"Unusual FLASK_ENV value: {env}")

def check_database_config():
    """Check database configuration."""
    db_url = os.environ.get('DATABASE_URL')
    if not db_url:
        logger.info("DATABASE_URL not set. Will use SQLite default.")
    else:
        # Basic validation of database URL format
        if db_url.startswith(('sqlite://', 'postgresql://', 'mysql://')):
            logger.info(f"Database type: {db_url.split('://')[0]}")
        else:
            logger.warning(f"Unusual database URL format: {db_url}")

def check_log_level():
    """Check logging configuration."""
    log_level = os.environ.get('LOG_LEVEL', 'INFO')
    valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
    
    if log_level.upper() not in valid_levels:
        logger.warning(f"Unusual LOG_LEVEL value: {log_level}. Valid values are {', '.join(valid_levels)}")
    else:
        logger.info(f"Log level set to: {log_level}")
    
    sql_echo = os.environ.get('SQLALCHEMY_ECHO', 'False')
    if sql_echo.lower() == 'true':
        logger.info("SQL query logging enabled (SQLALCHEMY_ECHO=True)")

def check_jwt_secret():
    """Check JWT secret key configuration."""
    jwt_key = os.environ.get('JWT_SECRET_KEY')
    if not jwt_key:
        logger.warning("JWT_SECRET_KEY not set. A random key will be generated on startup.")
    elif len(jwt_key) < 32:
        logger.warning(f"JWT_SECRET_KEY is relatively short ({len(jwt_key)} chars). Consider using a longer key for security.")

def check_required_vars():
    """Check that all required variables are set."""
    missing = []
    for var in REQUIRED_VARS:
        if not os.environ.get(var):
            missing.append(var)
    
    if missing:
        logger.error(f"Missing required environment variables: {', '.join(missing)}")
        return False
    return True

def generate_sample_aes_key():
    """Generate a sample AES key for demonstration."""
    random_bytes = os.urandom(32)
    hex_key = binascii.hexlify(random_bytes).decode()
    return hex_key

def main():
    """Main function to run the environment check."""
    print("\n===== Environment Configuration Check =====\n")
    
    # Try to load .env file first
    check_env_file()
    
    # Check all variables
    all_vars_ok = check_required_vars()
    
    # Specific checks
    aes_key_ok = check_aes_key()
    check_flask_env()
    check_database_config()
    check_log_level()
    check_jwt_secret()
    
    # Print summary
    print("\n===== Environment Check Summary =====")
    
    if all_vars_ok and aes_key_ok:
        print("✅ All required environment variables are set correctly!")
    else:
        print("❌ There are issues with your environment configuration")
        
        if not aes_key_ok:
            print("\n----- AES Key Fix -----")
            print("1. Generate a new AES key using: python tools/generate_aes_key.py")
            print("2. Set it in your .env file: APPLICATION_AES_KEY=<generated_key>")
            print("3. Make sure there are no spaces or newlines in the key")
            
            # Generate a sample key for demonstration
            sample_key = generate_sample_aes_key()
            print(f"\nExample of a valid key format: {sample_key[:10]}...{sample_key[-10:]}")
    
    print("\n----- Current Environment Variables -----")
    # Print all relevant environment variables (with censoring for sensitive values)
    for var in REQUIRED_VARS + OPTIONAL_VARS:
        value = os.environ.get(var)
        if value:
            # Censor sensitive values
            if var in ['APPLICATION_AES_KEY', 'JWT_SECRET_KEY']:
                if len(value) > 10:
                    display_value = f"{value[:5]}...{value[-5:]}"
                else:
                    display_value = "***"
            else:
                display_value = value
            print(f"{var}={display_value}")
        else:
            print(f"{var}=<not set>")

if __name__ == "__main__":
    main()