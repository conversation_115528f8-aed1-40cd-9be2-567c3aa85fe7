#!/usr/bin/env python
"""
Frontend Authentication Check Utility

This script analyzes how authentication is being handled in the frontend code.
It checks for common issues with JWT token handling in the frontend that might 
cause 401 Unauthorized errors when calling the backend.

Usage:
    python tools/frontend_auth_check.py [frontend_dir]

Arguments:
    frontend_dir - Path to the frontend directory (default: ../frontend)
"""

import os
import sys
import re
import json
from pathlib import Path

# Default frontend path
DEFAULT_FRONTEND_PATH = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'frontend')

def check_api_file(frontend_dir):
    """Check the API service file for proper authentication handling."""
    print("\n===== CHECKING API SERVICE FILE =====")
    
    # Common API file locations
    api_file_paths = [
        os.path.join(frontend_dir, 'src', 'services', 'api.js'),
        os.path.join(frontend_dir, 'src', 'api', 'index.js'),
        os.path.join(frontend_dir, 'src', 'utils', 'api.js'),
        os.path.join(frontend_dir, 'src', 'lib', 'api.js'),
    ]
    
    api_file = None
    for path in api_file_paths:
        if os.path.exists(path):
            api_file = path
            break
    
    if not api_file:
        print("❌ Could not find API service file. Checked these locations:")
        for path in api_file_paths:
            print(f"  - {path}")
        return False
    
    print(f"✅ Found API service file: {api_file}")
    
    # Read the file
    with open(api_file, 'r') as f:
        content = f.read()
    
    # Check for token handling
    auth_header_pattern = re.compile(r'["\']Authorization["\']\s*:\s*["\']Bearer\s+')
    if auth_header_pattern.search(content):
        print("✅ Found Bearer token authentication header format")
    else:
        print("❌ Could not find 'Authorization: Bearer' header in API file")
        print("  Make sure you're setting the header correctly:")
        print("  headers: { 'Authorization': `Bearer ${token}` }")
    
    # Check for token retrieval from storage
    storage_patterns = [
        re.compile(r'localStorage\.getItem\(["\']token["\']\)'),
        re.compile(r'localStorage\.getItem\(["\']accessToken["\']\)'),
        re.compile(r'localStorage\.getItem\(["\']jwt["\']\)'),
        re.compile(r'sessionStorage\.getItem\(["\']token["\']\)'),
    ]
    
    token_from_storage = False
    for pattern in storage_patterns:
        if pattern.search(content):
            token_from_storage = True
            print("✅ Found token retrieval from storage")
            break
    
    if not token_from_storage:
        print("❌ Could not find token retrieval from localStorage or sessionStorage")
        print("  Make sure you're getting the token from storage:")
        print("  const token = localStorage.getItem('token')")
    
    # Check for error handling of 401 responses
    unauthorized_handling = re.compile(r'401|unauthorized|unauthenticated|expired', re.IGNORECASE)
    if unauthorized_handling.search(content):
        print("✅ Found handling for 401 Unauthorized responses")
    else:
        print("⚠️ Warning: Could not find explicit handling for 401 Unauthorized responses")
        print("  Consider adding code to handle token expiration and redirect to login")
    
    return True

def check_jira_settings_page(frontend_dir):
    """Check the JIRA settings page for proper API calls."""
    print("\n===== CHECKING JIRA SETTINGS PAGE =====")
    
    # Common locations for JIRA settings page
    page_paths = [
        os.path.join(frontend_dir, 'src', 'views', 'JiraSettingsPage.js'),
        os.path.join(frontend_dir, 'src', 'pages', 'JiraSettings.js'),
        os.path.join(frontend_dir, 'src', 'components', 'JiraSettings.js'),
        os.path.join(frontend_dir, 'src', 'views', 'JiraSettings.js'),
    ]
    
    jira_page = None
    for path in page_paths:
        if os.path.exists(path):
            jira_page = path
            break
    
    if not jira_page:
        print("⚠️ Could not find JIRA settings page. Checked these locations:")
        for path in page_paths:
            print(f"  - {path}")
        return False
    
    print(f"✅ Found JIRA settings page: {jira_page}")
    
    # Read the file
    with open(jira_page, 'r') as f:
        content = f.read()
    
    # Check for API call patterns
    api_call_patterns = [
        re.compile(r'(?:axios|fetch|api).*(?:/jira/credentials|\'/jira\'|"/jira")'),
        re.compile(r'(?:post|put|delete).*(?:/jira/credentials|\'/jira\'|"/jira")'),
    ]
    
    api_calls_found = False
    for pattern in api_call_patterns:
        if pattern.search(content):
            api_calls_found = True
            print("✅ Found JIRA credentials API calls")
            break
    
    if not api_calls_found:
        print("❌ Could not find JIRA credentials API calls in the settings page")
        print("  Make sure you're calling the correct endpoint:")
        print("  /tenants/${tenantId}/jira/credentials")
    
    # Check for tenant ID inclusion
    tenant_id_pattern = re.compile(r'tenant.?[iI]d')
    if tenant_id_pattern.search(content):
        print("✅ Found tenant ID references in the settings page")
    else:
        print("⚠️ Warning: Could not find tenant ID references in the settings page")
        print("  Make sure the tenant ID is included in the API calls")
    
    return True

def check_login_flow(frontend_dir):
    """Check the login flow for token storage."""
    print("\n===== CHECKING LOGIN FLOW =====")
    
    # Common locations for login page
    login_paths = [
        os.path.join(frontend_dir, 'src', 'views', 'LoginPage.js'),
        os.path.join(frontend_dir, 'src', 'pages', 'Login.js'),
        os.path.join(frontend_dir, 'src', 'components', 'Login.js'),
        os.path.join(frontend_dir, 'src', 'views', 'Login.js'),
    ]
    
    login_page = None
    for path in login_paths:
        if os.path.exists(path):
            login_page = path
            break
    
    if not login_page:
        print("⚠️ Could not find login page. Checked these locations:")
        for path in login_paths:
            print(f"  - {path}")
        return False
    
    print(f"✅ Found login page: {login_page}")
    
    # Read the file
    with open(login_page, 'r') as f:
        content = f.read()
    
    # Check for token storage
    token_storage_patterns = [
        re.compile(r'localStorage\.setItem\(["\'](?:token|accessToken|jwt)["\']\s*,'),
        re.compile(r'sessionStorage\.setItem\(["\'](?:token|accessToken|jwt)["\']\s*,'),
    ]
    
    token_storage_found = False
    for pattern in token_storage_patterns:
        if pattern.search(content):
            token_storage_found = True
            print("✅ Found token storage in login flow")
            break
    
    if not token_storage_found:
        print("❌ Could not find token storage in login flow")
        print("  Make sure you're storing the token after login:")
        print("  localStorage.setItem('token', response.data.access_token)")
    
    # Check for tenant ID handling
    tenant_id_pattern = re.compile(r'tenant.?[iI]d')
    if tenant_id_pattern.search(content):
        print("✅ Found tenant ID handling in login flow")
    else:
        print("⚠️ Warning: Could not find tenant ID handling in login flow")
        print("  Make sure you're handling the tenant ID correctly during login")
    
    return True

def main():
    # Get frontend directory from command line or use default
    frontend_dir = sys.argv[1] if len(sys.argv) > 1 else DEFAULT_FRONTEND_PATH
    
    if not os.path.exists(frontend_dir):
        print(f"Error: Frontend directory not found: {frontend_dir}")
        print(f"Please provide the correct path to the frontend directory")
        sys.exit(1)
    
    print(f"Analyzing frontend code in: {frontend_dir}")
    
    # Run checks
    api_checked = check_api_file(frontend_dir)
    jira_page_checked = check_jira_settings_page(frontend_dir)
    login_checked = check_login_flow(frontend_dir)
    
    # Print summary
    print("\n===== SUMMARY =====")
    checks_passed = sum([api_checked, jira_page_checked, login_checked])
    total_checks = 3
    print(f"Checks passed: {checks_passed}/{total_checks}")
    
    if checks_passed == total_checks:
        print("✅ Frontend authentication flow looks good")
        print("If you're still experiencing 401 errors, check the server-side authentication with:")
        print("- python tools/debug_jwt.py <token> <tenant_id>")
        print("- python tools/test_jira_credentials.py login --tenant <id> --email <email> --password <pwd>")
    else:
        print("⚠️ Some issues were found in the frontend authentication flow")
        print("Fix the issues above and try again")
    
    print("\nAdditional debugging tips:")
    print("1. Check browser console for any CORS or network errors")
    print("2. Use browser dev tools to verify the Authorization header is being sent")
    print("3. Test with the backend debugging tools to isolate frontend vs backend issues")
    print("4. See tools/AUTH_DEBUG.md for more troubleshooting steps")

if __name__ == "__main__":
    main()