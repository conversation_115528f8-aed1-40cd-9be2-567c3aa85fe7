#!/usr/bin/env python
"""
Utility script to generate a secure random AES-256 key.

This script generates a secure random 32-byte (256-bit) key suitable for 
AES-256-GCM encryption and prints it as a hex string that can be used 
for the APPLICATION_AES_KEY environment variable.

Usage:
    python generate_aes_key.py

Output:
    A 64-character hex string representing a random 32-byte key
"""

import os
import binascii

def generate_aes_key():
    """Generate a secure random 32-byte key and return it as a hex string."""
    # Generate 32 random bytes (256 bits)
    random_bytes = os.urandom(32)
    
    # Convert to hex string
    hex_key = binascii.hexlify(random_bytes).decode()
    
    return hex_key

if __name__ == "__main__":
    # Generate and print the key
    key = generate_aes_key()
    
    print("\nGenerated AES-256 Key (hex):")
    print("-" * 70)
    print(key)
    print("-" * 70)
    
    print("\nTo use this key, add it to your .env file:")
    print("APPLICATION_AES_KEY=" + key)
    
    print("\nIMPORTANT: Store this key securely and never commit it to version control!")
    print("In production, consider using a secure key management system.\n")