#!/usr/bin/env python
"""
Test JIRA Credentials Endpoint Utility

This script tests the JIRA credentials endpoints by making direct calls,
bypassing the frontend. It can help diagnose authentication and API issues.

Usage:
    python tools/test_jira_credentials.py <command> [options]

Commands:
    login       - Get a JWT token by logging in
    get         - Get current JIRA credentials status
    add         - Add or update JIRA credentials
    delete      - Delete JIRA credentials
    status      - Check the server status

Options:
    --tenant <id>       - The tenant ID to use
    --email <email>     - Email for login
    --password <pwd>    - Password for login
    --token <token>     - JWT token (from login command)
    --base-url <url>    - JIRA base URL for add command
    --api-key <key>     - JIRA API key for add command
    --url <url>         - Custom API URL (default: http://localhost:5045)
"""

import argparse
import requests
import json
import os
import sys
import uuid
from pathlib import Path
from urllib.parse import urljoin
from dotenv import load_dotenv

# Try to load .env file
parent_dir = Path(__file__).resolve().parent.parent
dotenv_path = parent_dir / '.env'
if dotenv_path.exists():
    load_dotenv(dotenv_path)

def parse_args():
    parser = argparse.ArgumentParser(description='Test JIRA credentials API')
    parser.add_argument('command', choices=['login', 'get', 'add', 'delete', 'status'],
                       help='Command to execute')
    parser.add_argument('--tenant', help='Tenant ID')
    parser.add_argument('--email', help='Email for login')
    parser.add_argument('--password', help='Password for login')
    parser.add_argument('--token', help='JWT token (from login command)')
    parser.add_argument('--base-url', help='JIRA base URL for add command')
    parser.add_argument('--api-key', help='JIRA API key for add command')
    parser.add_argument('--url', default='http://localhost:5045',
                       help='API URL (default: http://localhost:5045)')
    
    return parser.parse_args()

def check_server_status(base_url):
    """Check if the server is running and responding."""
    print("\n===== CHECKING SERVER STATUS =====")
    try:
        response = requests.get(urljoin(base_url, '/health'), timeout=5)
        print(f"Status code: {response.status_code}")
        if response.ok:
            print("Server is running and healthy")
            return True
        else:
            print(f"Server returned an error: {response.text}")
            return False
    except requests.exceptions.ConnectionError:
        print("Error: Could not connect to the server. Is it running?")
        return False
    except Exception as e:
        print(f"Error checking server status: {str(e)}")
        return False

def login(base_url, tenant_id, email, password):
    """Login and get a JWT token."""
    print("\n===== LOGGING IN =====")
    
    if not all([tenant_id, email, password]):
        print("Error: tenant ID, email, and password are required for login")
        return None
    
    # Validate tenant UUID format
    try:
        uuid.UUID(tenant_id)
    except ValueError:
        print(f"Error: '{tenant_id}' is not a valid UUID format")
        return None
    
    url = urljoin(base_url, f'/tenants/{tenant_id}/auth/login')
    data = {
        'email': email,
        'password': password,
        'tenant_id': tenant_id  # Note: We're passing tenant_id in both URL and body
    }
    
    try:
        print(f"Sending request to: {url}")
        print(f"With data: {json.dumps({k: v if k != 'password' else '****' for k, v in data.items()})}")
        
        response = requests.post(url, json=data)
        print(f"Status code: {response.status_code}")
        
        if response.ok:
            token_data = response.json()
            print("Login successful!")
            token = token_data.get('access_token')
            if token:
                print(f"Access token: {token[:20]}...{token[-20:] if len(token) > 40 else ''}")
                return token
            else:
                print("Error: No access token in response")
                return None
        else:
            print(f"Login failed: {response.text}")
            return None
    except Exception as e:
        print(f"Error during login: {str(e)}")
        return None

def get_jira_credentials(base_url, tenant_id, token):
    """Get current JIRA credentials status."""
    print("\n===== GETTING JIRA CREDENTIALS STATUS =====")
    
    if not all([tenant_id, token]):
        print("Error: tenant ID and token are required")
        return False
    
    url = urljoin(base_url, f'/tenants/{tenant_id}/jira/credentials')
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    try:
        print(f"Sending request to: {url}")
        print(f"With headers: {json.dumps({k: v if k != 'Authorization' else 'Bearer ***' for k, v in headers.items()})}")
        
        response = requests.get(url, headers=headers)
        print(f"Status code: {response.status_code}")
        
        if response.ok:
            cred_data = response.json()
            print("JIRA credentials status:")
            print(json.dumps(cred_data, indent=2))
            return True
        else:
            print(f"Error getting credentials: {response.text}")
            return False
    except Exception as e:
        print(f"Error retrieving JIRA credentials: {str(e)}")
        return False

def add_jira_credentials(base_url, tenant_id, token, jira_base_url, api_key):
    """Add or update JIRA credentials."""
    print("\n===== ADDING JIRA CREDENTIALS =====")
    
    if not all([tenant_id, token, jira_base_url, api_key]):
        print("Error: tenant ID, token, JIRA base URL, and API key are required")
        return False
    
    url = urljoin(base_url, f'/tenants/{tenant_id}/jira/credentials')
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    data = {
        'base_url': jira_base_url,
        'api_key': api_key
    }
    
    try:
        print(f"Sending request to: {url}")
        print(f"With headers: {json.dumps({k: v if k != 'Authorization' else 'Bearer ***' for k, v in headers.items()})}")
        print(f"With data: {json.dumps({k: v if k != 'api_key' else '****' for k, v in data.items()})}")
        
        response = requests.post(url, json=data, headers=headers)
        print(f"Status code: {response.status_code}")
        
        if response.ok:
            result = response.json()
            print("JIRA credentials added/updated successfully:")
            print(json.dumps(result, indent=2))
            return True
        else:
            print(f"Error adding credentials: {response.text}")
            # Add troubleshooting information
            if response.status_code == 401:
                print("\nTROUBLESHOOTING 401 UNAUTHORIZED:")
                print("1. Check that your JWT token is valid (not expired)")
                print("2. Verify the tenant ID in the JWT token matches the requested tenant ID")
                print("3. Try getting a new token with the login command")
                print("4. Use the debug_jwt.py tool to analyze your token:")
                print(f"   python tools/debug_jwt.py \"{token}\" \"{tenant_id}\"")
            return False
    except Exception as e:
        print(f"Error adding JIRA credentials: {str(e)}")
        return False

def delete_jira_credentials(base_url, tenant_id, token):
    """Delete JIRA credentials."""
    print("\n===== DELETING JIRA CREDENTIALS =====")
    
    if not all([tenant_id, token]):
        print("Error: tenant ID and token are required")
        return False
    
    url = urljoin(base_url, f'/tenants/{tenant_id}/jira/credentials')
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    try:
        print(f"Sending request to: {url}")
        print(f"With headers: {json.dumps({k: v if k != 'Authorization' else 'Bearer ***' for k, v in headers.items()})}")
        
        response = requests.delete(url, headers=headers)
        print(f"Status code: {response.status_code}")
        
        if response.ok:
            result = response.json()
            print("JIRA credentials deleted successfully:")
            print(json.dumps(result, indent=2))
            return True
        else:
            print(f"Error deleting credentials: {response.text}")
            return False
    except Exception as e:
        print(f"Error deleting JIRA credentials: {str(e)}")
        return False

def main():
    args = parse_args()
    
    if args.command == 'status':
        check_server_status(args.url)
        return
    
    if args.command == 'login':
        if not all([args.tenant, args.email, args.password]):
            print("Error: --tenant, --email, and --password are required for login")
            return
        login(args.url, args.tenant, args.email, args.password)
        return
    
    if not args.token:
        print("Error: --token is required for this command")
        if args.email and args.password and args.tenant:
            print("Attempting to login to get a token...")
            args.token = login(args.url, args.tenant, args.email, args.password)
            if not args.token:
                return
        else:
            return
    
    if not args.tenant:
        print("Error: --tenant is required")
        return
    
    if args.command == 'get':
        get_jira_credentials(args.url, args.tenant, args.token)
    elif args.command == 'add':
        if not all([args.base_url, args.api_key]):
            print("Error: --base-url and --api-key are required for add command")
            return
        add_jira_credentials(args.url, args.tenant, args.token, args.base_url, args.api_key)
    elif args.command == 'delete':
        delete_jira_credentials(args.url, args.tenant, args.token)

if __name__ == "__main__":
    main()