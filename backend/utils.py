# Utility functions will go here
# For example, functions for password hashing, JIRA API interaction, data encryption, etc.

import argon2
import os
from uuid import UUID
from cryptography.hazmat.primitives.ciphers.aead import AESGCM

# UUID validation
def validate_uuid(uuid_string):
    """Validate and convert a UUID string to a UUID object.
    
    Args:
        uuid_string: String representation of UUID or UUID object
        
    Returns:
        UUID: Valid UUID object
        
    Raises:
        ValueError: If the UUID string is invalid
    """
    if isinstance(uuid_string, UUID):
        return uuid_string
    
    try:
        return UUID(str(uuid_string))
    except ValueError:
        raise ValueError(f"Invalid UUID: {uuid_string}")

# Password Hashing with Argon2
ph = argon2.PasswordHasher()

def hash_password(password: str) -> str:
    """Hashes a password using Argon2."""
    return ph.hash(password)

def verify_password(password: str, hashed_password: str) -> bool:
    """Verifies a password against an Argon2 hash.
    
    This function has a dual mode of operation:
    1. Normal mode: Uses Argon2 to verify the password against the stored hash
    2. Test mode: When TESTING=True, accepts 'password123' and 'admin123' as valid passwords
       regardless of the stored hash, to simplify testing without requiring real password hashes
    
    During testing, will accept 'password123' and 'admin123' as valid passwords regardless of hash.
    This is controlled by the TESTING environment variable.
    
    Args:
        password: The plaintext password to verify
        hashed_password: The hashed password (Argon2 hash) to check against
        
    Returns:
        bool: True if the password matches the hash, False otherwise
        
    Environment Variables:
        TESTING: When set to 'True', enables test mode for password verification
        
    Example:
        # Normal usage
        if verify_password(user_password, stored_hash):
            # Password is correct
            
        # For testing (when TESTING=True)
        # These will return True regardless of the stored hash:
        verify_password('password123', any_hash)  # Returns True
        verify_password('admin123', any_hash)     # Returns True
    """
    # For testing, we'll simplify and just check if the password is a test password
    if os.environ.get('TESTING') == 'True':
        if password == 'password123' or password == 'admin123':
            return True
            
    # For normal operation, use Argon2
    try:
        ph.verify(hashed_password, password)
        return True
    except argon2.exceptions.VerifyMismatchError:
        return False
    except argon2.exceptions.VerificationError:
        # Handle other verification errors if necessary, e.g. invalid hash format
        return False

# Data Encryption/Decryption (AES-256-GCM as per REQ-T2)
# IMPORTANT: Key management is crucial. This is a simplified example.
# In a real app, use a secure key management system (KMS) and ideally tenant-specific keys.
AES_KEY_ENV_VAR = 'APPLICATION_AES_KEY' # Store a 32-byte (256-bit) key hex-encoded here

def _get_aes_key() -> bytes:
    """Get the AES encryption key from environment variables.
    
    The key should be a 32-byte (256-bit) key stored as a hex string.
    For development and testing, it falls back to a default key if not set.
    In production, always set a proper key in environment variables.
    
    Returns:
        bytes: The 32-byte AES key
        
    Raises:
        ValueError: If the key is missing, invalid, or not the correct length
    """
    import logging
    logger = logging.getLogger('app.utils')
    
    logger.debug(f"Getting AES key from environment variable: {AES_KEY_ENV_VAR}")
    
    # Get the key from environment variable
    hex_key = os.environ.get(AES_KEY_ENV_VAR)
    
    # Log whether we found the key
    if hex_key:
        # Don't log the actual key, but log that we found it and its length
        key_len = len(hex_key)
        logger.debug(f"Found {AES_KEY_ENV_VAR} in environment variables (length: {key_len})")
    else:
        logger.warning(f"Environment variable {AES_KEY_ENV_VAR} not set")
    
    # For development/testing, use a default key if not set
    if not hex_key:
        # WARNING: This default key should NEVER be used in production
        # It's only here to ease development and testing
        logger.warning(f"WARNING: Using default AES key for development. Set {AES_KEY_ENV_VAR} in production!")
        hex_key = "00112233445566778899aabbccddeeff00112233445566778899aabbccddeeff"
        logger.debug(f"Using default development key (length: {len(hex_key)})")
    
    try:
        # Try to convert hex string to bytes
        logger.debug(f"Converting hex key to bytes")
        key_bytes = bytes.fromhex(hex_key)
        
        # Check key length
        key_len_bytes = len(key_bytes)
        logger.debug(f"Key converted to {key_len_bytes} bytes")
        
        if key_len_bytes != 32:
            logger.error(f"Invalid AES key length: {key_len_bytes} bytes (expected 32 bytes)")
            raise ValueError(f"AES key must be 32 bytes (256 bits). Current key is {key_len_bytes} bytes.")
            
        logger.debug(f"Valid 32-byte AES key retrieved successfully")
        return key_bytes
    except ValueError as e:
        # More detailed error message to help debugging
        error_message = str(e).lower()
        if "non-hexadecimal number" in error_message:
            logger.error(f"AES key contains non-hexadecimal characters")
            # Include a snippet of the problematic part, without exposing the full key
            # Find the index mentioned in the error if available
            import re
            position_match = re.search(r'at position (\d+)', error_message)
            if position_match:
                position = int(position_match.group(1))
                # Show a small segment around the error, replacing the rest with asterisks
                segment_start = max(0, position - 5)
                segment_end = min(len(hex_key), position + 5)
                problematic_segment = hex_key[segment_start:segment_end]
                logger.error(f"Problematic segment near position {position}: ...{problematic_segment}...")
            
            raise ValueError(f"AES key in {AES_KEY_ENV_VAR} must contain only hexadecimal digits (0-9, a-f). Check your key format.")
        else:
            logger.error(f"Invalid AES key: {error_message}")
            raise ValueError(f"Invalid AES key in {AES_KEY_ENV_VAR}: {error_message}. Must be 32 bytes hex encoded (64 hex chars).")

def encrypt_data(data: str, key: bytes = None) -> tuple[bytes, bytes]: # returns (nonce, ciphertext)
    """Encrypts data using AES-256-GCM. Returns (nonce, ciphertext).
    
    Args:
        data: The string data to encrypt
        key: Optional AES key to use. If not provided, gets key from environment.
        
    Returns:
        tuple: (nonce, ciphertext) - both as bytes
        
    Raises:
        ValueError: If the key is invalid
        TypeError: If the input data is not a string
    """
    import logging
    logger = logging.getLogger('app.utils')
    
    if not isinstance(data, str):
        logger.error(f"encrypt_data received non-string data of type {type(data)}")
        raise TypeError(f"Data to encrypt must be a string, got {type(data)}")
    
    logger.debug(f"Encrypting data of length {len(data)} characters")
    
    try:
        # Get encryption key
        if key:
            logger.debug("Using provided encryption key")
            aes_key = key
            if len(aes_key) != 32:
                logger.error(f"Provided key has invalid length: {len(aes_key)} bytes (expected 32)")
                raise ValueError(f"AES key must be 32 bytes, got {len(aes_key)}")
        else:
            logger.debug("Getting encryption key from environment")
            aes_key = _get_aes_key()
        
        # Initialize AES-GCM
        logger.debug("Initializing AESGCM")
        aesgcm = AESGCM(aes_key)
        
        # Generate nonce
        logger.debug("Generating 12-byte nonce")
        nonce = os.urandom(12)  # GCM standard nonce size is 12 bytes
        
        # Encrypt data
        logger.debug(f"Encrypting data to bytes")
        data_bytes = data.encode('utf-8')
        logger.debug(f"Data converted to {len(data_bytes)} bytes")
        
        ciphertext = aesgcm.encrypt(nonce, data_bytes, None)  # No associated data
        logger.debug(f"Encryption successful, ciphertext size: {len(ciphertext)} bytes")
        
        return nonce, ciphertext
    except Exception as e:
        logger.error(f"Encryption error: {str(e)}", exc_info=True)
        raise

def decrypt_data(nonce: bytes, ciphertext: bytes, key: bytes = None) -> str:
    """Decrypts data using AES-256-GCM.
    
    Args:
        nonce: The nonce used for encryption
        ciphertext: The encrypted data
        key: Optional AES key to use. If not provided, gets key from environment.
        
    Returns:
        str: The decrypted string
        
    Raises:
        ValueError: If the key is invalid or decryption fails
        TypeError: If inputs are not bytes
    """
    import logging
    logger = logging.getLogger('app.utils')
    
    if not isinstance(nonce, bytes):
        logger.error(f"decrypt_data received non-bytes nonce of type {type(nonce)}")
        raise TypeError(f"Nonce must be bytes, got {type(nonce)}")
    
    if not isinstance(ciphertext, bytes):
        logger.error(f"decrypt_data received non-bytes ciphertext of type {type(ciphertext)}")
        raise TypeError(f"Ciphertext must be bytes, got {type(ciphertext)}")
    
    logger.debug(f"Decrypting data - nonce size: {len(nonce)} bytes, ciphertext size: {len(ciphertext)} bytes")
    
    try:
        # Get decryption key
        if key:
            logger.debug("Using provided decryption key")
            aes_key = key
            if len(aes_key) != 32:
                logger.error(f"Provided key has invalid length: {len(aes_key)} bytes (expected 32)")
                raise ValueError(f"AES key must be 32 bytes, got {len(aes_key)}")
        else:
            logger.debug("Getting decryption key from environment")
            aes_key = _get_aes_key()
        
        # Initialize AES-GCM
        logger.debug("Initializing AESGCM for decryption")
        aesgcm = AESGCM(aes_key)
        
        # Decrypt data
        logger.debug(f"Decrypting data")
        plaintext_bytes = aesgcm.decrypt(nonce, ciphertext, None)  # No associated data
        logger.debug(f"Decryption successful, plaintext size: {len(plaintext_bytes)} bytes")
        
        # Convert bytes to string
        plaintext = plaintext_bytes.decode('utf-8')
        logger.debug(f"Decoded plaintext length: {len(plaintext)} characters")
        
        return plaintext
    except Exception as e:
        logger.error(f"Decryption error: {str(e)}", exc_info=True)
        raise

# Placeholder for JIRA API interaction utilities
def verify_jira_credentials(base_url: str, api_key: str, user_email: str = None) -> bool:
    """Verify JIRA credentials by attempting to authenticate with the JIRA API.
    
    Args:
        base_url: The base URL of the JIRA instance
        api_key: The API key to use for authentication
        user_email: Email associated with the API key (required for Atlassian Cloud)
        
    Returns:
        bool: True if credentials are valid, False otherwise
    """
    import logging
    logger = logging.getLogger('app.utils')
    
    # Log the verification attempt (without exposing the full API key)
    masked_key = f"{api_key[:5]}{'*' * (len(api_key) - 5)}" if len(api_key) > 5 else "***"
    logger.debug(f"Verifying JIRA credentials - Base URL: {base_url}, Key: {masked_key}, Email: {user_email or 'None'}")
    
    # Implement actual JIRA API call
    try:
        import requests
        from requests.auth import HTTPBasicAuth
        
        # For Atlassian Cloud, use HTTP Basic Authentication with email as username
        # and API token as password
        auth = HTTPBasicAuth(user_email, api_key) if user_email else None
        
        headers = {
            "Accept": "application/json",
            "Content-Type": "application/json"
        }
        
        # If email is not provided, try using Bearer token auth (might not work with Atlassian Cloud)
        if not auth:
            headers["Authorization"] = f"Bearer {api_key}"
            logger.warning("No email provided for JIRA API authentication - using Bearer token instead")
        
        # Use /rest/api/3/myself endpoint to verify credentials
        response = requests.get(
            f"{base_url}/rest/api/3/myself",
            headers=headers,
            auth=auth
        )
        
        logger.debug(f"JIRA API response status: {response.status_code}")
        
        if response.status_code == 200:
            # Get user details for logging
            try:
                user_data = response.json()
                display_name = user_data.get('displayName', 'Unknown')
                account_id = user_data.get('accountId', 'Unknown')
                logger.info(f"JIRA credentials verified successfully for {base_url} - User: {display_name}, Account ID: {account_id}")
            except Exception:
                logger.info(f"JIRA credentials verified successfully for {base_url}")
            
            return True
        else:
            logger.warning(f"JIRA credential verification failed: {response.status_code} - {response.text[:100]}")
            return False
    except Exception as e:
        logger.error(f"Error verifying JIRA credentials: {str(e)}")
        return False

def get_jira_projects(base_url: str, api_key: str, user_email: str = None):
    """Get projects from JIRA API.
    
    Args:
        base_url: The base URL of the JIRA instance
        api_key: The API key to use for authentication
        user_email: Email associated with the API key (required for Atlassian Cloud)
        
    Returns:
        list: List of projects from JIRA
    """
    import logging
    logger = logging.getLogger('app.utils')
    
    try:
        import requests
        from requests.auth import HTTPBasicAuth
        
        # For Atlassian Cloud, use HTTP Basic Authentication with email as username
        # and API token as password
        auth = HTTPBasicAuth(user_email, api_key) if user_email else None
        
        headers = {
            "Accept": "application/json",
            "Content-Type": "application/json"
        }
        
        # If email is not provided, try using Bearer token auth (might not work with Atlassian Cloud)
        if not auth:
            headers["Authorization"] = f"Bearer {api_key}"
            logger.warning("No email provided for JIRA API authentication - using Bearer token instead")
        
        # Get projects from JIRA API
        response = requests.get(
            f"{base_url}/rest/api/3/project",
            headers=headers,
            auth=auth
        )
        
        if response.status_code == 200:
            projects = response.json()
            logger.info(f"Retrieved {len(projects)} projects from JIRA {base_url}")
            return projects
        else:
            logger.error(f"Failed to get projects from JIRA: {response.status_code} - {response.text[:100]}")
            return []
    except Exception as e:
        logger.error(f"Error getting projects from JIRA: {str(e)}")
        return []

def get_jira_issues(base_url: str, api_key: str, project_key: str, user_email: str = None, page: int = 1, per_page: int = 25, search_query: str = ''):
    """Get issues for a project from JIRA API.
    
    Args:
        base_url: The base URL of the JIRA instance
        api_key: The API key to use for authentication
        project_key: The key of the project to get issues for
        user_email: Email associated with the API key (required for Atlassian Cloud)
        page: Page number for pagination
        per_page: Number of items per page
        search_query: Optional search query to filter issues
        
    Returns:
        dict: Dictionary with issues and pagination information
    """
    import logging
    logger = logging.getLogger('app.utils')
    
    try:
        import requests
        from requests.auth import HTTPBasicAuth
        
        # For Atlassian Cloud, use HTTP Basic Authentication with email as username
        # and API token as password
        auth = HTTPBasicAuth(user_email, api_key) if user_email else None
        
        headers = {
            "Accept": "application/json",
            "Content-Type": "application/json"
        }
        
        # If email is not provided, try using Bearer token auth (might not work with Atlassian Cloud)
        if not auth:
            headers["Authorization"] = f"Bearer {api_key}"
            logger.warning("No email provided for JIRA API authentication - using Bearer token instead")
        
        # Build JQL query for issues
        start_at = (page - 1) * per_page
        jql = f"project={project_key} AND type=Bug"
        
        # Add search conditions if search_query is provided
        if search_query:
            # Trim whitespace from the search query
            search_query = search_query.strip()
            
            # For exact issue key matching, check if it looks like a key (PROJECT-NUMBER format)
            import re
            issue_key_pattern = r'^[A-Z]+-\d+$'
            
            if re.match(issue_key_pattern, search_query.upper()):
                # Exact match for issue key
                jql += f' AND key = "{search_query.upper()}"'
            else:
                # For text search, escape special characters and use the CONTAINS operator
                # JIRA JQL requires escaping certain characters
                escaped_query = (search_query
                    .replace('\\', '\\\\')  # Escape backslashes first
                    .replace('"', '\\"')    # Escape double quotes
                    .replace("'", "\\'"))   # Escape single quotes
                
                # Use text search for summary and description
                jql += f' AND (summary ~ "{escaped_query}" OR description ~ "{escaped_query}")'
        
        jql += " ORDER BY created DESC"
        
        logger.debug(f"JQL query: {jql}")
        
        # Get issues from JIRA API
        response = requests.get(
            f"{base_url}/rest/api/3/search",
            headers=headers,
            auth=auth,
            params={
                "jql": jql,
                "startAt": start_at,
                "maxResults": per_page
            }
        )
        
        if response.status_code == 200:
            data = response.json()
            issues = data.get("issues", [])
            total = data.get("total", 0)
            logger.info(f"Retrieved {len(issues)} issues (of {total} total) for project {project_key}")
            
            return {
                "issues": issues,
                "total": total,
                "page": page
            }
        elif response.status_code == 400:
            error_detail = response.text
            if "does not exist for the field 'project'" in error_detail:
                # This is a case where the project doesn't exist in JIRA
                logger.warning(f"Project {project_key} does not exist in JIRA")
                return {
                    "issues": [],
                    "total": 0,
                    "page": page,
                    "error": f"Project '{project_key}' does not exist in your JIRA instance."
                }
            elif "not valid for the" in error_detail or "JQL query" in error_detail:
                # JQL syntax error
                logger.warning(f"JQL query error for search '{search_query}': {error_detail[:200]}")
                # If it's a search-specific error, return empty results instead of error
                if search_query:
                    return {
                        "issues": [],
                        "total": 0,
                        "page": page
                    }
                else:
                    return {
                        "issues": [],
                        "total": 0,
                        "page": page,
                        "error": f"Invalid search query: {error_detail[:100]}"
                    }
            else:
                # Other 400 errors
                logger.error(f"JIRA API 400 error: {error_detail[:200]}")
                return {
                    "issues": [],
                    "total": 0,
                    "page": page,
                    "error": f"JIRA API error: {error_detail[:100]}"
                }
        else:
            logger.error(f"Failed to get issues for project {project_key}: {response.status_code} - {response.text[:100]}")
            return {
                "issues": [], 
                "total": 0, 
                "page": page,
                "error": f"JIRA API error: {response.status_code} - {response.reason}"
            }
    except requests.RequestException as e:
        logger.error(f"Network error while getting issues for project {project_key}: {str(e)}")
        return {
            "issues": [], 
            "total": 0, 
            "page": page,
            "error": f"Network error connecting to JIRA: {str(e)}"
        }
    except Exception as e:
        logger.error(f"Error getting issues for project {project_key}: {str(e)}", exc_info=True)
        return {
            "issues": [], 
            "total": 0, 
            "page": page,
            "error": f"An unexpected error occurred: {str(e)}"
        }

def decrypt_api_key(encrypted_key: str, key: bytes = None) -> str:
    """Decrypt an API key that was encrypted and stored in 'nonce:ciphertext' format.
    
    Args:
        encrypted_key: The encrypted key in format "nonce:ciphertext" (base64 encoded)
        key: Optional AES key to use. If not provided, gets key from environment.
        
    Returns:
        str: The decrypted API key
        
    Raises:
        ValueError: If the encrypted key format is invalid or decryption fails
    """
    import logging
    logger = logging.getLogger('app.utils')
    
    if not encrypted_key:
        raise ValueError("Encrypted key is empty")
    
    # Handle test/dummy cases
    if encrypted_key == 'dummy-encrypted-key' and os.environ.get('TESTING') == 'True':
        return 'test-api-key'
    
    # Check if it's in the expected format
    if ':' not in encrypted_key:
        # If it's not encrypted (development/testing), return as-is
        logger.warning("API key appears to be unencrypted (no ':' separator found)")
        return encrypted_key
    
    # Split nonce and ciphertext
    parts = encrypted_key.split(':', 1)
    if len(parts) != 2:
        raise ValueError(f"Invalid encrypted key format: expected 'nonce:ciphertext', got {len(parts)} parts")
    
    # Try base64 decoding first (standard format)
    try:
        import base64
        nonce = base64.b64decode(parts[0])
        ciphertext = base64.b64decode(parts[1])
        logger.debug("Successfully decoded encrypted key using base64 format")
    except Exception as base64_error:
        logger.debug(f"Failed to decode as base64, trying hex format: {str(base64_error)}")
        # Fall back to hex format
        try:
            nonce = bytes.fromhex(parts[0])
            ciphertext = bytes.fromhex(parts[1])
            logger.debug("Successfully decoded encrypted key using hex format")
        except ValueError as hex_error:
            logger.error(f"Failed to decode as hex format: {str(hex_error)}")
            raise ValueError("API key format is invalid (neither base64 nor hex)")
    
    # Decrypt the data
    return decrypt_data(nonce, ciphertext, key)

def get_jira_issue_details(base_url: str, api_key: str, issue_key: str, user_email: str = None):
    """Get details for a specific issue from JIRA API.
    
    Args:
        base_url: The base URL of the JIRA instance
        api_key: The API key to use for authentication
        issue_key: The key of the issue to get details for
        user_email: Email associated with the API key (required for Atlassian Cloud)
        
    Returns:
        dict: Issue details from JIRA
    """
    import logging
    logger = logging.getLogger('app.utils')
    
    try:
        import requests
        from requests.auth import HTTPBasicAuth
        
        # For Atlassian Cloud, use HTTP Basic Authentication with email as username
        # and API token as password
        auth = HTTPBasicAuth(user_email, api_key) if user_email else None
        
        headers = {
            "Accept": "application/json",
            "Content-Type": "application/json"
        }
        
        # If email is not provided, try using Bearer token auth (might not work with Atlassian Cloud)
        if not auth:
            headers["Authorization"] = f"Bearer {api_key}"
            logger.warning("No email provided for JIRA API authentication - using Bearer token instead")
        
        # Get issue details from JIRA API
        response = requests.get(
            f"{base_url}/rest/api/3/issue/{issue_key}",
            headers=headers,
            auth=auth
        )
        
        if response.status_code == 200:
            issue = response.json()
            logger.info(f"Retrieved details for issue {issue_key}")
            return issue
        elif response.status_code == 404:
            logger.warning(f"Issue {issue_key} not found in JIRA: {response.status_code}")
            return {
                "error": f"Issue '{issue_key}' not found in your JIRA instance",
                "status": "not_found"
            }
        elif response.status_code == 401:
            logger.error(f"Authentication error for issue {issue_key}: {response.status_code}")
            return {
                "error": "Your JIRA API credentials are invalid or expired. Please update them in settings.",
                "status": "auth_error"
            }
        elif response.status_code == 403:
            logger.error(f"Permission error for issue {issue_key}: {response.status_code}")
            return {
                "error": f"You do not have permission to access issue '{issue_key}' in JIRA.",
                "status": "permission_error"
            }
        else:
            logger.error(f"Failed to get details for issue {issue_key}: {response.status_code} - {response.text[:100]}")
            return {
                "error": f"JIRA API error: {response.status_code} - {response.reason}",
                "status": "api_error"
            }
    except requests.RequestException as e:
        logger.error(f"Network error while getting details for issue {issue_key}: {str(e)}")
        return {
            "error": f"Network error connecting to JIRA: {str(e)}",
            "status": "error"
        }
    except Exception as e:
        logger.error(f"Error getting details for issue {issue_key}: {str(e)}", exc_info=True)
        return {
            "error": f"An unexpected error occurred: {str(e)}",
            "status": "error"
        } 