# PR Automation Architecture

## Overview
This document outlines the architecture for adding repository management and automated PR creation to the JIRA Bug Browser application.

## Use Cases
1. **Repository Configuration**: Tenants can configure multiple code repositories
2. **Bug Fix Workflow**: Link JIRA issues to code changes
3. **Automated PR Creation**: Generate PRs with proper descriptions and linking
4. **Multi-Platform Support**: Works with GitHub and Bitbucket

## Database Schema

### New Tables

```sql
-- Repository configurations per tenant
CREATE TABLE repository_configs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id),
    repo_name VARCHAR(255) NOT NULL,
    repo_type VARCHAR(50) NOT NULL CHECK (repo_type IN ('github', 'bitbucket')),
    local_path VARCHAR(500),
    remote_url VARCHAR(500) NOT NULL,
    default_branch VARCHAR(100) DEFAULT 'main',
    credentials_encrypted TEXT, -- Encrypted PAT/credentials <PERSON><PERSON><PERSON>
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES users(id),
    is_active BOOLEAN DEFAULT true,
    UNIQUE(tenant_id, repo_name)
);

-- PR creation history
CREATE TABLE pull_requests (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id),
    repo_config_id UUID NOT NULL REFERENCES repository_configs(id),
    issue_key VARCHAR(50) NOT NULL, -- JIRA issue key
    pr_number INTEGER,
    pr_url VARCHAR(500),
    title VARCHAR(500) NOT NULL,
    description TEXT,
    source_branch VARCHAR(100) NOT NULL,
    target_branch VARCHAR(100) NOT NULL,
    status VARCHAR(50) DEFAULT 'pending', -- pending, created, merged, closed
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by UUID REFERENCES users(id),
    metadata JSONB -- Platform-specific data
);

-- Repository activity logs
CREATE TABLE repository_activity (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id),
    repo_config_id UUID NOT NULL REFERENCES repository_configs(id),
    activity_type VARCHAR(50) NOT NULL, -- clone, pull, push, pr_created
    status VARCHAR(50) NOT NULL, -- success, failed
    details JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    user_id UUID REFERENCES users(id)
);
```

## API Endpoints

### Repository Management
```
# Repository CRUD
GET    /tenants/{tid}/repositories              # List repositories
POST   /tenants/{tid}/repositories              # Add repository
GET    /tenants/{tid}/repositories/{repo_id}    # Get repository details
PUT    /tenants/{tid}/repositories/{repo_id}    # Update repository
DELETE /tenants/{tid}/repositories/{repo_id}    # Delete repository

# Repository operations
POST   /tenants/{tid}/repositories/{repo_id}/sync    # Sync with remote
POST   /tenants/{tid}/repositories/{repo_id}/test    # Test connection
```

### PR Creation
```
# PR operations
POST   /tenants/{tid}/pull-requests/create      # Create PR
GET    /tenants/{tid}/pull-requests             # List PRs
GET    /tenants/{tid}/pull-requests/{pr_id}     # Get PR details
POST   /tenants/{tid}/pull-requests/{pr_id}/sync # Sync PR status
```

### Integration with Issues
```
# Issue-PR linking
GET    /tenants/{tid}/issues/{issue_key}/pull-requests  # PRs for issue
POST   /tenants/{tid}/issues/{issue_key}/create-pr      # Create PR for issue
```

## Service Architecture

### Core Services

#### 1. Repository Service
```python
class RepositoryService:
    def __init__(self, encryption_service, git_service):
        self.encryption = encryption_service
        self.git = git_service
    
    def add_repository(self, tenant_id, repo_config):
        # Validate repository access
        # Encrypt credentials
        # Store in database
        # Clone/initialize local repository
        pass
    
    def sync_repository(self, repo_id):
        # Fetch latest changes
        # Update local repository
        pass
    
    def validate_credentials(self, repo_config):
        # Test connection to remote repository
        pass
```

#### 2. PR Creation Service
```python
class PRCreationService:
    def __init__(self, repo_service, platform_factory):
        self.repo_service = repo_service
        self.platform_factory = platform_factory
    
    def create_pr(self, tenant_id, pr_request):
        # Validate permissions
        # Create feature branch
        # Generate PR description from template
        # Create PR via platform API
        # Store PR record
        # Link to JIRA issue
        pass
    
    def generate_pr_description(self, issue_data, template):
        # Create standardized PR description
        # Include JIRA issue link
        # Add checklist items
        pass
```

#### 3. Platform Integration Factory
```python
class PlatformFactory:
    def get_client(self, repo_type, credentials):
        if repo_type == 'github':
            return GitHubClient(credentials)
        elif repo_type == 'bitbucket':
            return BitbucketClient(credentials)
        raise ValueError(f"Unsupported platform: {repo_type}")

class GitHubClient:
    def create_pr(self, repo, pr_data):
        # Use GitHub API to create PR
        pass
    
    def get_pr_status(self, repo, pr_number):
        # Fetch PR status from GitHub
        pass

class BitbucketClient:
    def create_pr(self, repo, pr_data):
        # Use Bitbucket API to create PR
        pass
```

## Security Considerations

### Credential Management
- Store platform tokens encrypted with AES-256-GCM
- Never log or expose credentials
- Implement credential rotation reminders
- Use least-privilege tokens (repo access only)

### Access Control
- Repository management requires admin role
- PR creation tied to user permissions
- Audit all repository operations
- Validate tenant context for all operations

### Git Operations
- Use SSH keys or tokens, never passwords
- Sanitize all git commands
- Implement command timeouts
- Isolate operations per tenant

## Implementation Flow

### Phase 1: Repository Management
1. Create database schema
2. Implement repository CRUD endpoints
3. Add credential encryption
4. Build repository service
5. Create UI for repository management

### Phase 2: Git Integration
1. Implement git service wrapper
2. Add local repository management
3. Create sync functionality
4. Add branch management

### Phase 3: PR Automation
1. Implement platform clients (GitHub/Bitbucket)
2. Create PR generation service
3. Add PR template system
4. Build PR creation UI

### Phase 4: JIRA Integration
1. Link PRs to JIRA issues
2. Update JIRA with PR status
3. Add PR information to issue view
4. Create automation rules

## Configuration

### Environment Variables
```bash
# Repository settings
REPO_BASE_PATH=/var/app/repositories
MAX_REPO_SIZE_MB=500
GIT_TIMEOUT_SECONDS=30

# Platform limits
GITHUB_RATE_LIMIT=60
BITBUCKET_RATE_LIMIT=100

# PR templates
PR_TEMPLATE_PATH=/var/app/templates/pr
```

### PR Template Example
```markdown
## Summary
Fixes issue: [{{issue_key}}]({{jira_url}}/browse/{{issue_key}})

## Description
{{issue_summary}}

## Changes
- [ ] Implementation details here
- [ ] Tests added/updated
- [ ] Documentation updated

## Testing
- [ ] Manual testing completed
- [ ] Automated tests pass

## Related Issues
- {{issue_key}}: {{issue_summary}}
```

## Error Handling

### Common Scenarios
1. **Invalid credentials**: Prompt user to update
2. **Repository not found**: Clear error message
3. **Rate limiting**: Queue and retry
4. **Merge conflicts**: Alert user for manual resolution
5. **Network timeouts**: Implement exponential backoff

## Monitoring

### Key Metrics
- PR creation success rate
- Average PR creation time
- Repository sync frequency
- Credential validation failures
- Platform API usage

### Audit Events
- Repository added/modified/deleted
- PR created/updated
- Credentials updated
- Sync operations
- Failed operations

## Future Enhancements
1. **Webhook integration**: Real-time PR status updates
2. **Code review assignment**: Auto-assign reviewers
3. **CI/CD integration**: Trigger builds on PR creation
4. **Merge automation**: Auto-merge approved PRs
5. **Branch policies**: Enforce naming conventions
6. **PR templates**: Multiple templates per issue type
7. **Bulk operations**: Create PRs for multiple issues