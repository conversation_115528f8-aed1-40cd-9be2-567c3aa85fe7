# PR Automation Flow Diagram

## High-Level Architecture
```
┌─────────────────────────────────────────────────────────────────────────┐
│                          JIRA Bug Browser                                │
├─────────────────────────────────────────────────────────────────────────┤
│                                                                         │
│  ┌──────────────┐     ┌───────────────┐     ┌──────────────────┐      │
│  │   Frontend   │────▶│   Flask API   │────▶│    Database      │      │
│  │   (React)    │     │   Backend     │     │  (PostgreSQL)    │      │
│  └──────────────┘     └───────┬───────┘     └──────────────────┘      │
│                              │                                         │
│                              ▼                                         │
│  ┌─────────────────────────────────────────────────────────────┐      │
│  │                    Repository Service                        │      │
│  │  ┌─────────────┐  ┌─────────────┐  ┌───────────────────┐  │      │
│  │  │   Git       │  │  Platform   │  │   PR Creation     │  │      │
│  │  │  Service    │  │   Factory   │  │    Service        │  │      │
│  │  └─────────────┘  └─────────────┘  └───────────────────┘  │      │
│  └─────────────────────────────────────────────────────────────┘      │
│                              │                                         │
└──────────────────────────────┼─────────────────────────────────────────┘
                               │
                               ▼
        ┌──────────────────────┴──────────────────────┐
        │                                             │
   ┌────▼─────┐                              ┌───────▼────┐
   │  GitHub  │                              │ Bitbucket  │
   │   API    │                              │    API     │
   └──────────┘                              └────────────┘
```

## PR Creation Flow

```
User                 Frontend              Backend                Git Service         Platform API
 │                      │                     │                       │                   │
 │  Select Issue        │                     │                       │                   │
 ├─────────────────────▶│                     │                       │                   │
 │                      │                     │                       │                   │
 │                      │  GET issue details  │                       │                   │
 │                      ├────────────────────▶│                       │                   │
 │                      │                     │                       │                   │
 │                      │◀────────────────────┤                       │                   │
 │                      │   Issue data        │                       │                   │
 │                      │                     │                       │                   │
 │  Configure PR        │                     │                       │                   │
 ├─────────────────────▶│                     │                       │                   │
 │                      │                     │                       │                   │
 │                      │  POST create PR     │                       │                   │
 │                      ├────────────────────▶│                       │                   │
 │                      │                     │                       │                   │
 │                      │                     │  Create branch        │                   │
 │                      │                     ├──────────────────────▶│                   │
 │                      │                     │                       │                   │
 │                      │                     │  Commit changes       │                   │
 │                      │                     ├──────────────────────▶│                   │
 │                      │                     │                       │                   │
 │                      │                     │  Push branch          │                   │
 │                      │                     ├──────────────────────▶│                   │
 │                      │                     │                       │                   │
 │                      │                     │                       │  Create PR        │
 │                      │                     ├──────────────────────────────────────────▶│
 │                      │                     │                       │                   │
 │                      │                     │◀──────────────────────────────────────────┤
 │                      │                     │   PR URL & Number     │                   │
 │                      │                     │                       │                   │
 │                      │                     │  Update JIRA issue    │                   │
 │                      │                     ├─────────────────┐     │                   │
 │                      │                     │◀────────────────┘     │                   │
 │                      │                     │                       │                   │
 │                      │◀────────────────────┤                       │                   │
 │                      │   PR Created        │                       │                   │
 │                      │                     │                       │                   │
 │◀─────────────────────┤                     │                       │                   │
 │   Success            │                     │                       │                   │
 │                      │                     │                       │                   │
```

## Repository Management Flow

```
1. Add Repository Configuration
   User → UI → API → Validate Access → Encrypt Credentials → Store in DB → Clone Repository

2. Sync Repository
   Scheduler/User → API → Get Repo Config → Decrypt Credentials → Git Pull → Update Status

3. Create Pull Request
   User → Select Issue → Choose Repository → Create Branch → Generate PR → Submit to Platform

4. Monitor PR Status
   Webhook/Scheduler → API → Fetch PR Status → Update Database → Notify User
```

## Database Relationships

```
tenants
   │
   ├──→ repository_configs
   │         │
   │         ├──→ pull_requests
   │         │         │
   │         │         └──→ issue_key (links to JIRA)
   │         │
   │         └──→ repository_activity
   │
   └──→ users
         │
         └──→ created_by (in repository_configs, pull_requests)
```

## Security Flow

```
1. Credential Storage
   User Input → Validate → Encrypt (AES-256-GCM) → Store in Database

2. Credential Usage
   Request → Verify Tenant → Decrypt Credentials → Use for API → Audit Log

3. Access Control
   Request → JWT Token → Extract Tenant/Role → Verify Permissions → Allow/Deny
```