# PR Automation Feature

## Overview

The PR Automation feature enables seamless integration between JIRA issue tracking and code repository management. This feature allows users to:

- Configure multiple Git repositories (GitHub/Bitbucket) per tenant
- Create pull requests directly from JIRA issues
- Track PR status and link them to issues
- Automate the PR creation workflow

## Key Features

### 1. Repository Management
- Add/configure GitHub and Bitbucket repositories
- Secure credential storage with AES-256-GCM encryption
- Repository syncing and validation
- Activity logging for all operations

### 2. Pull Request Creation
- Create PRs directly from JIRA issues
- Automatic branch creation with issue-based naming
- Customizable PR templates
- Link PRs to specific JIRA issues

### 3. Status Tracking
- Real-time PR status synchronization
- Track PR lifecycle (open, merged, closed)
- View all PRs associated with an issue
- Activity history for audit trail

## Architecture

### Components

1. **Repository Service** (`services/repository_service.py`)
   - Manages repository configurations
   - Handles Git operations
   - Encrypts/decrypts credentials

2. **PR Service** (`services/pr_service.py`)
   - Creates and manages pull requests
   - Integrates with GitHub/Bitbucket APIs
   - Generates PR descriptions from templates

3. **API Endpoints**
   - `/tenants/{tid}/repositories` - Repository management
   - `/tenants/{tid}/pull-requests` - PR operations
   - `/tenants/{tid}/issues/{issue_key}/pull-requests` - Issue-specific PRs

### Database Schema

```sql
repository_configs
├── id (UUID)
├── tenant_id (UUID)
├── repo_name
├── repo_type (github/bitbucket)
├── remote_url
├── credentials_encrypted
└── is_active

pull_requests
├── id (UUID)
├── tenant_id (UUID)
├── repo_config_id (UUID)
├── issue_key
├── pr_number
├── pr_url
├── status
└── metadata (JSON)

repository_activity
├── id (UUID)
├── tenant_id (UUID)
├── repo_config_id (UUID)
├── activity_type
├── status
└── details (JSON)
```

## API Usage

### Repository Management

#### Add Repository
```bash
POST /tenants/{tenant_id}/repositories
Content-Type: application/json
Authorization: Bearer {token}

{
  "repo_name": "my-app",
  "repo_type": "github",
  "remote_url": "https://github.com/myorg/my-app.git",
  "token": "ghp_xxxxxxxxxxxx",
  "default_branch": "main"
}
```

#### List Repositories
```bash
GET /tenants/{tenant_id}/repositories
Authorization: Bearer {token}
```

#### Sync Repository
```bash
POST /tenants/{tenant_id}/repositories/{repo_id}/sync
Authorization: Bearer {token}
```

### Pull Request Management

#### Create PR from Issue
```bash
POST /tenants/{tenant_id}/issues/{issue_key}/create-pr
Content-Type: application/json
Authorization: Bearer {token}

{
  "repo_config_id": "uuid-of-repository",
  "title": "Fix: Issue description",
  "target_branch": "main",
  "description_template": "default"
}
```

#### List PRs for Issue
```bash
GET /tenants/{tenant_id}/issues/{issue_key}/pull-requests
Authorization: Bearer {token}
```

#### Sync PR Status
```bash
POST /tenants/{tenant_id}/pull-requests/{pr_id}/sync
Authorization: Bearer {token}
```

## Security Considerations

1. **Credential Encryption**
   - All repository credentials are encrypted using AES-256-GCM
   - Encryption keys are stored in environment variables
   - Never log or expose plaintext credentials

2. **Access Control**
   - Repository management requires admin role
   - PR creation tied to user permissions
   - All operations validate tenant context

3. **Audit Trail**
   - All repository operations are logged
   - Activity tracking for compliance
   - User actions are traceable

## Configuration

### Environment Variables
```bash
# Repository paths
REPO_BASE_PATH=/var/app/repositories
GIT_TIMEOUT_SECONDS=30
PR_TEMPLATE_PATH=/var/app/templates/pr

# Platform limits
GITHUB_RATE_LIMIT=60
BITBUCKET_RATE_LIMIT=100
```

### PR Templates

Create custom PR templates in the configured template directory:

```markdown
## Summary
Fixes issue: [$issue_key]($jira_url/browse/$issue_key)

## Description
$issue_summary

## Changes
- [ ] Implementation details
- [ ] Tests added
- [ ] Documentation updated

## Testing
- [ ] Unit tests pass
- [ ] Integration tests pass
- [ ] Manual testing completed
```

## Best Practices

1. **Repository Setup**
   - Use dedicated service accounts for API access
   - Rotate credentials regularly
   - Test connections before saving

2. **PR Creation**
   - Use descriptive branch names
   - Follow PR template guidelines
   - Link all PRs to JIRA issues

3. **Security**
   - Never commit credentials to repositories
   - Use environment variables for sensitive data
   - Implement proper error handling

## Troubleshooting

### Common Issues

1. **Repository Connection Failed**
   - Verify credentials are correct
   - Check repository URL format
   - Ensure service account has proper permissions

2. **PR Creation Failed**
   - Verify repository is synced
   - Check for existing branches with same name
   - Ensure proper API permissions

3. **Credential Errors**
   - Verify AES key is configured
   - Check credential format
   - Validate encryption/decryption

### Debug Commands

```bash
# Test repository connection
curl -X POST /tenants/{tid}/repositories/{repo_id}/test

# View repository activity
curl -X GET /tenants/{tid}/repositories/{repo_id}/activity

# Check PR status
curl -X GET /tenants/{tid}/pull-requests/{pr_id}
```

## Future Enhancements

1. **Webhook Integration**
   - Real-time PR status updates
   - Automatic JIRA updates

2. **Advanced Templates**
   - Multiple templates per issue type
   - Dynamic template selection

3. **Automation Rules**
   - Auto-merge approved PRs
   - Conditional PR creation

4. **CI/CD Integration**
   - Trigger builds on PR creation
   - Status checks integration

5. **Analytics**
   - PR metrics and reporting
   - Team performance tracking