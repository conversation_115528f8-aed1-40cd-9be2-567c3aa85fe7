def generate_extended_prompt():
    """
    Accepts a user input as a prompt and returns an extended prompt 
    which includes a predefined text block with the user's prompt inserted.
    """

    predefined_text = """We are in the process of building the following application: 
    1. A chat application used to create an end to end application. 
    2. First Step: Create a high level requirements document from the users one line description. 
    3. Second Step: Provide UI Images for the application and get user approval. 
    4. Third Step: Create all the workflows required for the application - and get user approval. 
    
Requirements is as follows: 
{user_prompt_placeholder}

Please provide the code after thinking deeply and following good software development practices and architecture.
"""

    user_prompt = input("Enter your prompt: ")

    # Insert the user's prompt into the predefined text
    # We'll split the predefined text at the "Requirements is as follows:" line 
    # and insert the user prompt there.
    
    parts = predefined_text.split("Requirements is as follows: \\n", 1)
    if len(parts) == 2:
        extended_prompt = parts[0] + "Requirements is as follows: \\n" + user_prompt + "\\n" + parts[1].split("{user_prompt_placeholder}\\n",1)[1]
    else:
        # Fallback in case the split string is not found (should not happen with the current template)
        extended_prompt = predefined_text.replace("{user_prompt_placeholder}", user_prompt)


    return extended_prompt

if __name__ == "__main__":
    extended_prompt = generate_extended_prompt()
    print("\\n--- Extended Prompt ---")
    print(extended_prompt) 