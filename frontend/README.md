# JIRA Bug Browser - Frontend

This is the React frontend for the JIRA Bug Browser application.

## Overview

The JIRA Bug Browser frontend provides a modern, user-friendly interface for browsing and managing JIRA issues. It connects to the Flask backend API and includes features such as authentication, project and issue browsing, and JIRA integration settings.

## Features

- **Multi-tenant Authentication**: Secure JWT-based authentication with role-based access control
- **JIRA Integration**: Connect to your JIRA instance and manage your credentials
- **Project Browser**: Browse JIRA projects and their issues
- **Issue View**: View detailed information about JIRA issues
- **Responsive Design**: Modern UI built with Material-UI that works on all devices
- **Theme Customization**: Kasavu-inspired theme with customizable styling

## Key Components

- **Authentication Flow**: Complete login/logout functionality with JWT token handling
- **JIRA Settings Page**: UI for managing JIRA credentials securely
- **Dashboard**: Main interface for viewing projects and issues
- **Project Navigation**: Sidebar for navigating between projects
- **Issue Workspace**: Main view for working with issues

## JIRA Integration Settings

The application includes a dedicated JIRA Settings page that allows users to:

1. **View Current Configuration**: See the current JIRA base URL and configuration status
2. **Add New Credentials**: Connect to a JIRA instance by providing base URL, email, and API token
3. **Update Credentials**: Update existing JIRA credentials
4. **Delete Credentials**: Remove JIRA integration

### Authentication Requirements

For Atlassian Cloud JIRA instances, the following credentials are required:

1. **Base URL**: Your JIRA instance URL (e.g., `https://your-domain.atlassian.net`)
2. **Email Address**: The email associated with your Atlassian account
3. **API Token**: A token generated from your Atlassian account settings

All three components are required for successful JIRA API authentication. The email address is used with the API token for HTTP Basic Authentication.

The JIRA Settings page is accessible through:

- The user menu in the top-right corner
- A direct link from the dashboard

## Getting Started

### Prerequisites

- Node.js 16+
- npm or yarn
- Running backend API (see backend README for setup)

### Installation

1. Clone the repository and navigate to the frontend directory:
```bash
cd frontend
```

2. Install dependencies:
```bash
npm install
# or
yarn install
```

3. Set up environment variables (create a .env file):
```
REACT_APP_API_BASE_URL=http://localhost:5045
```

4. Start the development server:
```bash
npm start
# or
yarn start
```

The app will be available at http://localhost:3000.

## Testing

The frontend includes a suite of tests for components, services, and integration.

Run tests with:
```bash
npm test
# or
yarn test
```

## Building for Production

To create a production build:
```bash
npm run build
# or
yarn build
```

The build artifacts will be stored in the `build/` directory.

## Future Improvements

- Add advanced filtering for issues
- Implement real-time updates using websockets
- Add theme customization options
- Enhance accessibility features
- Implement data visualization for JIRA metrics
- Add offline support with service workers