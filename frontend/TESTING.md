# JIRA Bug Browser - Frontend Testing Guide

This document outlines the testing strategy for the JIRA Bug Browser frontend application.

## Test Suite

The frontend application uses Jest and React Testing Library for testing. The test suite includes:

1. **Unit Tests**: Test individual components and functions in isolation
2. **Integration Tests**: Test component interactions and authentication flows
3. **Accessibility Tests**: Ensure UI meets WCAG standards

## Running Tests

```bash
# Run all tests
npm test

# Run with coverage report
npm run test:coverage
```

## Test Structure

- `__tests__/`: Root test directory
  - `App.test.js`: Tests for the main App component
  - `LoginPage.test.js`: Tests for the login page
  - `MainLayout.test.js`: Tests for the main application layout
  - `DashboardPage.test.js`: Tests for the dashboard page
  - `api.test.js`: Tests for API service functions
  - `theme.test.js`: Tests for theme configuration
  - `integration/`: Contains integration tests
    - `AuthFlow.test.js`: Tests for authentication flow
  - `accessibility/`: Contains accessibility tests
    - `AccessibilityTests.test.js`: Tests for WCAG compliance
  - `mocks/`: Mock implementations for testing
    - `handlers.js`: MSW request handlers
    - `server.js`: MSW server setup

## Mock Service Worker

The tests use [Mock Service Worker (MSW)](https://mswjs.io/) to intercept and mock API requests. This allows testing components that make API calls without actually hitting a backend server.

The mock API handlers are defined in `__tests__/mocks/handlers.js`.

## Testing Authentication

Authentication testing involves:

1. Testing login form validation
2. Testing successful login flow
3. Testing token refresh mechanism
4. Testing logout functionality
5. Testing protected routes

## Testing JIRA Integration

JIRA integration testing includes:

1. Testing JIRA credentials form validation (base URL, email, and API token)
2. Testing successful JIRA credentials submission
3. Testing JIRA settings page error handling
4. Testing project and issue data loading
5. Testing credential deletion
6. Mocking JIRA API responses for consistent testing

## Accessibility Testing

Accessibility tests use `jest-axe` to check for WCAG compliance. These tests ensure:

1. Proper semantic HTML
2. ARIA attributes where needed
3. Sufficient color contrast
4. Keyboard navigation
5. Screen reader compatibility

## Coverage Thresholds

The test suite enforces minimum coverage thresholds:

- 80% statement coverage
- 80% branch coverage
- 80% function coverage
- 80% line coverage

## Adding New Tests

When adding new components or features:

1. Create unit tests for all new components
2. Add integration tests for component interactions
3. Include accessibility tests for UI components
4. Update mock handlers if new API endpoints are used

## Best Practices

1. Test component behavior, not implementation details
2. Use screen queries that resemble how users interact with the app
3. Prefer `getByRole` and `getByLabelText` over other queries
4. Mock minimal external dependencies
5. Write tests that are resilient to design changes