{"version": 3, "file": "static/js/156.473f88b6.chunk.js", "mappings": "+UAAA,IAAIA,GAAG,EAAE,MAAMC,EAAEA,IAAIC,iBAAiB,YAAYC,IAAIA,EAAEC,YAAYJ,EAAEG,EAAEE,UAAUJ,EAAEE,GAAG,IAAG,EAAG,EAAEA,EAAEA,CAACH,EAAEC,EAAEE,EAAEG,KAAK,IAAIC,EAAEC,EAAE,OAAOC,IAAIR,EAAES,OAAO,IAAID,GAAGH,KAAKE,EAAEP,EAAES,OAAQ,OAADH,QAAC,IAADA,EAAAA,EAAG,IAAIC,QAAG,IAASD,KAAKA,EAAEN,EAAES,MAAMT,EAAEU,MAAMH,EAAEP,EAAEW,OAAO,EAAEZ,EAAEC,IAAID,EAAEC,EAAE,GAAG,OAAOD,EAAEC,EAAE,GAAG,oBAAoB,OAAjD,CAAyDA,EAAES,MAAMP,GAAGH,EAAEC,IAAI,CAAC,EAAEK,EAAEN,IAAIa,uBAAuB,IAAIA,uBAAuB,IAAIb,OAAO,EAAEO,EAAEA,KAAK,MAAMP,EAAEc,YAAYC,iBAAiB,cAAc,GAAG,GAAGf,GAAGA,EAAEgB,cAAc,GAAGhB,EAAEgB,cAAcF,YAAYG,MAAM,OAAOjB,CAAC,EAAEQ,EAAEA,KAAI,IAAAU,EAAC,MAAMlB,EAAEO,IAAI,OAAyB,QAAzBW,EAAQ,OAADlB,QAAC,IAADA,OAAC,EAADA,EAAGmB,uBAAe,IAAAD,EAAAA,EAAE,CAAC,EAAET,EAAE,SAACR,GAAS,IAAPE,EAACiB,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,IAAE,EAAK,MAAMd,EAAEC,IAAI,IAAIE,EAAE,WAA8J,OAAnJT,GAAG,EAAES,EAAE,qBAAqBH,IAAIiB,SAASC,cAAchB,IAAI,EAAEC,EAAE,YAAYc,SAASE,aAAahB,EAAE,UAAUH,EAAEoB,OAAOjB,EAAEH,EAAEoB,KAAKC,QAAQ,KAAK,OAAa,CAACC,KAAK3B,EAAES,MAAMP,EAAES,OAAO,OAAOD,MAAM,EAAEkB,QAAQ,GAAGC,GAAE,MAAAC,OAAOC,KAAKf,MAAK,KAAAc,OAAIE,KAAKC,MAAM,cAAcD,KAAKE,UAAU,MAAOC,eAAe3B,EAAE,EAAE4B,EAAE,IAAIC,QAAQ,SAASC,EAAEvC,EAAEC,GAAG,OAAOoC,EAAEG,IAAIxC,IAAIqC,EAAEI,IAAIzC,EAAE,IAAIC,GAAGoC,EAAEG,IAAIxC,EAAE,CAAC,MAAM0C,EAACC,WAAAA,IAAAC,EAAAA,EAAAA,GAAA,kBAAAA,EAAAA,EAAAA,GAAA,SAAK,IAACA,EAAAA,EAAAA,GAAA,SAAG,GAAE,CAACC,CAAAA,CAAE7C,GAAE,IAAA8C,EAAC,GAAG9C,EAAE+C,eAAe,OAAO,MAAM9C,EAAE+C,KAAKzC,EAAE,GAAGJ,EAAE6C,KAAKzC,EAAE0C,IAAI,GAAGD,KAAK1C,GAAGL,GAAGE,GAAGH,EAAEkD,UAAU/C,EAAE+C,UAAU,KAAKlD,EAAEkD,UAAUjD,EAAEiD,UAAU,KAAKF,KAAK1C,GAAGN,EAAEU,MAAMsC,KAAKzC,EAAE4C,KAAKnD,KAAKgD,KAAK1C,EAAEN,EAAEU,MAAMsC,KAAKzC,EAAE,CAACP,IAAU,QAAP8C,EAACE,KAAK/C,SAAC,IAAA6C,GAANA,EAAAM,KAAAJ,KAAShD,EAAE,EAAE,MAAM6C,EAAE,SAAC7C,EAAEC,GAAS,IAAPE,EAACiB,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAC,CAAC,EAAK,IAAI,GAAGiC,oBAAoBC,oBAAoBC,SAASvD,GAAG,CAAC,MAAMM,EAAE,IAAI+C,qBAAqBrD,IAAIwD,QAAQC,UAAUC,MAAM,KAAKzD,EAAED,EAAE2D,aAAa,GAAG,IAAI,OAAOrD,EAAEsD,SAAOC,EAAAA,EAAAA,GAAC,CAACnC,KAAK1B,EAAE8D,UAAS,GAAM3D,IAAIG,CAAC,CAAC,CAAC,MAAAyD,GAAM,CAAC,EAAEC,EAAEhE,IAAI,IAAIC,GAAE,EAAG,MAAM,KAAKA,IAAID,IAAIC,GAAE,EAAG,CAAC,EAAE,IAAIgE,GAAG,EAAE,MAAMC,EAAEA,IAAI,WAAW3C,SAAS4C,iBAAiB5C,SAASC,aAAa,IAAI,EAAE4C,EAAEpE,IAAI,WAAWuB,SAAS4C,iBAAiBF,GAAG,IAAIA,EAAE,qBAAqBjE,EAAE0B,KAAK1B,EAAEK,UAAU,EAAEgE,IAAI,EAAEC,EAAEA,KAAKpE,iBAAiB,mBAAmBkE,GAAE,GAAIlE,iBAAiB,qBAAqBkE,GAAE,EAAG,EAAEC,EAAEA,KAAKE,oBAAoB,mBAAmBH,GAAE,GAAIG,oBAAoB,qBAAqBH,GAAE,EAAG,EAAEI,EAAEA,KAAK,GAAGP,EAAE,EAAE,KAAAQ,EAAC,MAAMzE,EAAEQ,IAAIL,EAAEoB,SAASC,cAAgI,QAA7GiD,EAACC,WAAW5D,YAAYC,iBAAiB,oBAAoB4D,QAAQ1E,GAAG,WAAWA,EAAE2B,MAAM3B,EAAEiD,UAAUlD,IAAI,UAAE,IAAAyE,OAAnH,EAAOA,EAA8GvB,UAAUe,EAAG,OAAD9D,QAAC,IAADA,EAAAA,EAAG+D,IAAII,IAAIrE,GAAG,KAAK2E,YAAY,KAAKX,EAAEC,IAAII,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,mBAAIO,GAAkB,OAAOZ,CAAC,EAAE,EAAEa,EAAE9E,IAAIuB,SAASC,aAAatB,iBAAiB,sBAAsB,IAAIF,MAAK,GAAIA,GAAG,EAAE+E,EAAE,CAAC,KAAK,KAAKC,EAAE,SAAChF,GAAS,IAAPO,EAACa,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAC,CAAC,EAAK0D,GAAG,KAAK,MAAMzC,EAAEmC,IAAI,IAAIjC,EAAEG,EAAEjC,EAAE,OAAO,MAAMuD,EAAEnB,EAAE,SAAS7C,IAAI,IAAI,MAAMC,KAAKD,EAAE,2BAA2BC,EAAE2B,OAAOoC,EAAEiB,aAAahF,EAAEiD,UAAUb,EAAEwC,kBAAkBnC,EAAEhC,MAAMuB,KAAKiD,IAAIjF,EAAEiD,UAAU1C,IAAI,GAAGkC,EAAEb,QAAQsB,KAAKlD,GAAGsC,GAAE,IAAK,IAAIyB,IAAIzB,EAAEpC,EAAEH,EAAE0C,EAAEqC,EAAExE,EAAE4E,kBAAkBlF,GAAGA,IAAIyC,EAAEjC,EAAE,OAAO8B,EAAEpC,EAAEH,EAAE0C,EAAEqC,EAAExE,EAAE4E,kBAAkB7E,GAAG,KAAKoC,EAAEhC,MAAMI,YAAYG,MAAMhB,EAAEI,UAAUkC,GAAE,EAAG,GAAG,IAAI,GAAG,EAAE6C,EAAE,CAAC,GAAG,KAAKC,EAAE,SAACrF,GAAS,IAAPO,EAACa,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAC,CAAC,EAAK4D,EAAEhB,GAAG,KAAK,IAAIxD,EAAE6B,EAAE5B,EAAE,MAAM,GAAG,MAAMuD,EAAEzB,EAAEhC,EAAEmC,GAAGuB,EAAEjE,IAAI,IAAI,MAAMC,KAAKD,EAAEgE,EAAEnB,EAAE5C,GAAG+D,EAAE1D,EAAE+B,EAAE3B,QAAQ2B,EAAE3B,MAAMsD,EAAE1D,EAAE+B,EAAER,QAAQmC,EAAEzD,EAAEC,IAAI,EAAE0D,EAAErB,EAAE,eAAeoB,GAAGC,IAAI1D,EAAEL,EAAEH,EAAEqC,EAAE+C,EAAE7E,EAAE4E,kBAAkB5D,SAASrB,iBAAiB,oBAAoB,KAAK,WAAWqB,SAAS4C,kBAAkBF,EAAEC,EAAEoB,eAAe9E,GAAE,GAAI,IAAIP,GAAG,KAAK+D,EAAE1D,EAAE,EAAE+B,EAAE5B,EAAE,MAAM,GAAGD,EAAEL,EAAEH,EAAEqC,EAAE+C,EAAE7E,EAAE4E,kBAAkB7E,GAAG,IAAIE,KAAK,IAAIoE,WAAWpE,GAAG,IAAI,EAAE,IAAI+E,EAAE,EAAEC,EAAE,IAAIC,EAAE,EAAE,MAAMC,EAAE1F,IAAI,IAAI,MAAMC,KAAKD,EAAEC,EAAE0F,gBAAgBH,EAAEvD,KAAK2D,IAAIJ,EAAEvF,EAAE0F,eAAeF,EAAExD,KAAKiD,IAAIO,EAAExF,EAAE0F,eAAeJ,EAAEE,GAAGA,EAAED,GAAG,EAAE,EAAE,EAAE,EAAE,IAAIK,EAAE,MAAMC,EAAEA,KAAA,IAAAC,EAAA,OAAIF,EAAEN,EAA8B,QAA7BQ,EAACjF,YAAYkF,wBAAgB,IAAAD,EAAAA,EAAE,CAAC,EAA8G,IAAIE,EAAE,EAAE,MAAMC,EAACvD,WAAAA,IAAAC,EAAAA,EAAAA,GAAA,SAAG,KAAEA,EAAAA,EAAAA,GAAA,SAAG,IAAIuD,MAAGvD,EAAAA,EAAAA,GAAA,kBAAAA,EAAAA,EAAAA,GAAA,iBAAK4B,CAAAA,GAAIyB,EAAEH,IAAI9C,KAAKiB,EAAE5C,OAAO,EAAE2B,KAAKkB,EAAEkC,OAAO,CAACpB,CAAAA,GAAI,MAAMhF,EAAEiC,KAAK2D,IAAI5C,KAAKiB,EAAE5C,OAAO,EAAEY,KAAKC,OAAO4D,IAAIG,GAAG,KAAK,OAAOjD,KAAKiB,EAAEjE,EAAE,CAAC6C,CAAAA,CAAE7C,GAAE,IAAAqG,EAAC,GAAS,QAANA,EAAArD,KAAKoB,SAAC,IAAAiC,GAANA,EAAAjD,KAAAJ,KAAShD,IAAIA,EAAE2F,eAAe,gBAAgB3F,EAAEsG,UAAU,OAAO,MAAMrG,EAAE+C,KAAKiB,EAAEhB,IAAI,GAAG,IAAI9C,EAAE6C,KAAKkB,EAAE1B,IAAIxC,EAAE2F,eAAe,GAAGxF,GAAG6C,KAAKiB,EAAE5C,OAAO,IAAIrB,EAAEuG,SAAStG,EAAEmF,EAAE,KAAAoB,EAAC,GAAGrG,EAAEH,EAAEuG,SAASpG,EAAEiF,GAAGjF,EAAE0B,QAAQ,CAAC7B,GAAGG,EAAEiF,EAAEpF,EAAEuG,UAAUvG,EAAEuG,WAAWpG,EAAEiF,GAAGpF,EAAEkD,YAAY/C,EAAE0B,QAAQ,GAAGqB,WAAW/C,EAAE0B,QAAQsB,KAAKnD,IAAIG,EAAE,CAAC2B,GAAG9B,EAAE2F,cAAc9D,QAAQ,CAAC7B,GAAGoF,EAAEpF,EAAEuG,UAAUvD,KAAKkB,EAAEzB,IAAItC,EAAE2B,GAAG3B,GAAG6C,KAAKiB,EAAEd,KAAKhD,IAAI6C,KAAKiB,EAAEwC,MAAM,CAACzG,EAAEC,IAAIA,EAAEmF,EAAEpF,EAAEoF,IAAIpC,KAAKiB,EAAE5C,OAAO,GAAG,CAAC,MAAMrB,EAAEgD,KAAKiB,EAAEyC,OAAO,IAAI,IAAI,MAAMzG,KAAKD,EAAEgD,KAAKkB,EAAEyC,OAAO1G,EAAE6B,GAAG,CAAO,QAAN0E,EAAAxD,KAAKqB,SAAC,IAAAmC,GAANA,EAAApD,KAAAJ,KAAS7C,EAAE,CAAC,EAAE,MAAMyG,EAAE5G,IAAI,MAAMC,EAAEyE,WAAWmC,qBAAqBjC,WAAW,WAAWrD,SAAS4C,gBAAgBnE,KAAKC,EAAED,EAAEgE,EAAEhE,IAAIuB,SAASrB,iBAAiB,mBAAmBF,EAAE,CAAC8G,MAAK,IAAK,EAAEC,EAAE,CAAC,IAAI,KAAKC,EAAE,SAAChH,GAAS,IAAPM,EAACc,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAC,CAAC,EAAKsD,WAAWuC,wBAAwB,kBAAkBA,uBAAuBC,WAAWpC,GAAG,KAAI,IAAAqC,EAA5hC,qBAAqBrG,aAAa+E,IAAIA,EAAEhD,EAAE,QAAQ6C,EAAE,CAAChE,KAAK,QAAQoC,UAAS,EAAGsD,kBAAkB,KAAi8B,IAAI7G,EAAEC,EAAEC,EAAE,OAAO,MAAM4B,EAAEE,EAAEjC,EAAE4F,GAAGxD,EAAE1C,IAAI4G,GAAG,KAAK,IAAI,MAAM3G,KAAKD,EAAEqC,EAAEQ,EAAE5C,GAAG,MAAMA,EAAEoC,EAAE2C,IAAI/E,GAAGA,EAAEmF,IAAI5E,EAAEE,QAAQF,EAAEE,MAAMT,EAAEmF,EAAE5E,EAAEqB,QAAQ5B,EAAE4B,QAAQtB,IAAI,GAAG,EAAEyD,EAAEnB,EAAE,QAAQH,EAAE,CAAC0E,kBAAqC,QAApBD,EAAC7G,EAAE8G,yBAAiB,IAAAD,EAAAA,EAAE,KAAK5G,EAAEJ,EAAEH,EAAEQ,EAAEuG,EAAEzG,EAAE6E,kBAAkBnB,IAAIA,EAAEJ,QAAQ,CAAClC,KAAK,cAAcoC,UAAS,IAAKvC,SAASrB,iBAAiB,oBAAoB,KAAK,WAAWqB,SAAS4C,kBAAkBzB,EAAEsB,EAAEsB,eAAe/E,GAAE,GAAI,IAAIN,GAAG,KAAKoC,EAAEmC,IAAIhE,EAAEC,EAAE,OAAOF,EAAEJ,EAAEH,EAAEQ,EAAEuG,EAAEzG,EAAE6E,iBAAiB,IAAI,GAAG,EAAE,MAAMkC,EAAC1E,WAAAA,IAAAC,EAAAA,EAAAA,GAAA,iBAAGC,CAAAA,CAAE7C,GAAE,IAAAsH,EAAO,QAANA,EAAAtE,KAAKoB,SAAC,IAAAkD,GAANA,EAAAlE,KAAAJ,KAAShD,EAAE,EAAE,MAAMuH,EAAE,CAAC,KAAK,KAAKC,EAAE,SAACxH,GAAS,IAAPO,EAACa,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAC,CAAC,EAAK0D,GAAG,KAAK,MAAMzC,EAAEmC,IAAI,IAAI9B,EAAEuB,EAAExD,EAAE,OAAO,MAAMyD,EAAE3B,EAAEhC,EAAE8G,GAAGjD,EAAEpE,IAAIO,EAAE4E,mBAAmBnF,EAAEA,EAAEyH,OAAO,IAAI,IAAI,MAAMxH,KAAKD,EAAEkE,EAAErB,EAAE5C,GAAGA,EAAEiD,UAAUb,EAAEwC,kBAAkBZ,EAAEvD,MAAMuB,KAAKiD,IAAIjF,EAAEiD,UAAU1C,IAAI,GAAGyD,EAAEpC,QAAQ,CAAC5B,GAAGyC,IAAI,EAAE4B,EAAEzB,EAAE,2BAA2BuB,GAAG,GAAGE,EAAE,CAAC5B,EAAEvC,EAAEH,EAAEiE,EAAEsD,EAAEhH,EAAE4E,kBAAkB,MAAM3E,EAAEwD,GAAG,KAAKI,EAAEE,EAAEgB,eAAehB,EAAEW,aAAavC,GAAE,EAAG,IAAI,IAAI,MAAM1C,IAAI,CAAC,UAAU,QAAQ,oBAAoBE,iBAAiBF,GAAG,IAAI4G,EAAEpG,IAAI,CAACkH,SAAQ,EAAGZ,MAAK,IAAK7G,GAAGA,IAAIgE,EAAExD,EAAE,OAAOiC,EAAEvC,EAAEH,EAAEiE,EAAEsD,EAAEhH,EAAE4E,kBAAkB7E,GAAG,KAAK2D,EAAEvD,MAAMI,YAAYG,MAAMhB,EAAEI,UAAUqC,GAAE,EAAG,GAAG,GAAG,IAAI,EAAEiF,EAAE,CAAC,IAAI,MAAMC,EAAE5H,IAAIuB,SAASC,aAAasD,GAAG,IAAI8C,EAAE5H,KAAK,aAAauB,SAASsG,WAAW3H,iBAAiB,QAAQ,IAAI0H,EAAE5H,KAAI,GAAI4E,WAAW5E,EAAE,EAAE8H,EAAE,SAAC9H,GAAS,IAAPM,EAACc,UAAAC,OAAA,QAAAC,IAAAF,UAAA,GAAAA,UAAA,GAAC,CAAC,EAASiB,EAAE5B,EAAE,QAAQ8B,EAAEpC,EAAEH,EAAEqC,EAAEsF,EAAErH,EAAE6E,kBAAkByC,GAAG,KAAK,MAAMlF,EAAEnC,IAAImC,IAAIL,EAAE3B,MAAMuB,KAAKiD,IAAIxC,EAAE1B,cAAcR,IAAI,GAAG6B,EAAER,QAAQ,CAACa,GAAGH,GAAE,GAAItC,GAAG,KAAKoC,EAAE5B,EAAE,OAAO,GAAG8B,EAAEpC,EAAEH,EAAEqC,EAAEsF,EAAErH,EAAE6E,kBAAkB5C,GAAE,EAAG,IAAI,GAAG,C", "sources": ["../../node_modules/web-vitals/dist/web-vitals.js"], "sourcesContent": ["let e=-1;const t=t=>{addEventListener(\"pageshow\",(n=>{n.persisted&&(e=n.timeStamp,t(n))}),!0)},n=(e,t,n,i)=>{let o,s;return r=>{t.value>=0&&(r||i)&&(s=t.value-(o??0),(s||void 0===o)&&(o=t.value,t.delta=s,t.rating=((e,t)=>e>t[1]?\"poor\":e>t[0]?\"needs-improvement\":\"good\")(t.value,n),e(t)))}},i=e=>{requestAnimationFrame((()=>requestAnimationFrame((()=>e()))))},o=()=>{const e=performance.getEntriesByType(\"navigation\")[0];if(e&&e.responseStart>0&&e.responseStart<performance.now())return e},s=()=>{const e=o();return e?.activationStart??0},r=(t,n=-1)=>{const i=o();let r=\"navigate\";e>=0?r=\"back-forward-cache\":i&&(document.prerendering||s()>0?r=\"prerender\":document.wasDiscarded?r=\"restore\":i.type&&(r=i.type.replace(/_/g,\"-\")));return{name:t,value:n,rating:\"good\",delta:0,entries:[],id:`v5-${Date.now()}-${Math.floor(8999999999999*Math.random())+1e12}`,navigationType:r}},c=new WeakMap;function a(e,t){return c.get(e)||c.set(e,new t),c.get(e)}class d{t;i=0;o=[];h(e){if(e.hadRecentInput)return;const t=this.o[0],n=this.o.at(-1);this.i&&t&&n&&e.startTime-n.startTime<1e3&&e.startTime-t.startTime<5e3?(this.i+=e.value,this.o.push(e)):(this.i=e.value,this.o=[e]),this.t?.(e)}}const h=(e,t,n={})=>{try{if(PerformanceObserver.supportedEntryTypes.includes(e)){const i=new PerformanceObserver((e=>{Promise.resolve().then((()=>{t(e.getEntries())}))}));return i.observe({type:e,buffered:!0,...n}),i}}catch{}},f=e=>{let t=!1;return()=>{t||(e(),t=!0)}};let u=-1;const l=()=>\"hidden\"!==document.visibilityState||document.prerendering?1/0:0,m=e=>{\"hidden\"===document.visibilityState&&u>-1&&(u=\"visibilitychange\"===e.type?e.timeStamp:0,p())},g=()=>{addEventListener(\"visibilitychange\",m,!0),addEventListener(\"prerenderingchange\",m,!0)},p=()=>{removeEventListener(\"visibilitychange\",m,!0),removeEventListener(\"prerenderingchange\",m,!0)},v=()=>{if(u<0){const e=s(),n=document.prerendering?void 0:globalThis.performance.getEntriesByType(\"visibility-state\").filter((t=>\"hidden\"===t.name&&t.startTime>e))[0]?.startTime;u=n??l(),g(),t((()=>{setTimeout((()=>{u=l(),g()}))}))}return{get firstHiddenTime(){return u}}},y=e=>{document.prerendering?addEventListener(\"prerenderingchange\",(()=>e()),!0):e()},b=[1800,3e3],P=(e,o={})=>{y((()=>{const c=v();let a,d=r(\"FCP\");const f=h(\"paint\",(e=>{for(const t of e)\"first-contentful-paint\"===t.name&&(f.disconnect(),t.startTime<c.firstHiddenTime&&(d.value=Math.max(t.startTime-s(),0),d.entries.push(t),a(!0)))}));f&&(a=n(e,d,b,o.reportAllChanges),t((t=>{d=r(\"FCP\"),a=n(e,d,b,o.reportAllChanges),i((()=>{d.value=performance.now()-t.timeStamp,a(!0)}))})))}))},T=[.1,.25],E=(e,o={})=>{P(f((()=>{let s,c=r(\"CLS\",0);const f=a(o,d),u=e=>{for(const t of e)f.h(t);f.i>c.value&&(c.value=f.i,c.entries=f.o,s())},l=h(\"layout-shift\",u);l&&(s=n(e,c,T,o.reportAllChanges),document.addEventListener(\"visibilitychange\",(()=>{\"hidden\"===document.visibilityState&&(u(l.takeRecords()),s(!0))})),t((()=>{f.i=0,c=r(\"CLS\",0),s=n(e,c,T,o.reportAllChanges),i((()=>s()))})),setTimeout(s))})))};let _=0,L=1/0,M=0;const C=e=>{for(const t of e)t.interactionId&&(L=Math.min(L,t.interactionId),M=Math.max(M,t.interactionId),_=M?(M-L)/7+1:0)};let I;const w=()=>I?_:performance.interactionCount??0,F=()=>{\"interactionCount\"in performance||I||(I=h(\"event\",C,{type:\"event\",buffered:!0,durationThreshold:0}))};let k=0;class A{u=[];l=new Map;m;p;v(){k=w(),this.u.length=0,this.l.clear()}P(){const e=Math.min(this.u.length-1,Math.floor((w()-k)/50));return this.u[e]}h(e){if(this.m?.(e),!e.interactionId&&\"first-input\"!==e.entryType)return;const t=this.u.at(-1);let n=this.l.get(e.interactionId);if(n||this.u.length<10||e.duration>t.T){if(n?e.duration>n.T?(n.entries=[e],n.T=e.duration):e.duration===n.T&&e.startTime===n.entries[0].startTime&&n.entries.push(e):(n={id:e.interactionId,entries:[e],T:e.duration},this.l.set(n.id,n),this.u.push(n)),this.u.sort(((e,t)=>t.T-e.T)),this.u.length>10){const e=this.u.splice(10);for(const t of e)this.l.delete(t.id)}this.p?.(n)}}}const B=e=>{const t=globalThis.requestIdleCallback||setTimeout;\"hidden\"===document.visibilityState?e():(t(e=f(e)),document.addEventListener(\"visibilitychange\",e,{once:!0}))},N=[200,500],S=(e,i={})=>{globalThis.PerformanceEventTiming&&\"interactionId\"in PerformanceEventTiming.prototype&&y((()=>{F();let o,s=r(\"INP\");const c=a(i,A),d=e=>{B((()=>{for(const t of e)c.h(t);const t=c.P();t&&t.T!==s.value&&(s.value=t.T,s.entries=t.entries,o())}))},f=h(\"event\",d,{durationThreshold:i.durationThreshold??40});o=n(e,s,N,i.reportAllChanges),f&&(f.observe({type:\"first-input\",buffered:!0}),document.addEventListener(\"visibilitychange\",(()=>{\"hidden\"===document.visibilityState&&(d(f.takeRecords()),o(!0))})),t((()=>{c.v(),s=r(\"INP\"),o=n(e,s,N,i.reportAllChanges)})))}))};class q{m;h(e){this.m?.(e)}}const x=[2500,4e3],O=(e,o={})=>{y((()=>{const c=v();let d,u=r(\"LCP\");const l=a(o,q),m=e=>{o.reportAllChanges||(e=e.slice(-1));for(const t of e)l.h(t),t.startTime<c.firstHiddenTime&&(u.value=Math.max(t.startTime-s(),0),u.entries=[t],d())},g=h(\"largest-contentful-paint\",m);if(g){d=n(e,u,x,o.reportAllChanges);const s=f((()=>{m(g.takeRecords()),g.disconnect(),d(!0)}));for(const e of[\"keydown\",\"click\",\"visibilitychange\"])addEventListener(e,(()=>B(s)),{capture:!0,once:!0});t((t=>{u=r(\"LCP\"),d=n(e,u,x,o.reportAllChanges),i((()=>{u.value=performance.now()-t.timeStamp,d(!0)}))}))}}))},$=[800,1800],D=e=>{document.prerendering?y((()=>D(e))):\"complete\"!==document.readyState?addEventListener(\"load\",(()=>D(e)),!0):setTimeout(e)},H=(e,i={})=>{let c=r(\"TTFB\"),a=n(e,c,$,i.reportAllChanges);D((()=>{const d=o();d&&(c.value=Math.max(d.responseStart-s(),0),c.entries=[d],a(!0),t((()=>{c=r(\"TTFB\",0),a=n(e,c,$,i.reportAllChanges),a(!0)})))}))};export{T as CLSThresholds,b as FCPThresholds,N as INPThresholds,x as LCPThresholds,$ as TTFBThresholds,E as onCLS,P as onFCP,S as onINP,O as onLCP,H as onTTFB};\n"], "names": ["e", "t", "addEventListener", "n", "persisted", "timeStamp", "i", "o", "s", "r", "value", "delta", "rating", "requestAnimationFrame", "performance", "getEntriesByType", "responseStart", "now", "_e$activationStart", "activationStart", "arguments", "length", "undefined", "document", "prerendering", "wasDiscarded", "type", "replace", "name", "entries", "id", "concat", "Date", "Math", "floor", "random", "navigationType", "c", "WeakMap", "a", "get", "set", "d", "constructor", "_defineProperty", "h", "_this$t", "hadRecentInput", "this", "at", "startTime", "push", "call", "PerformanceObserver", "supportedEntryTypes", "includes", "Promise", "resolve", "then", "getEntries", "observe", "_objectSpread", "buffered", "_unused", "f", "u", "l", "visibilityState", "m", "p", "g", "removeEventListener", "v", "_globalThis$performan", "globalThis", "filter", "setTimeout", "firstHiddenTime", "y", "b", "P", "disconnect", "max", "reportAllChanges", "T", "E", "takeRecords", "_", "L", "M", "C", "interactionId", "min", "I", "w", "_performance$interact", "interactionCount", "k", "A", "Map", "clear", "_this$m", "entryType", "duration", "_this$p", "sort", "splice", "delete", "B", "requestIdleCallback", "once", "N", "S", "PerformanceEventTiming", "prototype", "_i$durationThreshold", "durationThreshold", "q", "_this$m2", "x", "O", "slice", "capture", "$", "D", "readyState", "H"], "sourceRoot": ""}