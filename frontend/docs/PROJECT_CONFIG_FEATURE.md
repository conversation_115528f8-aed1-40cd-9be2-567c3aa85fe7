# Project Configuration Feature

## Overview

This feature allows users to associate local repository paths with their JIRA projects. When hovering over a project card in the Projects view, a settings icon appears that allows users to configure the local path for that project.

## User Interface

### Project Cards
- When hovering over any project card, a settings icon (⚙️) appears on the right side
- Clicking the settings icon opens the Project Configuration dialog

### Project Configuration Dialog
- Shows the project name and key at the top
- Provides a text field to enter the local repository path
- Has three action buttons:
  - **Remove Path** (shown only if a path is already set) - Deletes the current path
  - **Cancel** - Closes the dialog without saving
  - **Save** - Saves the entered path

## Features

### Path Management
- Users can set a local filesystem path for each project
- The path is stored per user and per tenant
- Paths can be updated or removed at any time
- The system validates that a path is entered before enabling the Save button

### Error Handling
- Shows appropriate error messages if saving or loading fails
- Provides success feedback when operations complete

## API Endpoints

The feature uses the following API endpoints:

1. **GET** `/tenants/{tenant_id}/user-project-paths/{project_key}`
   - Retrieves the current path for a project

2. **POST** `/tenants/{tenant_id}/user-project-paths`
   - Creates or updates a project path
   - Body: `{ project_key, local_path }`

3. **DELETE** `/tenants/{tenant_id}/user-project-paths/{project_key}`
   - Removes a project path

## Implementation Details

### Components
- **ProjectsPage.js** - Updated to show settings icon on hover
- **ProjectConfigDialog.js** - New dialog component for path configuration

### State Management
- Hover state tracked in ProjectsPage
- Dialog open/close state managed locally
- Path data loaded fresh each time dialog opens

### Security
- All operations are scoped to the current tenant
- Users can only manage their own project paths
- Authentication required for all operations

## Future Enhancements

Potential improvements for this feature:
1. Path validation (check if directory exists)
2. Directory browser/picker integration
3. Batch operations for multiple projects
4. Export/import path configurations
5. Integration with repository operations

## Testing

The feature includes comprehensive unit tests covering:
- Dialog rendering
- Path loading
- Save operations
- Delete operations
- Error handling
- User interactions

Run tests with:
```bash
npm test -- --testPathPattern=ProjectConfigDialog.test.js
```