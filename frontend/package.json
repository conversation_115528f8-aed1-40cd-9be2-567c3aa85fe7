{"name": "jira-bug-browser-frontend", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.17.1", "@mui/material": "^5.14.2", "axios": "^1.3.0", "jwt-decode": "^4.0.0", "lucide-react": "^0.263.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.10.0", "socket.io-client": "^4.8.1", "web-vitals": "^5.0.1"}, "scripts": {"start": "PORT=3000 react-scripts start", "build": "react-scripts build", "test": "react-scripts test --transformIgnorePatterns 'node_modules/(?!axios)/'", "test:coverage": "react-scripts test --coverage --watchAll=false", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^14.3.1", "@testing-library/user-event": "^14.6.1", "jest-axe": "^8.0.0", "jest-environment-jsdom": "^29.5.0", "msw": "^1.3.5", "react-scripts": "5.0.1", "whatwg-fetch": "^3.6.20"}, "jest": {"collectCoverageFrom": ["src/**/*.{js,jsx}", "!src/index.js", "!src/reportWebVitals.js", "!src/setupTests.js", "!src/__tests__/mocks/server.js", "!src/__tests__/mocks/handlers.js"], "coverageThreshold": {"global": {"branches": 80, "functions": 80, "lines": 80, "statements": 80}}, "moduleNameMapper": {"^axios$": "axios/dist/node/axios.cjs"}}}