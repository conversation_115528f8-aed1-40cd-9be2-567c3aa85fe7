import React, { useState, useContext, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider, CssBaseline } from '@mui/material';
import theme from './theme';
import LoginPage from './views/LoginPage';
import MainLayout from './layouts/MainLayout';
import DashboardPage from './views/DashboardPage'; // Placeholder for content after login
import JiraSettingsPage from './views/JiraSettingsPage'; // JIRA settings page
import ProjectsPageV3 from './views/ProjectsPageV3'; // Projects page with enhanced repository management
import RepositorySourcesPage from './views/RepositorySourcesPage'; // Repository sources page
import RepositoriesPage from './views/RepositoriesPage'; // All repositories page
import FunctionalAreasPage from './views/FunctionalAreasPage'; // Functional areas page
import IssuesPage from './views/IssuesPage'; // Issues page for a project
import IssueDetailsPage from './views/IssueDetailsPage'; // Issue details page
import { setAuthToken, registerAuthHandlers } from './services/api'; // Import API utilities
import { jwtDecode } from 'jwt-decode';

// Basic Auth Context (can be expanded or moved to its own file)
export const AuthContext = React.createContext();

const AuthProvider = ({ children }) => {
  const [authState, setAuthState] = useState({
    isAuthenticated: false,
    token: null,
    refreshToken: null,
    user: null,
    tenantId: null,
    isLoading: true, // To handle initial token loading state
  });

  // Define register auth handlers outside of useEffect to avoid reference issues
  const handleAuthError = () => {
    console.log('Auth error handler called from App component');
    setAuthState({
      isAuthenticated: false,
      token: null,
      refreshToken: null,
      user: null,
      tenantId: null,
      isLoading: false,
    });
  };

  useEffect(() => {
    const token = localStorage.getItem('accessToken');
    const storedRefreshToken = localStorage.getItem('refreshToken');
    const storedTenantId = localStorage.getItem('tenantId');

    if (token && storedRefreshToken && storedTenantId) {
      try {
        const decodedToken = jwtDecode(token);
        // Don't check token expiry here - let the interceptor handle it
        // The interceptor will automatically refresh the token if needed
        
        setAuthToken(token); // Set token for API calls
        setAuthState({
          isAuthenticated: true,
          token: token,
          refreshToken: storedRefreshToken,
          user: { email: decodedToken.email, roles: decodedToken.roles, id: decodedToken.sub },
          tenantId: storedTenantId, // Use stored tenantId consistent with login
          isLoading: false,
        });
      } catch (error) {
        console.error("Error decoding token from localStorage:", error);
        localStorage.removeItem('accessToken');
        localStorage.removeItem('refreshToken');
        localStorage.removeItem('tenantId');
        setAuthState({ isAuthenticated: false, token: null, refreshToken: null, user: null, tenantId: null, isLoading: false });
      }
    } else {
      setAuthState(prevState => ({ ...prevState, isLoading: false }));
    }
  }, []);

  const login = (data) => {
    localStorage.setItem('accessToken', data.access_token);
    localStorage.setItem('refreshToken', data.refresh_token);
    localStorage.setItem('tenantId', data.tenantId); // Store tenantId on login
    setAuthToken(data.access_token); // Set for current session API calls
    setAuthState({
      isAuthenticated: true,
      token: data.access_token,
      refreshToken: data.refresh_token,
      user: { email: data.email, roles: data.roles, id: data.userId },
      tenantId: data.tenantId,
      isLoading: false,
    });
  };

  const logout = () => {
    localStorage.removeItem('accessToken');
    localStorage.removeItem('refreshToken');
    localStorage.removeItem('tenantId');
    setAuthToken(null); // Clear token from API client
    setAuthState({
      isAuthenticated: false,
      token: null,
      refreshToken: null,
      user: null,
      tenantId: null,
      isLoading: false,
    });
  };
  
  // Register auth handlers after login/logout functions are defined
  useEffect(() => {
    registerAuthHandlers({
      onAuthError: handleAuthError,
      onLogout: logout
    });
  }, []);

  // Show a loading spinner or similar while checking auth state
  if (authState.isLoading) {
    // You can return a global loading spinner component here
    return <div style={{display: 'flex', justifyContent:'center', alignItems:'center', height:'100vh'}}>Loading application...</div>;
  }

  return (
    <AuthContext.Provider value={{ authState, login, logout }}>
      {children}
    </AuthContext.Provider>
  );
};

// ProtectedRoute component
const ProtectedRoute = ({ children }) => {
  const { authState } = React.useContext(AuthContext);
  if (!authState.isAuthenticated) {
    return <Navigate to="/login" replace />;
  }
  return children;
};

function App() {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline /> 
      <AuthProvider>
        <Router>
          <Routes>
            <Route path="/login" element={<LoginPage />} />
            <Route 
              path="/*" 
              element={ 
                <ProtectedRoute>
                  <MainLayout />
                </ProtectedRoute>
              }
            >
              {/* Nested routes within MainLayout - these will render in MainLayout's <Outlet /> */}
              <Route index element={<DashboardPage />} /> {/* Default page after login */}
              <Route path="jira-settings" element={<JiraSettingsPage />} /> {/* JIRA settings page */}
              <Route path="projects" element={<ProjectsPageV3 />} /> {/* Projects page with enhanced repository management */}
              <Route path="projects/:projectKey" element={<IssuesPage />} /> {/* Issues for specific project */}
              <Route path="issues" element={<DashboardPage />} /> {/* Placeholder: All issues page */}
              <Route path="issues/:issueKey" element={<IssueDetailsPage />} /> {/* Single issue details */}
              <Route path="repository-sources" element={<RepositorySourcesPage />} /> {/* Repository sources page */}
              <Route path="repositories" element={<RepositoriesPage />} /> {/* All repositories page */}
              <Route path="functional-areas" element={<FunctionalAreasPage />} /> {/* Functional areas page */}
              {/* Add other nested routes here */}
            </Route>
            <Route path="*" element={<Navigate to="/login" replace />} /> {/* Fallback route */}
          </Routes>
        </Router>
      </AuthProvider>
    </ThemeProvider>
  );
}

export default App;