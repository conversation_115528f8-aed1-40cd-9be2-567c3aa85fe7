import React from 'react';
import { render } from '@testing-library/react';
import App from '../App';

// Mock the components that would be rendered
jest.mock('../views/LoginPage', () => () => <div data-testid="login-page">Login Page</div>);
jest.mock('../layouts/MainLayout', () => () => <div data-testid="main-layout">Main Layout</div>);

// Mock JWT decode
jest.mock('jwt-decode', () => ({
  jwtDecode: jest.fn().mockImplementation(() => ({
    email: '<EMAIL>',
    roles: ['user'],
    sub: '123',
    tenant_id: 'tenant-123',
  }))
}));

describe('App Component', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
  });

  test('checks localStorage for tokens on mount', () => {
    render(<App />);
    
    // Verify that localStorage.getItem was called for tokens
    expect(window.localStorage.getItem).toHaveBeenCalled();
  });
});