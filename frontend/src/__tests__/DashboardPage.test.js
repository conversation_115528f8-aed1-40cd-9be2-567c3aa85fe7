import React from 'react';
import { render, screen } from '@testing-library/react';
import DashboardPage from '../views/DashboardPage';

describe('DashboardPage Component', () => {
  test('renders welcome message', () => {
    render(<DashboardPage />);
    expect(screen.getByText(/Welcome to the JIRA Bug Browser/i)).toBeInTheDocument();
  });

  test('renders description text', () => {
    render(<DashboardPage />);
    expect(screen.getByText(/This is the main dashboard area/i)).toBeInTheDocument();
  });

  test('renders next steps section', () => {
    render(<DashboardPage />);
    expect(screen.getByText(/Next Steps:/i)).toBeInTheDocument();
  });
});