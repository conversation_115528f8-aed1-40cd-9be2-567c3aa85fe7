import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import LoginPage from '../views/LoginPage';
import { AuthContext } from '../App';
import { loginUser, resolveTenantByName } from '../services/api';

// Mock the necessary modules
jest.mock('../services/api', () => ({
  loginUser: jest.fn(),
  setAuthToken: jest.fn(),
  resolveTenantByName: jest.fn(() => Promise.reject(new Error('Tenant not found'))),
}));

// Mock react-router-dom
jest.mock('react-router-dom', () => ({
  useNavigate: () => jest.fn(),
}));

// Mock jwt-decode
jest.mock('jwt-decode', () => ({
  jwtDecode: jest.fn().mockImplementation(() => ({
    email: '<EMAIL>',
    roles: ['user'],
    sub: '123',
    tenant_id: 'tenant-123',
  })),
}));

describe('LoginPage Component', () => {
  const mockLogin = jest.fn();
  const mockAuthContext = {
    login: mockLogin,
    authState: {
      isAuthenticated: false,
      token: null,
      refreshToken: null,
      user: null,
      tenantId: null,
    },
  };

  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
  });

  test('renders login form with all fields', () => {
    render(
      <AuthContext.Provider value={mockAuthContext}>
        <LoginPage />
      </AuthContext.Provider>
    );

    // Check if form elements are rendered
    expect(screen.getByLabelText(/Tenant Name\/Slug/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Email Address/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/Password/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /Sign In/i })).toBeInTheDocument();
  });

  test('shows validation error when fields are empty', async () => {
    render(
      <AuthContext.Provider value={mockAuthContext}>
        <LoginPage />
      </AuthContext.Provider>
    );

    // Mock empty form values
    const tenantField = screen.getByLabelText(/Tenant Name\/Slug/i);
    const emailField = screen.getByLabelText(/Email Address/i);
    const passwordField = screen.getByLabelText(/Password/i);
    
    fireEvent.change(tenantField, { target: { value: '' } });
    fireEvent.change(emailField, { target: { value: '' } });
    fireEvent.change(passwordField, { target: { value: '' } });

    // Submit the form
    fireEvent.click(screen.getByRole('button', { name: /Sign In/i }));

    // Verify error is shown
    await waitFor(() => {
      expect(screen.getByText(/All fields are required/i)).toBeInTheDocument();
    });
  });
});