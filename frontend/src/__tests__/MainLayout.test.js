import React from 'react';
import { render, screen } from '@testing-library/react';
import MainLayout from '../layouts/MainLayout';
import { AuthContext } from '../App';

// Mock react-router-dom
jest.mock('react-router-dom', () => ({
  Outlet: () => <div data-testid="outlet">Outlet Content</div>,
  useNavigate: () => jest.fn(),
  useLocation: () => ({ pathname: '/' }),
  Link: ({ children, ...props }) => <a {...props}>{children}</a>,
}));

describe('MainLayout Component', () => {
  const mockLogout = jest.fn();
  
  // Mock auth context with authenticated user
  const mockAuthContext = {
    authState: {
      isAuthenticated: true,
      token: 'fake-token',
      refreshToken: 'fake-refresh-token',
      user: {
        email: '<EMAIL>',
        roles: ['user'],
        id: '123',
      },
      tenantId: 'tenant-123',
    },
    logout: mockLogout,
  };

  test('renders top bar with application name', () => {
    render(
      <AuthContext.Provider value={mockAuthContext}>
        <MainLayout />
      </AuthContext.Provider>
    );

    expect(screen.getByText(/ACME JIRA/i)).toBeInTheDocument();
  });

  test('displays tenant badge when tenantId is available', () => {
    render(
      <AuthContext.Provider value={mockAuthContext}>
        <MainLayout />
      </AuthContext.Provider>
    );

    // Look for partial match of tenant ID (since it's truncated in UI)
    expect(screen.getByText(/Tenant:/i)).toBeInTheDocument();
  });

  test('renders outlet content', () => {
    render(
      <AuthContext.Provider value={mockAuthContext}>
        <MainLayout />
      </AuthContext.Provider>
    );

    expect(screen.getByTestId('outlet')).toBeInTheDocument();
  });
});