import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import ProjectConfigDialog from '../components/ProjectConfigDialog';
import * as api from '../services/api';

// Mock the API functions
jest.mock('../services/api');

describe('ProjectConfigDialog', () => {
  const mockProps = {
    open: true,
    onClose: jest.fn(),
    projectKey: 'PROJ-1',
    projectName: 'Test Project',
    tenantId: 'test-tenant-id'
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders dialog with project information', () => {
    api.getUserProjectPath.mockRejectedValueOnce({ response: { status: 404 } });
    
    render(<ProjectConfigDialog {...mockProps} />);
    
    expect(screen.getByText('Configure Project Path')).toBeInTheDocument();
    expect(screen.getByText(/Test Project/)).toBeInTheDocument();
    expect(screen.getByText(/PROJ-1/)).toBeInTheDocument();
  });

  test('loads existing path on mount', async () => {
    api.getUserProjectPath.mockResolvedValueOnce({
      local_path: '/Users/<USER>/projects/test-project'
    });
    
    render(<ProjectConfigDialog {...mockProps} />);
    
    await waitFor(() => {
      const input = screen.getByLabelText('Local Repository Path');
      expect(input.value).toBe('/Users/<USER>/projects/test-project');
    });
  });

  test('saves new path when save button is clicked', async () => {
    api.getUserProjectPath.mockRejectedValueOnce({ response: { status: 404 } });
    api.saveUserProjectPath.mockResolvedValueOnce({});
    
    const { container } = render(<ProjectConfigDialog {...mockProps} />);
    
    await waitFor(() => {
      expect(screen.getByLabelText('Local Repository Path')).toBeInTheDocument();
    });
    
    const input = screen.getByLabelText('Local Repository Path');
    fireEvent.change(input, { target: { value: '/Users/<USER>/new-path' } });
    
    await waitFor(() => {
      const saveButton = screen.getByText('Save');
      expect(saveButton).not.toBeDisabled();
    });
    
    const saveButton = screen.getByText('Save');
    fireEvent.click(saveButton);
    
    await waitFor(() => {
      expect(api.saveUserProjectPath).toHaveBeenCalledWith(
        'test-tenant-id',
        'PROJ-1',
        '/Users/<USER>/new-path'
      );
    });
  });

  test('shows error when save fails', async () => {
    api.getUserProjectPath.mockRejectedValueOnce({ response: { status: 404 } });
    api.saveUserProjectPath.mockRejectedValueOnce(new Error('Save failed'));
    
    render(<ProjectConfigDialog {...mockProps} />);
    
    await waitFor(() => {
      expect(screen.getByLabelText('Local Repository Path')).toBeInTheDocument();
    });
    
    const input = screen.getByLabelText('Local Repository Path');
    fireEvent.change(input, { target: { value: '/Users/<USER>/new-path' } });
    
    await waitFor(() => {
      const saveButton = screen.getByText('Save');
      expect(saveButton).not.toBeDisabled();
      fireEvent.click(saveButton);
    });
    
    await waitFor(() => {
      expect(screen.getByText('Failed to save project path')).toBeInTheDocument();
    });
  });

  test('deletes path when remove button is clicked', async () => {
    api.getUserProjectPath.mockResolvedValueOnce({
      local_path: '/Users/<USER>/projects/test-project'
    });
    api.deleteUserProjectPath.mockResolvedValueOnce({});
    
    render(<ProjectConfigDialog {...mockProps} />);
    
    await waitFor(() => {
      const removeButton = screen.getByText('Remove Path');
      fireEvent.click(removeButton);
    });
    
    await waitFor(() => {
      expect(api.deleteUserProjectPath).toHaveBeenCalledWith(
        'test-tenant-id',
        'PROJ-1'
      );
    });
  });

  test('closes dialog when cancel button is clicked', async () => {
    api.getUserProjectPath.mockRejectedValueOnce({ response: { status: 404 } });
    
    render(<ProjectConfigDialog {...mockProps} />);
    
    await waitFor(() => {
      expect(screen.getByText('Cancel')).toBeInTheDocument();
    });
    
    const cancelButton = screen.getByText('Cancel');
    fireEvent.click(cancelButton);
    
    expect(mockProps.onClose).toHaveBeenCalled();
  });
});