import React from 'react';
import { render, screen } from '@testing-library/react';
import LoginPage from '../../views/LoginPage';
import DashboardPage from '../../views/DashboardPage';
import MainLayout from '../../layouts/MainLayout';
import { AuthContext } from '../../App';

// Mock the AuthContext
const mockAuthContext = {
  authState: {
    isAuthenticated: true,
    token: 'fake-token',
    refreshToken: 'fake-refresh-token',
    user: {
      email: '<EMAIL>',
      roles: ['user'],
      id: '123',
    },
    tenantId: 'tenant-123',
  },
  login: jest.fn(),
  logout: jest.fn(),
};

// Mock react-router-dom
jest.mock('react-router-dom', () => ({
  useNavigate: () => jest.fn(),
  Outlet: () => <div data-testid="outlet">Outlet Content</div>,
}));

describe('Accessibility Tests', () => {
  test('Button components have accessible names', () => {
    render(
      <AuthContext.Provider value={mockAuthContext}>
        <LoginPage />
      </AuthContext.Provider>
    );
    
    const buttons = screen.getAllByRole('button');
    buttons.forEach(button => {
      expect(button).toHaveAccessibleName();
    });
  });

  test('Form fields have associated labels', () => {
    render(
      <AuthContext.Provider value={mockAuthContext}>
        <LoginPage />
      </AuthContext.Provider>
    );
    
    const inputs = screen.getAllByRole('textbox');
    inputs.forEach(input => {
      expect(input).toHaveAccessibleName();
    });
    
    const password = screen.getByLabelText(/Password/i);
    expect(password).toHaveAccessibleName();
  });
});