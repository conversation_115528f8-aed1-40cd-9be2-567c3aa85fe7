import { rest } from 'msw';

const API_BASE_URL = 'http://localhost:5045';

export const handlers = [
  // Auth endpoints
  rest.post(`${API_BASE_URL}/tenants/:tenantId/auth/login`, (req, res, ctx) => {
    const { email, password } = req.body;
    
    if (email === '<EMAIL>' && password === 'password123') {
      return res(
        ctx.json({
          access_token: 'fake-access-token',
          refresh_token: 'fake-refresh-token',
        })
      );
    }
    
    return res(
      ctx.status(401),
      ctx.json({
        title: 'Unauthorized',
        status: 401,
        detail: 'Invalid credentials.'
      })
    );
  }),
  
  rest.post(`${API_BASE_URL}/tenants/:tenantId/auth/refresh`, (req, res, ctx) => {
    // Check if Authorization header exists and contains a token
    const authHeader = req.headers.get('Authorization');
    
    if (authHeader && authHeader.includes('Bearer')) {
      return res(
        ctx.json({
          access_token: 'new-fake-access-token',
        })
      );
    }
    
    return res(
      ctx.status(401),
      ctx.json({
        title: 'Unauthorized',
        status: 401,
        detail: 'Invalid refresh token.'
      })
    );
  }),
  
  rest.post(`${API_BASE_URL}/tenants/:tenantId/auth/logout`, (req, res, ctx) => {
    return res(
      ctx.json({
        message: 'Logout successful'
      })
    );
  }),
  
  // Tenant endpoints
  rest.get(`${API_BASE_URL}/api/public/tenants/resolve`, (req, res, ctx) => {
    const tenantName = req.url.searchParams.get('name');
    
    if (tenantName === 'DefaultTenant' || tenantName === 'test-tenant') {
      return res(
        ctx.json({
          id: 'tenant-123',
          name: tenantName
        })
      );
    }
    
    return res(
      ctx.status(404),
      ctx.json({
        title: 'Not Found',
        status: 404,
        detail: `Tenant '${tenantName}' not found`
      })
    );
  }),
  
  // Projects endpoints
  rest.get(`${API_BASE_URL}/tenants/:tenantId/projects`, (req, res, ctx) => {
    return res(
      ctx.json([
        { key: 'PROJ1', name: 'Project 1', bugCount: 10 },
        { key: 'PROJ2', name: 'Project 2', bugCount: 5 },
        { key: 'PROJ3', name: 'Project 3', bugCount: 15 }
      ])
    );
  }),
  
  // Issues endpoints
  rest.get(`${API_BASE_URL}/tenants/:tenantId/projects/:projectKey/issues`, (req, res, ctx) => {
    const type = req.url.searchParams.get('type') || 'Bug';
    const page = parseInt(req.url.searchParams.get('page') || '1');
    const projectKey = req.params.projectKey;
    
    return res(
      ctx.json({
        issues: [
          { key: `${projectKey}-1`, summary: 'First bug', status: 'To Do', priority: 'High', assignee: 'John', updated: '1d' },
          { key: `${projectKey}-2`, summary: 'Second bug', status: 'In Progress', priority: 'Medium', assignee: 'Alice', updated: '2d' },
          { key: `${projectKey}-3`, summary: 'Third bug', status: 'Done', priority: 'Low', assignee: 'Bob', updated: '3d' }
        ],
        pagination: {
          page,
          totalPages: 2,
          totalItems: 6,
          pageSize: 3
        }
      })
    );
  }),
  
  rest.get(`${API_BASE_URL}/tenants/:tenantId/issues/:issueKey`, (req, res, ctx) => {
    const { issueKey } = req.params;
    
    return res(
      ctx.json({
        key: issueKey,
        summary: 'Detailed bug information',
        description: 'This is a detailed description of the bug.',
        status: 'In Progress',
        priority: 'High',
        assignee: 'John Doe',
        reporter: 'Jane Smith',
        created: '2023-01-15T10:30:00Z',
        updated: '2023-01-20T14:45:00Z',
        comments: [
          { author: 'Jane Smith', text: 'Initial report', created: '2023-01-15T10:30:00Z' },
          { author: 'John Doe', text: 'Working on it', created: '2023-01-18T09:15:00Z' }
        ],
        attachments: [
          { name: 'screenshot.png', size: 1024 * 1024, created: '2023-01-15T10:35:00Z' }
        ]
      })
    );
  }),
  
  // JIRA credentials endpoints
  rest.post(`${API_BASE_URL}/tenants/:tenantId/jira/credentials`, (req, res, ctx) => {
    return res(
      ctx.json({
        message: 'JIRA credentials updated successfully'
      })
    );
  }),
  
  rest.get(`${API_BASE_URL}/tenants/:tenantId/jira/credentials`, (req, res, ctx) => {
    return res(
      ctx.json({
        base_url: 'https://example.atlassian.net',
        configured: true,
        last_updated: '2023-01-10T12:00:00Z'
      })
    );
  })
];