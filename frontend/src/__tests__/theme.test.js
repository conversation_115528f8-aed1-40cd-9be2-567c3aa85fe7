import theme from '../theme';

describe('Theme Configuration', () => {
  test('has correct primary color', () => {
    expect(theme.palette.primary.main).toBe('#2079A6'); // --mk-sea-blue-500
  });

  test('has correct secondary color', () => {
    expect(theme.palette.secondary.main).toBe('#2E8F6E'); // --mk-monsoon-green-500
  });

  test('has correct background colors', () => {
    expect(theme.palette.background.default).toBe('#F7F9F4'); // --mk-mullappoo-white
    expect(theme.palette.background.paper).toBe('#E8ECEA'); // --mk-shadow-100
  });

  test('has correct text colors', () => {
    expect(theme.palette.text.primary).toBe('#754C24'); // --mk-arecanut-brown-600
    expect(theme.palette.text.secondary).toBe('#4F4F4F');
  });

  test('has correct semantic colors', () => {
    expect(theme.palette.success.main).toBe('#27AE60');
    expect(theme.palette.warning.main).toBe('#F2994A');
    expect(theme.palette.error.main).toBe('#EB5757');
  });

  test('has correct typography settings', () => {
    expect(theme.typography.fontFamily).toContain('Inter');
    expect(theme.typography.fontFamily).toContain('Roboto');
    expect(theme.typography.fontSize).toBe(14);
    
    // Check heading weights
    expect(theme.typography.h1.fontWeight).toBe(600);
    expect(theme.typography.h2.fontWeight).toBe(600);
    expect(theme.typography.h3.fontWeight).toBe(600);
    expect(theme.typography.h4.fontWeight).toBe(600);
    expect(theme.typography.h5.fontWeight).toBe(600);
    expect(theme.typography.h6.fontWeight).toBe(600);
    
    // Check body settings
    expect(theme.typography.body1.fontWeight).toBe(400);
    expect(theme.typography.body1.lineHeight).toBe('20px');
  });

  test('has correct shape settings', () => {
    expect(theme.shape.borderRadius).toBe(10); // Base border radius from UI spec
  });
});