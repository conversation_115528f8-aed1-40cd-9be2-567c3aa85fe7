import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Checkbox,
  Chip,
  LinearProgress,
  Alert,
  IconButton,
  Divider,
  Card,
  CardContent,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  CircularProgress
} from '@mui/material';
import {
  PlayArrow as PlayIcon,
  Stop as StopIcon,
  Clear as ClearIcon,
  Storage as RepositoryIcon,
  BugReport as BugIcon,
  Terminal as TerminalIcon,
  Close as CloseIcon
} from '@mui/icons-material';
import io from 'socket.io-client';
import { executeAgentBugFix, getProjectRepositories, getAgentExecutionStatus, testSocketConnection, getSocketStatus } from '../services/api';

const BugFixConsole = ({
  open,
  onClose,
  issueKey,
  issueDetails,
  tenantId,
  projectKey
}) => {
  const [repositories, setRepositories] = useState([]);
  const [selectedRepos, setSelectedRepos] = useState([]);
  const [isExecuting, setIsExecuting] = useState(false);
  const [executionId, setExecutionId] = useState(null);
  const [consoleOutput, setConsoleOutput] = useState([]);
  const [error, setError] = useState(null);
  const [loading, setLoading] = useState(true);
  const [socket, setSocket] = useState(null);
  const [executionStatus, setExecutionStatus] = useState('idle'); // idle, running, completed, failed
  const [showTimeoutWarning, setShowTimeoutWarning] = useState(false);
  const [socketConnected, setSocketConnected] = useState(false);

  const consoleEndRef = useRef(null);
  const socketRef = useRef(null);

  // Scroll to bottom of console
  const scrollToBottom = () => {
    consoleEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [consoleOutput]);

  // Fetch available repositories
  useEffect(() => {
    if (open && projectKey) {
      fetchRepositories();
    }
  }, [open, projectKey]);

  // Define a function to setup socket handlers
  const setupSocketHandlers = (newSocket) => {
    // First remove any existing handlers to avoid duplicates
    newSocket.removeAllListeners();

    console.log(`Setting up handlers for socket with ID: ${newSocket.id || 'Not connected yet'}`);


    // Connection events
    newSocket.on('connect', () => {
      console.log('Connected to WebSocket server with ID:', newSocket.id);
      setSocketConnected(true);
      addConsoleMessage(`Connected to agent execution server (socket ID: ${newSocket.id})`, 'system');
    });

    newSocket.on('connect_error', (error) => {
      console.error('Socket connection error:', error);
      setSocketConnected(false);
      addConsoleMessage(`Socket connection error: ${error.message}`, 'error');
      setError(`WebSocket connection error: ${error.message}`);
      setExecutionStatus('failed');
      setIsExecuting(false);
    });

    newSocket.on('disconnect', () => {
      console.log('Disconnected from WebSocket server');
      setSocketConnected(false);
      addConsoleMessage('Disconnected from agent execution server', 'system');
    });

    // Reconnection events
    newSocket.on('reconnect', (attemptNumber) => {
      console.log(`Reconnected to WebSocket server after ${attemptNumber} attempts`);
      setSocketConnected(true);
      addConsoleMessage(`Reconnected to agent execution server (attempt #${attemptNumber})`, 'system');
    });

    newSocket.on('reconnect_attempt', (attemptNumber) => {
      console.log(`Attempting to reconnect to WebSocket server (attempt #${attemptNumber})`);
      addConsoleMessage(`Attempting to reconnect (attempt #${attemptNumber})...`, 'system');
    });

    newSocket.on('reconnect_error', (error) => {
      console.error('Socket reconnection error:', error);
      addConsoleMessage(`Reconnection error: ${error.message}`, 'error');
    });

    newSocket.on('reconnect_failed', () => {
      console.error('Failed to reconnect to WebSocket server');
      setSocketConnected(false);
      addConsoleMessage('Failed to reconnect to agent execution server after multiple attempts', 'error');
    });



    newSocket.on('error', (data) => {
      console.error('Socket error:', data);
      addConsoleMessage(`Socket error: ${data.message || JSON.stringify(data)}`, 'error');
    });

    // Agent events - crucial for displaying agent output
    newSocket.on('agent_output', (data) => {
      console.log('Agent output received:', data);
      // More robust handling of data
      if (data && typeof data === 'object') {
        const content = data.content || data.message || JSON.stringify(data);
        const type = data.type || 'system';
        addConsoleMessage(content, type);
      } else if (typeof data === 'string') {
        addConsoleMessage(data, 'system');
      } else {
        console.error('Received invalid agent_output data format:', data);
        addConsoleMessage('Received message in unknown format', 'error');
      }
    });

    // Make sure we also listen for the 'message' event which is a standard Socket.IO event
    newSocket.on('message', (data) => {
      console.log('Generic message received:', data);
      if (typeof data === 'string') {
        addConsoleMessage(data, 'system');
      } else if (data && typeof data === 'object') {
        const content = data.content || data.message || JSON.stringify(data);
        const type = data.type || 'system';
        addConsoleMessage(content, type);
      }
    });


    newSocket.on('agent_completed', (data) => {
      console.log('Agent completed event received:', data);
      setExecutionStatus('completed');
      setIsExecuting(false);
      setShowTimeoutWarning(false);
      addConsoleMessage('Agent execution completed successfully', 'success');
      if (data && data.result) {
        addConsoleMessage(`Result: ${data.result}`, 'system');
      }
    });

    // Listen for any event - useful for debugging
    // Note: this might be noisy, but helps catch unexpected events
    newSocket.onAny((eventName, ...args) => {
      console.log(`Received event '${eventName}':`, args);
    });
  };

  // Initialize WebSocket connection
  useEffect(() => {
    if (open) {
      // Use explicit URL with port to avoid confusion
      const socketUrl = process.env.REACT_APP_WS_URL || `http://localhost:5045`;
      console.log(`Using Socket.IO URL: ${socketUrl}`);
      console.log(`Connecting to Socket.IO at ${socketUrl}`);

      // Create socket with improved configuration
      const newSocket = io(socketUrl, {
        reconnection: true,
        reconnectionDelay: 1000,
        query: { tenantId: tenantId }, // Include tenant ID in the connection
        auth: { tenantId: tenantId },  // Add auth parameter for additional security
        path: '/socket.io',  // Explicitly set the Socket.IO path for clarity
        autoConnect: true  // Ensure automatic connection
      });

      // Store the socket reference
      socketRef.current = newSocket;
      setSocket(newSocket);

      // Setup all event handlers
      setupSocketHandlers(newSocket);

      console.log("Socket connection initialized with ID:", newSocket.id);

      // If not connected right away, log it
      if (!newSocket.connected) {
        console.log("Socket not yet connected - waiting for connect event");
      }



      return () => {


        // Leave any namespaces before closing
        if (newSocket && newSocket.connected) {
          console.log("Leaving namespaces and disconnecting socket");
            console.log("Closing socket connection");
            newSocket.close();

        }
      };
    }
  }, [open, tenantId, executionId]);

  const fetchRepositories = async () => {
    try {
      setLoading(true);
      console.log(`Fetching repositories for tenant ${tenantId}, project ${projectKey}`);
      const response = await getProjectRepositories(tenantId, projectKey);
      console.log('Repositories response:', response);

      if (response && Array.isArray(response.repositories)) {
        setRepositories(response.repositories);
      } else {
        console.error('Invalid repositories response format:', response);
        setRepositories([]);
      }
    } catch (err) {
      console.error('Error fetching repositories:', err);
      const errorMessage = err.response?.data?.error || err.message || 'Failed to load repositories';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const addConsoleMessage = (message, type = 'stdout') => {
    const timestamp = new Date().toLocaleTimeString();
    setConsoleOutput(prev => [...prev, { message, type, timestamp }]);
  };

  const handleRepoToggle = (repoId) => {
    setSelectedRepos(prev => {
      if (prev.includes(repoId)) {
        return prev.filter(id => id !== repoId);
      } else {
        return [...prev, repoId];
      }
    });
  };

  const handleExecute = async () => {
    if (selectedRepos.length === 0) {
      setError('Please select at least one repository');
      return;
    }

    try {
      // Clear any previous errors and console output
      setError(null);
      setIsExecuting(true);
      setExecutionStatus('running');
      setConsoleOutput([]);

      addConsoleMessage(`Starting bug fix for issue ${issueKey}`, 'system');

      const response = await executeAgentBugFix(tenantId, issueKey, selectedRepos);
      console.log('Agent execution started:', response);
      setExecutionId(response.execution_id);

    } catch (err) {
      console.error('Error starting agent execution:', err);
      const errorMessage = err.response?.data?.detail || err.message || 'Failed to start agent execution';
      setError(errorMessage);
      setIsExecuting(false);
      setExecutionStatus('failed');
      addConsoleMessage(`Error: ${errorMessage}`, 'error');
    }
  };

  const handleStop = () => {
    // TODO: Implement stop functionality
    if (socketRef.current && executionId) {
      socketRef.current.emit('stop_execution', { execution_id: executionId });
    }
    setIsExecuting(false);
    setExecutionStatus('idle');
    setShowTimeoutWarning(false);
    addConsoleMessage('Execution stopped by user', 'system');
  };

  const handleRetry = () => {
    // Stop the current execution and start a new one
    handleStop();
    setTimeout(() => {
      handleExecute();
    }, 500);
  };

  const handleClearConsole = () => {
    setConsoleOutput([]);
  };

  const handleTestConnection = async () => {
    try {
      // First clear any relevant errors
      setError(null);
      addConsoleMessage("Starting socket connection test...", "system");

      // Check if we have an active socket
      if (!socketRef.current || !socketRef.current.connected) {
        addConsoleMessage("Socket is not connected. Trying to reconnect...", "system");
        // Try to reinitialize socket using socket.io-client
        const socketUrl = process.env.REACT_APP_WS_URL || `http://${window.location.hostname}:5045`;
        socketRef.current = io(socketUrl, {
          transports: ['polling'], // Use only polling transport to match backend configuration
          reconnection: true,
          reconnectionAttempts: 5,
          reconnectionDelay: 1000,
          timeout: 30000,
          forceNew: true,
          query: { tenantId }
        });

        // Set up handlers
        setupSocketHandlers(socketRef.current);
        setSocket(socketRef.current);

        // Give it a moment to connect
        await new Promise(resolve => setTimeout(resolve, 1000));
      }

      // Try a direct ping first (most basic test)
      addConsoleMessage("Sending ping to server...", "system");
      socketRef.current.emit('ping', { timestamp: new Date().toISOString() });

      // Now perform the comprehensive connection test
      addConsoleMessage("Requesting server to create test namespace...", "system");
      const response = await testSocketConnection(tenantId);

      if (response.success) {
        addConsoleMessage(`Test namespace created: ${response.namespace_id}`, "system");

        // Join the test namespace
        socketRef.current.emit('join_execution', {
          namespace_id: response.namespace_id,
          user_id: `user_${tenantId}`
        });

        addConsoleMessage("Joined test namespace, waiting for test messages...", "system");

        // Also check server-side socket status
        try {
          const statusResponse = await getSocketStatus(tenantId);
          addConsoleMessage(`Server reports ${statusResponse.total_namespaces} active namespaces (${statusResponse.tenant_namespaces} for this tenant)`, "system");
        } catch (err) {
          console.error('Error fetching socket status:', err);
          addConsoleMessage(`Error fetching socket status: ${err.message}`, "error");
        }
      } else {
        addConsoleMessage(`Socket test failed: ${response.error || 'Unknown error'}`, "error");
      }
    } catch (err) {
      console.error('Error testing connection:', err);
      addConsoleMessage(`Error testing connection: ${err.message}`, "error");
    }
  };

  const getMessageColor = (type) => {
    switch (type) {
      case 'error':
        return '#ff5252';
      case 'success':
        return '#4caf50';
      case 'system':
        return '#2196f3';
      case 'stderr':
        return '#ff9800';
      case 'agent':
        return '#9c27b0'; // Purple for agent messages
      default:
        return '#ffffff';
    }
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      sx={{
        '& .MuiDialog-paper': {
          height: '90vh',
          maxHeight: '900px'
        }
      }}
    >
      <DialogTitle>
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Box display="flex" alignItems="center" gap={1}>
            <BugIcon color="primary" />
            <Typography variant="h6">
              Bug Fix Console - {issueKey}
            </Typography>
          </Box>
          <IconButton onClick={onClose} size="small">
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent dividers sx={{ p: 0 }}>
        <Box sx={{ display: 'flex', height: '100%' }}>
          {/* Left panel - Repository selection */}
          <Box
            sx={{
              width: '300px',
              borderRight: 1,
              borderColor: 'divider',
              overflow: 'auto',
              p: 2
            }}
          >
            <Typography variant="subtitle1" gutterBottom>
              Select Repositories
            </Typography>

            {loading ? (
              <Box display="flex" justifyContent="center" py={3}>
                <CircularProgress />
              </Box>
            ) : error ? (
              <Box>
                <Alert severity="error" sx={{ mb: 2 }}>
                  {error}
                </Alert>
                <Button variant="outlined" onClick={fetchRepositories} sx={{ mb: 2 }}>
                  Retry
                </Button>
              </Box>
            ) : repositories.length === 0 ? (
              <Alert severity="info" sx={{ mb: 2 }}>
                No repositories linked to this project. Please link repositories from the project page first.
              </Alert>
            ) : (
              <List dense>
                {repositories.map((repo) => (
                  <ListItem
                    key={repo.id}
                    onClick={() => !isExecuting && handleRepoToggle(repo.id)}
                    sx={{ cursor: isExecuting ? 'default' : 'pointer', opacity: isExecuting ? 0.6 : 1 }}
                  >
                    <ListItemIcon>
                      <Checkbox
                        edge="start"
                        checked={selectedRepos.includes(repo.id)}
                        tabIndex={-1}
                        disableRipple
                      />
                    </ListItemIcon>
                    <ListItemText
                      primary={repo.full_name || repo.name}
                      secondary={repo.source_type || 'Repository'}
                    />
                  </ListItem>
                ))}
              </List>
            )}

            <Divider sx={{ my: 2 }} />

            <Box sx={{ display: 'flex', gap: 1, mb: 1 }}>
              <Button
                fullWidth
                variant="contained"
                color="primary"
                startIcon={<PlayIcon />}
                onClick={handleExecute}
                disabled={isExecuting || selectedRepos.length === 0}
                sx={{ borderRadius: '12px 0 12px 12px' }}
              >
                Execute
              </Button>
              <Button
                fullWidth
                variant="outlined"
                color="error"
                startIcon={<StopIcon />}
                onClick={handleStop}
                disabled={!isExecuting}
                sx={{ borderRadius: '12px 0 12px 12px' }}
              >
                Stop
              </Button>
            </Box>

            <Button
              fullWidth
              variant="outlined"
              color="info"
              onClick={handleTestConnection}
              disabled={isExecuting}
              sx={{
                borderRadius: '12px 0 12px 12px',
                '& .MuiButton-startIcon': { mr: 0.5 }
              }}
              startIcon={<span style={{ fontSize: '1.2rem' }}>🔌</span>}
            >
              Test Socket Connection
            </Button>
          </Box>

          {/* Right panel - Console output */}
          <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                p: 1,
                borderBottom: 1,
                borderColor: 'divider',
                bgcolor: 'background.paper'
              }}
            >
              <Box display="flex" alignItems="center" gap={1}>
                <TerminalIcon fontSize="small" />
                <Typography variant="subtitle2">Console Output</Typography>
                {executionStatus === 'running' && (
                  <CircularProgress size={16} sx={{ ml: 1 }} />
                )}
                <Chip
                  size="small"
                  label={socketConnected ? "Socket Connected" : "Socket Disconnected"}
                  color={socketConnected ? "success" : "error"}
                  sx={{ ml: 1, height: '20px', fontSize: '0.7rem' }}
                  onClick={handleTestConnection}
                />
              </Box>
              <IconButton
                size="small"
                onClick={handleClearConsole}
                disabled={isExecuting}
              >
                <ClearIcon fontSize="small" />
              </IconButton>
            </Box>

            <Box
              sx={{
                flex: 1,
                bgcolor: '#1e1e1e',
                color: '#ffffff',
                fontFamily: 'monospace',
                fontSize: '14px',
                p: 2,
                overflow: 'auto',
                display: 'flex',
                flexDirection: 'column'
              }}
            >
              {error && executionStatus === 'failed' && (
                <Alert
                  severity="error"
                  sx={{
                    mb: 2,
                    fontFamily: 'inherit',
                    '& .MuiAlert-message': {
                      whiteSpace: 'pre-wrap',
                      wordBreak: 'break-word'
                    }
                  }}
                  action={
                    <Button color="inherit" size="small" onClick={handleRetry}>
                      Retry
                    </Button>
                  }
                >
                  <Typography variant="subtitle2" component="div" gutterBottom>
                    Agent Execution Failed
                  </Typography>
                  {error}
                </Alert>
              )}

              {showTimeoutWarning && executionStatus === 'running' && (
                <Alert
                  severity="warning"
                  sx={{
                    mb: 2,
                    fontFamily: 'inherit'
                  }}
                  action={
                    <>
                      <Button color="inherit" size="small" onClick={handleStop} sx={{ mr: 1 }}>
                        Stop
                      </Button>
                      <Button color="inherit" size="small" onClick={handleRetry}>
                        Retry
                      </Button>
                    </>
                  }
                >
                  <Typography variant="subtitle2" component="div" gutterBottom>
                    Execution Taking Too Long
                  </Typography>
                  The agent execution is taking longer than expected. This might indicate an error in the agent.
                  You can stop and retry the execution.
                </Alert>
              )}

              {consoleOutput.map((entry, index) => (
                <Box
                  key={index}
                  sx={{
                    mb: 0.5,
                    color: getMessageColor(entry.type),
                    wordBreak: 'break-word'
                  }}
                >
                  <span style={{ color: '#888888' }}>[{entry.timestamp}]</span> <span style={{ whiteSpace: 'pre-wrap' }}>{entry.message}</span>
                </Box>
              ))}
              <div ref={consoleEndRef} />
            </Box>

            {isExecuting && (
              <LinearProgress sx={{ position: 'absolute', bottom: 0, left: 0, right: 0 }} />
            )}
          </Box>
        </Box>
      </DialogContent>
    </Dialog>
  );
};

export default BugFixConsole;