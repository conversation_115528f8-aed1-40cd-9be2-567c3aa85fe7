import React, { useState, useEffect, useContext } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction,
  IconButton,
  Chip,
  Autocomplete,
  TextField,
  MenuItem,
  Alert,
  Card,
  CardContent,
  Grid,
  Divider,
  FormControlLabel,
  Checkbox
} from '@mui/material';
import {
  Add as AddIcon,
  Remove as RemoveIcon,
  Code as CodeIcon,
  Storage as StorageIcon,
  Star as StarIcon,
  StarBorder as StarBorderIcon,
  Launch as LaunchIcon
} from '@mui/icons-material';
import { AuthContext } from '../App';
import { 
  getFunctionalArea,
  linkProjectToFunctionalArea,
  unlinkProjectFromFunctionalArea,
  linkRepositoryToFunctionalArea,
  unlinkRepositoryFromFunctionalArea,
  listProjects,
  listDiscoveredRepositories
} from '../services/api';

const PROJECT_ROLES = [
  'backend',
  'frontend', 
  'mobile-app',
  'api',
  'web-app',
  'admin-panel',
  'documentation',
  'testing'
];

const REPOSITORY_ROLES = [
  'backend-api',
  'frontend-web',
  'mobile-ios',
  'mobile-android',
  'shared-lib',
  'infrastructure',
  'documentation',
  'testing',
  'deployment'
];

const FunctionalAreaManagementDialog = ({ open, onClose, functionalArea, onUpdate }) => {
  const { authState } = useContext(AuthContext);
  const [activeTab, setActiveTab] = useState(0);
  const [projects, setProjects] = useState([]);
  const [repositories, setRepositories] = useState([]);
  const [allProjects, setAllProjects] = useState([]);
  const [allRepositories, setAllRepositories] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Form state for adding new links
  const [selectedProject, setSelectedProject] = useState(null);
  const [selectedRepository, setSelectedRepository] = useState(null);
  const [projectRole, setProjectRole] = useState('');
  const [repositoryRole, setRepositoryRole] = useState('');
  const [isPrimaryProject, setIsPrimaryProject] = useState(false);
  const [isPrimaryRepository, setIsPrimaryRepository] = useState(false);

  useEffect(() => {
    if (open && functionalArea) {
      fetchAreaDetails();
      fetchAvailableResources();
    }
  }, [open, functionalArea]);

  const fetchAreaDetails = async () => {
    try {
      setLoading(true);
      const response = await getFunctionalArea(authState.tenantId, functionalArea.id);
      const area = response.functional_area;
      setProjects(area.projects || []);
      setRepositories(area.repositories || []);
      setError(null);
    } catch (err) {
      console.error('Error fetching area details:', err);
      setError('Failed to load functional area details');
    } finally {
      setLoading(false);
    }
  };

  const fetchAvailableResources = async () => {
    try {
      // Fetch all projects
      const projectsResponse = await listProjects(authState.tenantId);
      setAllProjects(projectsResponse.projects || []);

      // Fetch all repositories
      const reposResponse = await listDiscoveredRepositories(authState.tenantId);
      setAllRepositories(reposResponse.repositories || []);
    } catch (err) {
      console.error('Error fetching available resources:', err);
    }
  };

  const handleLinkProject = async () => {
    if (!selectedProject) return;

    try {
      await linkProjectToFunctionalArea(authState.tenantId, functionalArea.id, {
        project_key: selectedProject.key,
        project_role: projectRole,
        is_primary: isPrimaryProject
      });

      // Reset form
      setSelectedProject(null);
      setProjectRole('');
      setIsPrimaryProject(false);
      
      // Refresh data
      fetchAreaDetails();
      onUpdate?.();
    } catch (err) {
      console.error('Error linking project:', err);
      setError('Failed to link project');
    }
  };

  const handleUnlinkProject = async (projectKey) => {
    try {
      await unlinkProjectFromFunctionalArea(authState.tenantId, functionalArea.id, projectKey);
      fetchAreaDetails();
      onUpdate?.();
    } catch (err) {
      console.error('Error unlinking project:', err);
      setError('Failed to unlink project');
    }
  };

  const handleLinkRepository = async () => {
    if (!selectedRepository) return;

    try {
      await linkRepositoryToFunctionalArea(authState.tenantId, functionalArea.id, {
        repository_id: selectedRepository.id,
        repository_role: repositoryRole,
        is_primary: isPrimaryRepository
      });

      // Reset form
      setSelectedRepository(null);
      setRepositoryRole('');
      setIsPrimaryRepository(false);
      
      // Refresh data
      fetchAreaDetails();
      onUpdate?.();
    } catch (err) {
      console.error('Error linking repository:', err);
      setError('Failed to link repository');
    }
  };

  const handleUnlinkRepository = async (repositoryId) => {
    try {
      await unlinkRepositoryFromFunctionalArea(authState.tenantId, functionalArea.id, repositoryId);
      fetchAreaDetails();
      onUpdate?.();
    } catch (err) {
      console.error('Error unlinking repository:', err);
      setError('Failed to unlink repository');
    }
  };

  const getAvailableProjects = () => {
    const linkedProjectKeys = projects.map(p => p.project_key);
    return allProjects.filter(p => !linkedProjectKeys.includes(p.key));
  };

  const getAvailableRepositories = () => {
    const linkedRepoIds = repositories.map(r => r.repository_id);
    return allRepositories.filter(r => !linkedRepoIds.includes(r.id));
  };

  if (!functionalArea) return null;

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{ sx: { borderRadius: '12px 0 12px 12px', minHeight: '70vh' } }}
    >
      <DialogTitle>
        <Box>
          <Typography variant="h6">{functionalArea.name}</Typography>
          <Typography variant="body2" color="text.secondary">
            Manage projects and repositories
          </Typography>
        </Box>
      </DialogTitle>

      <DialogContent>
        {error && (
          <Alert severity="error" sx={{ mb: 2, borderRadius: '12px 0 12px 12px' }}>
            {error}
          </Alert>
        )}

        <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
          <Tabs value={activeTab} onChange={(e, value) => setActiveTab(value)}>
            <Tab 
              label={
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <CodeIcon fontSize="small" />
                  Projects ({projects.length})
                </Box>
              }
            />
            <Tab 
              label={
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <StorageIcon fontSize="small" />
                  Repositories ({repositories.length})
                </Box>
              }
            />
          </Tabs>
        </Box>

        {/* Projects Tab */}
        {activeTab === 0 && (
          <Box>
            {/* Add Project Section */}
            <Card sx={{ mb: 3, borderRadius: '12px 0 12px 12px' }}>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 2 }}>
                  Link Project
                </Typography>
                <Grid container spacing={2} alignItems="center">
                  <Grid item xs={12} md={4}>
                    <Autocomplete
                      options={getAvailableProjects()}
                      getOptionLabel={(option) => `${option.key} - ${option.name}`}
                      value={selectedProject}
                      onChange={(e, value) => setSelectedProject(value)}
                      renderInput={(params) => (
                        <TextField {...params} label="Select Project" size="small" />
                      )}
                      disabled={getAvailableProjects().length === 0}
                    />
                  </Grid>
                  <Grid item xs={12} md={3}>
                    <TextField
                      select
                      label="Role"
                      size="small"
                      fullWidth
                      value={projectRole}
                      onChange={(e) => setProjectRole(e.target.value)}
                    >
                      <MenuItem value="">None</MenuItem>
                      {PROJECT_ROLES.map((role) => (
                        <MenuItem key={role} value={role}>
                          {role.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                        </MenuItem>
                      ))}
                    </TextField>
                  </Grid>
                  <Grid item xs={12} md={3}>
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={isPrimaryProject}
                          onChange={(e) => setIsPrimaryProject(e.target.checked)}
                        />
                      }
                      label="Primary"
                    />
                  </Grid>
                  <Grid item xs={12} md={2}>
                    <Button
                      variant="contained"
                      startIcon={<AddIcon />}
                      onClick={handleLinkProject}
                      disabled={!selectedProject}
                      size="small"
                      fullWidth
                    >
                      Link
                    </Button>
                  </Grid>
                </Grid>
                {getAvailableProjects().length === 0 && (
                  <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                    All projects are already linked to this functional area
                  </Typography>
                )}
              </CardContent>
            </Card>

            {/* Linked Projects List */}
            <Typography variant="h6" sx={{ mb: 2 }}>
              Linked Projects ({projects.length})
            </Typography>
            {projects.length === 0 ? (
              <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', py: 4 }}>
                No projects linked yet
              </Typography>
            ) : (
              <List>
                {projects.map((project, index) => (
                  <React.Fragment key={project.project_key}>
                    <ListItem>
                      <ListItemIcon>
                        {project.is_primary ? <StarIcon color="primary" /> : <CodeIcon />}
                      </ListItemIcon>
                      <ListItemText
                        primary={
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Typography variant="subtitle1">{project.project_key}</Typography>
                            {project.is_primary && (
                              <Chip label="Primary" size="small" color="primary" />
                            )}
                            {project.project_role && (
                              <Chip 
                                label={project.project_role.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())} 
                                size="small" 
                                variant="outlined" 
                              />
                            )}
                          </Box>
                        }
                        secondary={
                          project.linked_at ? `Linked: ${new Date(project.linked_at).toLocaleDateString()}` : ''
                        }
                      />
                      <ListItemSecondaryAction>
                        <IconButton
                          size="small"
                          href={`/projects/${project.project_key}`}
                          target="_blank"
                          sx={{ mr: 1 }}
                        >
                          <LaunchIcon />
                        </IconButton>
                        <IconButton
                          size="small"
                          onClick={() => handleUnlinkProject(project.project_key)}
                          color="error"
                        >
                          <RemoveIcon />
                        </IconButton>
                      </ListItemSecondaryAction>
                    </ListItem>
                    {index < projects.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            )}
          </Box>
        )}

        {/* Repositories Tab */}
        {activeTab === 1 && (
          <Box>
            {/* Add Repository Section */}
            <Card sx={{ mb: 3, borderRadius: '12px 0 12px 12px' }}>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 2 }}>
                  Link Repository
                </Typography>
                <Grid container spacing={2} alignItems="center">
                  <Grid item xs={12} md={4}>
                    <Autocomplete
                      options={getAvailableRepositories()}
                      getOptionLabel={(option) => option.full_name || option.name}
                      value={selectedRepository}
                      onChange={(e, value) => setSelectedRepository(value)}
                      renderInput={(params) => (
                        <TextField {...params} label="Select Repository" size="small" />
                      )}
                      disabled={getAvailableRepositories().length === 0}
                    />
                  </Grid>
                  <Grid item xs={12} md={3}>
                    <TextField
                      select
                      label="Role"
                      size="small"
                      fullWidth
                      value={repositoryRole}
                      onChange={(e) => setRepositoryRole(e.target.value)}
                    >
                      <MenuItem value="">None</MenuItem>
                      {REPOSITORY_ROLES.map((role) => (
                        <MenuItem key={role} value={role}>
                          {role.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                        </MenuItem>
                      ))}
                    </TextField>
                  </Grid>
                  <Grid item xs={12} md={3}>
                    <FormControlLabel
                      control={
                        <Checkbox
                          checked={isPrimaryRepository}
                          onChange={(e) => setIsPrimaryRepository(e.target.checked)}
                        />
                      }
                      label="Primary"
                    />
                  </Grid>
                  <Grid item xs={12} md={2}>
                    <Button
                      variant="contained"
                      startIcon={<AddIcon />}
                      onClick={handleLinkRepository}
                      disabled={!selectedRepository}
                      size="small"
                      fullWidth
                    >
                      Link
                    </Button>
                  </Grid>
                </Grid>
                {getAvailableRepositories().length === 0 && (
                  <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                    All repositories are already linked to this functional area
                  </Typography>
                )}
              </CardContent>
            </Card>

            {/* Linked Repositories List */}
            <Typography variant="h6" sx={{ mb: 2 }}>
              Linked Repositories ({repositories.length})
            </Typography>
            {repositories.length === 0 ? (
              <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', py: 4 }}>
                No repositories linked yet
              </Typography>
            ) : (
              <List>
                {repositories.map((repo, index) => (
                  <React.Fragment key={repo.repository_id}>
                    <ListItem>
                      <ListItemIcon>
                        {repo.is_primary ? <StarIcon color="primary" /> : <StorageIcon />}
                      </ListItemIcon>
                      <ListItemText
                        primary={
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Typography variant="subtitle1">
                              {repo.repository_full_name || repo.repository_name}
                            </Typography>
                            {repo.is_primary && (
                              <Chip label="Primary" size="small" color="primary" />
                            )}
                            {repo.repository_role && (
                              <Chip 
                                label={repo.repository_role.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())} 
                                size="small" 
                                variant="outlined" 
                              />
                            )}
                          </Box>
                        }
                        secondary={
                          <Box>
                            {repo.default_branch && (
                              <Typography variant="caption" display="block">
                                Default branch: {repo.default_branch}
                              </Typography>
                            )}
                            {repo.linked_at && (
                              <Typography variant="caption" display="block">
                                Linked: {new Date(repo.linked_at).toLocaleDateString()}
                              </Typography>
                            )}
                          </Box>
                        }
                      />
                      <ListItemSecondaryAction>
                        {repo.clone_url && (
                          <IconButton
                            size="small"
                            href={repo.clone_url}
                            target="_blank"
                            sx={{ mr: 1 }}
                          >
                            <LaunchIcon />
                          </IconButton>
                        )}
                        <IconButton
                          size="small"
                          onClick={() => handleUnlinkRepository(repo.repository_id)}
                          color="error"
                        >
                          <RemoveIcon />
                        </IconButton>
                      </ListItemSecondaryAction>
                    </ListItem>
                    {index < repositories.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            )}
          </Box>
        )}
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose}>Close</Button>
      </DialogActions>
    </Dialog>
  );
};

export default FunctionalAreaManagementDialog;