import React, { useState, useEffect } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Typography,
  Box,
  IconButton,
  Alert
} from '@mui/material';
import { getUserProjectPath, saveUserProjectPath, deleteUserProjectPath } from '../services/api';

// Close icon as component
const CloseIcon = () => <span>✕</span>;

const ProjectConfigDialog = ({ open, onClose, projectKey, projectName, tenantId }) => {
  const [localPath, setLocalPath] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);
  const [originalPath, setOriginalPath] = useState('');

  useEffect(() => {
    if (open && projectKey) {
      fetchCurrentPath();
    }
  }, [open, projectKey]);

  const fetchCurrentPath = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await getUserProjectPath(tenantId, projectKey);
      setLocalPath(response.local_path || '');
      setOriginalPath(response.local_path || '');
    } catch (err) {
      if (err.response?.status !== 404) {
        setError('Failed to load current path');
        console.error('Error fetching project path:', err);
      }
      setLocalPath('');
      setOriginalPath('');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    if (!localPath.trim()) {
      setError('Please enter a valid path');
      return;
    }

    try {
      setLoading(true);
      setError(null);
      await saveUserProjectPath(tenantId, projectKey, localPath);
      setSuccess(true);
      setOriginalPath(localPath);
      setTimeout(() => {
        setSuccess(false);
        onClose();
      }, 1500);
    } catch (err) {
      setError('Failed to save project path');
      console.error('Error saving project path:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    try {
      setLoading(true);
      setError(null);
      await deleteUserProjectPath(tenantId, projectKey);
      setLocalPath('');
      setOriginalPath('');
      setSuccess(true);
      setTimeout(() => {
        setSuccess(false);
      }, 1500);
    } catch (err) {
      setError('Failed to delete project path');
      console.error('Error deleting project path:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setError(null);
    setSuccess(false);
    onClose();
  };

  return (
    <Dialog 
      open={open} 
      onClose={handleClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: '12px 0 12px 12px'
        }
      }}
    >
      <DialogTitle sx={{ m: 0, p: 2 }}>
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <Typography variant="h6">
            Configure Project Path
          </Typography>
          <IconButton
            aria-label="close"
            onClick={handleClose}
            sx={{
              color: (theme) => theme.palette.grey[500],
            }}
          >
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>
      <DialogContent dividers>
        <Typography variant="body1" paragraph>
          Set the local repository path for <strong>{projectName}</strong> ({projectKey})
        </Typography>
        
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}
        
        {success && (
          <Alert severity="success" sx={{ mb: 2 }}>
            Path saved successfully!
          </Alert>
        )}
        
        <TextField
          fullWidth
          label="Local Repository Path"
          placeholder="/Users/<USER>/projects/repository"
          value={localPath}
          onChange={(e) => setLocalPath(e.target.value)}
          disabled={loading}
          variant="outlined"
          margin="normal"
          helperText="Enter the full path to your local repository for this project"
          InputProps={{
            sx: {
              borderRadius: '12px 0 12px 12px'
            }
          }}
        />
      </DialogContent>
      <DialogActions sx={{ p: 2 }}>
        {originalPath && (
          <Button 
            onClick={handleDelete} 
            disabled={loading}
            color="error"
            sx={{ mr: 'auto' }}
          >
            Remove Path
          </Button>
        )}
        <Button onClick={handleClose} disabled={loading}>
          Cancel
        </Button>
        <Button 
          onClick={handleSave} 
          disabled={loading || !localPath.trim()}
          variant="contained"
          sx={{
            borderRadius: '12px 0 12px 12px'
          }}
        >
          Save
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ProjectConfigDialog;