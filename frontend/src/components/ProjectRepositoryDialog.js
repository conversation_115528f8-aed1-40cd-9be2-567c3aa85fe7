import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Checkbox,
  Typography,
  Alert,
  CircularProgress,
  Box,
  Chip
} from '@mui/material';
import { 
  getProjectRepositories,
  linkRepositoryToProject,
  unlinkRepositoryFromProject,
  listRepositories
} from '../services/api';

const ProjectRepositoryDialog = ({ open, onClose, projectKey, projectName, tenantId }) => {
  const [allRepositories, setAllRepositories] = useState([]);
  const [projectRepositories, setProjectRepositories] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    if (open && projectKey) {
      fetchData();
    }
  }, [open, projectKey]);

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const [allReposData, projectReposData] = await Promise.all([
        listRepositories(tenantId),
        getProjectRepositories(tenantId, projectKey)
      ]);
      
      setAllRepositories(allReposData.repositories || []);
      setProjectRepositories(projectReposData.repositories || []);
    } catch (err) {
      console.error('Error fetching repository data:', err);
      setError('Failed to load repositories');
    } finally {
      setLoading(false);
    }
  };

  const isRepositoryLinked = (repoId) => {
    return projectRepositories.some(repo => repo.id === repoId);
  };

  const isPrimaryRepository = (repoId) => {
    const linkedRepo = projectRepositories.find(repo => repo.id === repoId);
    return linkedRepo ? linkedRepo.is_primary : false;
  };

  const handleToggleRepository = async (repository) => {
    try {
      setSaving(true);
      setError(null);
      
      if (isRepositoryLinked(repository.id)) {
        await unlinkRepositoryFromProject(tenantId, projectKey, repository.id);
      } else {
        await linkRepositoryToProject(tenantId, projectKey, repository.id, {
          is_primary: projectRepositories.length === 0 // Make first repo primary
        });
      }
      
      await fetchData();
    } catch (err) {
      console.error('Error toggling repository:', err);
      setError('Failed to update repository link');
    } finally {
      setSaving(false);
    }
  };

  const handleSetPrimary = async (repository) => {
    try {
      setSaving(true);
      setError(null);
      
      await linkRepositoryToProject(tenantId, projectKey, repository.id, {
        is_primary: true
      });
      
      await fetchData();
    } catch (err) {
      console.error('Error setting primary repository:', err);
      setError('Failed to set primary repository');
    } finally {
      setSaving(false);
    }
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: { borderRadius: '12px 0 12px 12px' }
      }}
    >
      <DialogTitle>
        <Typography variant="h6">
          Manage Repositories for {projectName}
        </Typography>
        <Typography variant="caption" color="text.secondary">
          Project Key: {projectKey}
        </Typography>
      </DialogTitle>
      <DialogContent dividers>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}
        
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
            <CircularProgress />
          </Box>
        ) : (
          <List>
            {allRepositories.map((repository) => {
              const isLinked = isRepositoryLinked(repository.id);
              const isPrimary = isPrimaryRepository(repository.id);
              
              return (
                <ListItem key={repository.id}>
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="subtitle1">
                          {repository.display_name}
                        </Typography>
                        <Chip
                          label={repository.repository_type}
                          size="small"
                          color={repository.repository_type === 'github' ? 'primary' : 'secondary'}
                        />
                        {isPrimary && (
                          <Chip
                            label="Primary"
                            size="small"
                            color="success"
                          />
                        )}
                      </Box>
                    }
                    secondary={repository.remote_url}
                  />
                  <ListItemSecondaryAction>
                    {isLinked && !isPrimary && (
                      <Button
                        size="small"
                        onClick={() => handleSetPrimary(repository)}
                        disabled={saving}
                        sx={{ mr: 2 }}
                      >
                        Set as Primary
                      </Button>
                    )}
                    <Checkbox
                      edge="end"
                      checked={isLinked}
                      onChange={() => handleToggleRepository(repository)}
                      disabled={saving}
                    />
                  </ListItemSecondaryAction>
                </ListItem>
              );
            })}
          </List>
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Close</Button>
      </DialogActions>
    </Dialog>
  );
};

export default ProjectRepositoryDialog;