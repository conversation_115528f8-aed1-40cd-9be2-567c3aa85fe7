import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Typography,
  Box,
  Alert,
  CircularProgress,
  FormControlLabel,
  Checkbox,
  Divider,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Grid
} from '@mui/material';

// Icons
const ExpandMoreIcon = () => <span>▼</span>;
const SettingsIcon = () => <span>⚙️</span>;

const RepositoryAgentConfigDialog = ({ open, onClose, repository, onConfigSaved }) => {
  const [instruction, setInstruction] = useState('');
  const [description, setDescription] = useState('');
  const [enabledTools, setEnabledTools] = useState([]);
  const [toolConfigs, setToolConfigs] = useState({});
  const [availableTools, setAvailableTools] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [expandedTool, setExpandedTool] = useState(null);

  useEffect(() => {
    if (repository) {
      setInstruction(repository.agent_instruction || '');
      setDescription(repository.agent_description || '');
      setEnabledTools(repository.enabled_tools || []);
      setToolConfigs(repository.tool_configs || {});
    }
  }, [repository]);

  useEffect(() => {
    if (open) {
      // Load available tools when dialog opens
      loadAvailableTools();
    }
  }, [open]);

  const loadAvailableTools = async () => {
    try {
      const { getAvailableTools } = await import('../services/api');
      const tenantId = localStorage.getItem('tenantId');
      const response = await getAvailableTools(tenantId);
      setAvailableTools(response.tools || []);
    } catch (err) {
      console.error('Error loading available tools:', err);
    }
  };

  const handleToolToggle = (toolId) => {
    setEnabledTools(prev => {
      if (prev.includes(toolId)) {
        // Remove tool config when disabling
        setToolConfigs(configs => {
          const newConfigs = { ...configs };
          delete newConfigs[toolId];
          return newConfigs;
        });
        return prev.filter(id => id !== toolId);
      } else {
        // Initialize empty config when enabling
        setToolConfigs(configs => ({
          ...configs,
          [toolId]: {}
        }));
        return [...prev, toolId];
      }
    });
  };

  const handleToolConfigChange = (toolId, field, value) => {
    setToolConfigs(prev => ({
      ...prev,
      [toolId]: {
        ...prev[toolId],
        [field]: value
      }
    }));
  };

  const renderConfigField = (toolId, fieldName, fieldSchema) => {
    const value = toolConfigs[toolId]?.[fieldName] || fieldSchema.default || '';
    
    if (fieldSchema.type === 'boolean') {
      return (
        <FormControlLabel
          control={
            <Checkbox
              checked={value}
              onChange={(e) => handleToolConfigChange(toolId, fieldName, e.target.checked)}
            />
          }
          label={fieldSchema.title || fieldName}
        />
      );
    }
    
    return (
      <TextField
        fullWidth
        label={fieldSchema.title || fieldName}
        value={value}
        onChange={(e) => handleToolConfigChange(toolId, fieldName, e.target.value)}
        helperText={fieldSchema.description}
        type={fieldSchema.type === 'integer' ? 'number' : 'text'}
        inputProps={{
          min: fieldSchema.minimum,
          max: fieldSchema.maximum
        }}
        sx={{ mb: 2 }}
      />
    );
  };

  const validateToolConfigs = () => {
    // Validate required fields for each enabled tool
    for (const toolId of enabledTools) {
      const tool = availableTools.find(t => t.id === toolId);
      if (tool && tool.config_schema?.required) {
        const toolConfig = toolConfigs[toolId] || {};
        for (const requiredField of tool.config_schema.required) {
          if (!toolConfig[requiredField]) {
            setError(`${tool.name}: ${tool.config_schema.properties[requiredField]?.title || requiredField} is required`);
            return false;
          }
        }
      }
    }
    return true;
  };

  const handleSave = async () => {
    try {
      setLoading(true);
      setError(null);

      // Validate tool configurations
      if (!validateToolConfigs()) {
        setLoading(false);
        return;
      }

      const { updateRepositoryAgentConfig } = await import('../services/api');
      
      // Get tenant ID from localStorage since we can't use useContext in this scope
      const tenantId = localStorage.getItem('tenantId');
      
      await updateRepositoryAgentConfig(tenantId, repository.id, {
        agent_instruction: instruction.trim() || null,
        agent_description: description.trim() || null,
        enabled_tools: enabledTools,
        tool_configs: toolConfigs
      });

      if (onConfigSaved) {
        onConfigSaved();
      }
      
      onClose();
    } catch (err) {
      console.error('Error saving agent configuration:', err);
      setError(err.response?.data?.error || 'Failed to save configuration');
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    setError(null);
    onClose();
  };

  if (!repository) {
    return null;
  }

  return (
    <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
      <DialogTitle>
        Configure AI Agent for {repository.name}
      </DialogTitle>
      <DialogContent>
        <Box sx={{ py: 2 }}>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            Configure how the AI agent should behave when analyzing bugs in this repository.
          </Typography>

          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          <TextField
            fullWidth
            label="Agent Instruction"
            multiline
            rows={4}
            value={instruction}
            onChange={(e) => setInstruction(e.target.value)}
            sx={{ mb: 3 }}
            placeholder="You are an agent specialized in handling bugs in the [repository name] repository."
            helperText="Customize the instruction given to the AI agent. This will override the default instruction."
          />

          <TextField
            fullWidth
            label="Agent Description"
            multiline
            rows={6}
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            placeholder="[Repository name] repository contains code for [description].
The repo is located at: [path]
Source type: [type]
You are provided with a claude code tool which is an AI code assistant that can help you write code."
            helperText="Customize the description given to the AI agent. This will override the default description."
          />

          <Divider sx={{ my: 3 }} />

          <Typography variant="subtitle1" sx={{ mb: 2 }}>
            Available Tools
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            Select additional tools that the AI agent can use when working with this repository.
          </Typography>

          <Box>
            {availableTools.map(tool => (
              <Accordion 
                key={tool.id}
                expanded={expandedTool === tool.id}
                onChange={(_, isExpanded) => setExpandedTool(isExpanded ? tool.id : null)}
                sx={{ mb: 1 }}
              >
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={enabledTools.includes(tool.id)}
                        onChange={() => handleToolToggle(tool.id)}
                        onClick={(e) => e.stopPropagation()}
                      />
                    }
                    label={
                      <Box>
                        <Typography variant="body1">{tool.name}</Typography>
                        <Typography variant="body2" color="text.secondary">
                          {tool.description}
                        </Typography>
                      </Box>
                    }
                    onClick={(e) => e.stopPropagation()}
                  />
                </AccordionSummary>
                {enabledTools.includes(tool.id) && tool.config_schema?.properties && (
                  <AccordionDetails>
                    <Box sx={{ p: 2 }}>
                      <Typography variant="subtitle2" sx={{ mb: 2 }}>
                        <SettingsIcon /> Tool Configuration
                      </Typography>
                      <Grid container spacing={2}>
                        {Object.entries(tool.config_schema.properties).map(([fieldName, fieldSchema]) => (
                          <Grid item xs={12} key={fieldName}>
                            {renderConfigField(tool.id, fieldName, fieldSchema)}
                          </Grid>
                        ))}
                      </Grid>
                    </Box>
                  </AccordionDetails>
                )}
              </Accordion>
            ))}
          </Box>
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={handleClose} disabled={loading}>
          Cancel
        </Button>
        <Button 
          onClick={handleSave} 
          variant="contained" 
          disabled={loading}
          startIcon={loading ? <CircularProgress size={16} /> : null}
        >
          {loading ? 'Saving...' : 'Save Configuration'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default RepositoryAgentConfigDialog;