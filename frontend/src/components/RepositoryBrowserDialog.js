import React, { useState, useEffect, useContext } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  Box,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  Checkbox,
  Button,
  Typography,
  Chip,
  TextField,
  InputAdornment,
  CircularProgress,
  Alert
} from '@mui/material';
import { AuthContext } from '../App';
import { listAvailableRepositoriesForProject, linkRepositoryToProject } from '../services/api';

// Icons
const SearchIcon = () => <span>🔍</span>;
const GitHubIcon = () => <span>🐙</span>;
const BitbucketIcon = () => <span>🪣</span>;
const GitLabIcon = () => <span>🦊</span>;

const RepositoryBrowserDialog = ({ open, onClose, projectKey, tenantId, onRepositoriesLinked }) => {
  const { authState } = useContext(AuthContext);
  const effectiveTenantId = tenantId || authState.tenantId;
  const [repositories, setRepositories] = useState([]);
  const [selectedRepos, setSelectedRepos] = useState(new Set());
  const [primaryRepo, setPrimaryRepo] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    console.log('Dialog state:', { open, projectKey, effectiveTenantId });
    if (open && projectKey) {
      loadRepositories();
    }
  }, [open, projectKey, effectiveTenantId]);

  const loadRepositories = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Debug log
      console.log('Loading repositories for project:', projectKey, 'tenant:', effectiveTenantId);
      
      const response = await listAvailableRepositoriesForProject(effectiveTenantId, projectKey);
      setRepositories(response.repositories || []);
      
      // Pre-select already linked repositories
      const linked = response.repositories?.filter(r => r.is_linked) || [];
      setSelectedRepos(new Set(linked.map(r => r.id)));
    } catch (err) {
      console.error('Error loading repositories:', err);
      setError(`Failed to load repositories: ${err.response?.data?.error || err.message}`);
    } finally {
      setLoading(false);
    }
  };

  const handleToggleRepository = (repoId) => {
    const newSelected = new Set(selectedRepos);
    if (newSelected.has(repoId)) {
      newSelected.delete(repoId);
      if (primaryRepo === repoId) {
        setPrimaryRepo(null);
      }
    } else {
      newSelected.add(repoId);
    }
    setSelectedRepos(newSelected);
  };

  const handleSetPrimary = (repoId) => {
    if (selectedRepos.has(repoId)) {
      setPrimaryRepo(repoId);
    }
  };

  const handleSave = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Get currently linked repos
      const currentlyLinked = repositories.filter(r => r.is_linked).map(r => r.id);
      
      // Determine what to link and unlink
      const toLink = Array.from(selectedRepos).filter(id => !currentlyLinked.includes(id));
      
      // Link new repositories
      for (const repoId of toLink) {
        await linkRepositoryToProject(effectiveTenantId, projectKey, repoId, {
          is_primary: repoId === primaryRepo
        });
      }
      
      if (onRepositoriesLinked) {
        onRepositoriesLinked();
      }
      onClose();
    } catch (err) {
      setError(`Failed to save changes: ${err.response?.data?.error || err.message}`);
    } finally {
      setLoading(false);
    }
  };

  const getSourceIcon = (sourceType) => {
    switch (sourceType) {
      case 'github':
        return <GitHubIcon />;
      case 'bitbucket':
        return <BitbucketIcon />;
      case 'gitlab':
        return <GitLabIcon />;
      default:
        return null;
    }
  };

  const filteredRepositories = repositories.filter(repo => 
    repo.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    repo.full_name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    repo.description?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  return (
    <Dialog 
      open={open} 
      onClose={onClose} 
      maxWidth="md" 
      fullWidth
      PaperProps={{
        sx: { 
          minHeight: '400px',
          borderRadius: '12px 0 12px 12px' 
        }
      }}
    >
      <DialogTitle>
        Link Repositories to Project: {projectKey}
      </DialogTitle>
      <DialogContent sx={{ pb: 2 }}>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
            {error}
          </Alert>
        )}
        
        <Box sx={{ mb: 2 }}>
          <TextField
            fullWidth
            placeholder="Search repositories..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
          />
        </Box>

        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
            <CircularProgress />
          </Box>
        ) : filteredRepositories.length === 0 ? (
          <Box sx={{ py: 4, textAlign: 'center' }}>
            <Typography color="text.secondary">
              {searchQuery ? 'No repositories match your search' : 'No repositories available'}
            </Typography>
            {!searchQuery && repositories.length === 0 && (
              <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                Please check your repository sources configuration
              </Typography>
            )}
          </Box>
        ) : (
          <List sx={{ maxHeight: 400, overflow: 'auto' }}>
            {filteredRepositories.map((repo) => (
              <ListItem key={repo.id} divider>
                <ListItemIcon>
                  <Checkbox
                    edge="start"
                    checked={selectedRepos.has(repo.id)}
                    onChange={() => handleToggleRepository(repo.id)}
                  />
                </ListItemIcon>
                <ListItemIcon sx={{ minWidth: 30 }}>
                  {getSourceIcon(repo.source_type)}
                </ListItemIcon>
                <ListItemText
                  primary={
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Typography variant="subtitle2">
                        {repo.full_name || repo.name}
                      </Typography>
                      {repo.is_linked && (
                        <Chip label="Linked" size="small" color="success" />
                      )}
                      {primaryRepo === repo.id && (
                        <Chip label="Primary" size="small" color="primary" />
                      )}
                    </Box>
                  }
                  secondary={repo.description || 'No description'}
                />
                <ListItemSecondaryAction>
                  {selectedRepos.has(repo.id) && (
                    <Button
                      size="small"
                      variant={primaryRepo === repo.id ? "contained" : "outlined"}
                      onClick={() => handleSetPrimary(repo.id)}
                      disabled={repo.is_linked}
                    >
                      Primary
                    </Button>
                  )}
                </ListItemSecondaryAction>
              </ListItem>
            ))}
          </List>
        )}

        <Box sx={{ mt: 3, display: 'flex', justifyContent: 'space-between' }}>
          <Typography variant="caption" color="text.secondary">
            {selectedRepos.size} repositories selected
          </Typography>
          <Box sx={{ display: 'flex', gap: 2 }}>
            <Button onClick={onClose}>Cancel</Button>
            <Button 
              variant="contained" 
              onClick={handleSave}
              disabled={loading || selectedRepos.size === 0}
            >
              Save Changes
            </Button>
          </Box>
        </Box>
      </DialogContent>
    </Dialog>
  );
};

export default RepositoryBrowserDialog;