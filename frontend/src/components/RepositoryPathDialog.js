import React, { useState, useEffect } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Typography,
  Box,
  Alert
} from '@mui/material';
import { 
  getRepositoryLocalPath,
  setRepositoryLocalPath
} from '../services/api';

const RepositoryPathDialog = ({ open, onClose, repository, tenantId }) => {
  const [localPath, setLocalPath] = useState('');
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(false);

  useEffect(() => {
    if (open && repository) {
      fetchCurrentPath();
    }
  }, [open, repository]);

  const fetchCurrentPath = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await getRepositoryLocalPath(tenantId, repository.id);
      setLocalPath(response.local_path || '');
    } catch (err) {
      if (err.response?.status !== 404) {
        setError('Failed to load current path');
        console.error('Error fetching repository path:', err);
      }
      setLocalPath('');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    if (!localPath.trim()) {
      setError('Please enter a valid path');
      return;
    }

    try {
      setSaving(true);
      setError(null);
      await setRepositoryLocalPath(tenantId, repository.id, localPath);
      setSuccess(true);
      setTimeout(() => {
        setSuccess(false);
        onClose();
      }, 1500);
    } catch (err) {
      setError('Failed to save repository path');
      console.error('Error saving repository path:', err);
    } finally {
      setSaving(false);
    }
  };

  if (!repository) return null;

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: { borderRadius: '12px 0 12px 12px' }
      }}
    >
      <DialogTitle>
        <Typography variant="h6">
          Set Local Path
        </Typography>
      </DialogTitle>
      <DialogContent>
        <Box sx={{ mb: 2 }}>
          <Typography variant="subtitle1">
            {repository.display_name}
          </Typography>
          <Typography variant="caption" color="text.secondary">
            {repository.remote_url}
          </Typography>
        </Box>
        
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}
        
        {success && (
          <Alert severity="success" sx={{ mb: 2 }}>
            Path saved successfully!
          </Alert>
        )}
        
        <TextField
          fullWidth
          label="Local Repository Path"
          placeholder="/Users/<USER>/projects/repository"
          value={localPath}
          onChange={(e) => setLocalPath(e.target.value)}
          disabled={loading || saving}
          variant="outlined"
          margin="normal"
          helperText="Enter the full path to your local repository"
          InputProps={{
            sx: {
              borderRadius: '12px 0 12px 12px'
            }
          }}
        />
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} disabled={saving}>
          Cancel
        </Button>
        <Button
          onClick={handleSave}
          disabled={loading || saving || !localPath.trim()}
          variant="contained"
          sx={{
            borderRadius: '12px 0 12px 12px'
          }}
        >
          Save
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default RepositoryPathDialog;