import React, { useState, useEffect, useContext } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Button,
  Typography,
  Box,
  Alert
} from '@mui/material';
import { AuthContext } from '../App';
import { setUserRepositoryPath, setDirectRepositoryPath } from '../services/api';

const UserRepositoryPathDialog = ({ open, onClose, repository, projectKey, onPathSaved }) => {
  const { authState } = useContext(AuthContext);
  const [localPath, setLocalPath] = useState('');
  const [error, setError] = useState(null);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    if (open) {
      setLocalPath('');
      setError(null);
    }
  }, [open]);

  const handleSave = async () => {
    if (!localPath.trim()) {
      setError('Please enter a valid path');
      return;
    }

    try {
      setSaving(true);
      
      console.log('Saving path for repository:', repository.id, localPath);
      
      if (projectKey) {
        // If there's a project key, use the project-specific API
        await setUserRepositoryPath(authState.tenantId, projectKey, repository.id, localPath);
      } else {
        // If no project key, use the direct repository API
        await setDirectRepositoryPath(authState.tenantId, repository.id, localPath);
      }
      
      if (onPathSaved) {
        onPathSaved();
      }
      onClose();
    } catch (err) {
      setError('Failed to save repository path');
      console.error(err);
    } finally {
      setSaving(false);
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>Set Local Repository Path</DialogTitle>
      <DialogContent>
        <Box sx={{ mb: 2 }}>
          <Typography variant="body2" color="text.secondary">
            Repository: {repository?.full_name || repository?.name}
          </Typography>
        </Box>
        
        <TextField
          label="Local Path"
          fullWidth
          value={localPath}
          onChange={(e) => setLocalPath(e.target.value)}
          placeholder="/path/to/repository"
          helperText="Enter the full path to the repository on your local machine"
          sx={{ mb: 2 }}
        />
        
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button 
          onClick={handleSave} 
          variant="contained" 
          disabled={saving}
        >
          {saving ? 'Saving...' : 'Save'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default UserRepositoryPathDialog;