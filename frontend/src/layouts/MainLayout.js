import React, { useContext } from 'react';
import { Outlet, useNavigate, Link, useLocation } from 'react-router-dom';
import { AppBar, Toolbar, Typography, Box, IconButton, Tooltip, Avatar, Menu, MenuItem, Divider, ListItemIcon, Button } from '@mui/material';
import { AuthContext } from '../App'; // Adjust path if AuthContext is moved

// Mock Lucide React Icons because we don't want to install the actual dependency for tests
const Settings = () => <span>⚙️</span>;
const LogOut = () => <span>🚪</span>;
const HelpIcon = () => <span>❓</span>;
const BellIcon = () => <span>🔔</span>;
const SearchIcon = () => <span>🔍</span>;
const JiraIcon = () => <span>🔌</span>;

// Placeholder for Kasavu styled AppBar or custom top bar component
const KasavuAppBar = (props) => (
    <AppBar 
        position="sticky" 
        elevation={1} 
        sx={{ 
            backgroundColor: 'background.default', // --mk-mullappoo-white
            color: 'text.primary', // --mk-arecanut-brown-600 for text
            // Add subtle wave texture later if possible via sx or styled-component
        }}
        {...props} 
    />
);

const MainLayout = () => {
  const { authState, logout } = useContext(AuthContext);
  const navigate = useNavigate();
  const location = useLocation();
  const [anchorElUser, setAnchorElUser] = React.useState(null);

  const handleOpenUserMenu = (event) => setAnchorElUser(event.currentTarget);
  const handleCloseUserMenu = () => setAnchorElUser(null);

  const handleLogout = () => {
    logout();
    navigate('/login');
    handleCloseUserMenu();
  };

  return (
    <Box sx={{ display: 'flex', flexDirection: 'column', minHeight: '100vh' }}>
      <KasavuAppBar>
        <Toolbar sx={{ justifyContent: 'space-between' }}>
          <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1 }}>
            {/* Product Logo */}
            <Box 
              component="img"
              src="/assets/repair.png"
              alt="JIFFY BUG FIX Logo"
              sx={{ 
                height: 32, 
                mr: 1,
                display: 'inline-block'
              }}
            />
            
            {/* Product Name with link to home */}
            <Typography 
              variant="h6" 
              component={Link} 
              to="/" 
              sx={{ 
                fontWeight: 'bold', 
                textDecoration: 'none', 
                color: 'inherit',
                '&:hover': {
                  opacity: 0.8,
                },
                mr: 2,
                display: 'flex',
                alignItems: 'center'
              }}
            >
              JIFFY BUG FIX
            </Typography>
            
            {/* Tenant Badge - As per UI spec */}
            {authState.tenantId && (
                <Typography variant="caption" sx={{
                    mr: 3, 
                    px: 1, 
                    py: 0.5, 
                    backgroundColor: 'primary.main', 
                    color: 'primary.contrastText', 
                    borderRadius: 1, // Standard small radius for badge
                    // Consider Kasavu-cut if desired for badges too
                }}>
                    Tenant: {authState.tenantId.substring(0,8)}...
                </Typography>
            )}

            {/* Navigation Menu - Integrated into top bar */}
            <Box sx={{ display: 'flex' }}>
              <Button 
                component={Link} 
                to="/" 
                sx={{ 
                  color: 'text.primary',
                  '&:hover': { bgcolor: 'action.hover' },
                  textTransform: 'none',
                  fontWeight: location.pathname === '/' ? 'bold' : 'normal',
                  borderBottom: location.pathname === '/' ? 2 : 0,
                  borderColor: 'primary.main',
                  borderRadius: 0,
                  px: 1
                }}
              >
                Dashboard
              </Button>
              <Button 
                component={Link} 
                to="/projects" 
                sx={{ 
                  color: 'text.primary',
                  '&:hover': { bgcolor: 'action.hover' },
                  textTransform: 'none',
                  fontWeight: location.pathname === '/projects' ? 'bold' : 'normal',
                  borderBottom: location.pathname === '/projects' ? 2 : 0,
                  borderColor: 'primary.main',
                  borderRadius: 0,
                  px: 1
                }}
              >
                Projects
              </Button>
              <Button 
                component={Link} 
                to="/issues" 
                sx={{ 
                  color: 'text.primary',
                  '&:hover': { bgcolor: 'action.hover' },
                  textTransform: 'none',
                  fontWeight: location.pathname === '/issues' ? 'bold' : 'normal',
                  borderBottom: location.pathname === '/issues' ? 2 : 0,
                  borderColor: 'primary.main',
                  borderRadius: 0,
                  px: 1
                }}
              >
                Issues
              </Button>
              <Button 
                component={Link} 
                to="/repositories" 
                sx={{ 
                  color: 'text.primary',
                  '&:hover': { bgcolor: 'action.hover' },
                  textTransform: 'none',
                  fontWeight: location.pathname === '/repositories' ? 'bold' : 'normal',
                  borderBottom: location.pathname === '/repositories' ? 2 : 0,
                  borderColor: 'primary.main',
                  borderRadius: 0,
                  px: 1
                }}
              >
                Repositories
              </Button>
              <Button 
                component={Link} 
                to="/functional-areas" 
                sx={{ 
                  color: 'text.primary',
                  '&:hover': { bgcolor: 'action.hover' },
                  textTransform: 'none',
                  fontWeight: location.pathname === '/functional-areas' ? 'bold' : 'normal',
                  borderBottom: location.pathname === '/functional-areas' ? 2 : 0,
                  borderColor: 'primary.main',
                  borderRadius: 0,
                  px: 1
                }}
              >
                Areas
              </Button>
              {authState.user?.roles?.includes('tenant_admin') && (
                <Button 
                  component={Link} 
                  to="/repository-sources" 
                  sx={{ 
                    color: 'text.primary',
                    '&:hover': { bgcolor: 'action.hover' },
                    textTransform: 'none',
                    fontWeight: location.pathname === '/repository-sources' ? 'bold' : 'normal',
                    borderBottom: location.pathname === '/repository-sources' ? 2 : 0,
                    borderColor: 'primary.main',
                    borderRadius: 0,
                    px: 1
                  }}
                >
                  Sources
                </Button>
              )}
              <Button 
                component={Link} 
                to="/jira-settings" 
                sx={{ 
                  color: 'text.primary',
                  '&:hover': { bgcolor: 'action.hover' },
                  textTransform: 'none',
                  fontWeight: location.pathname === '/jira-settings' ? 'bold' : 'normal',
                  borderBottom: location.pathname === '/jira-settings' ? 2 : 0,
                  borderColor: 'primary.main',
                  borderRadius: 0,
                  px: 1
                }}
              >
                JIRA Settings
              </Button>
            </Box>
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            {/* Quick Search - As per UI spec */}
            <Tooltip title="Quick Search (Coming Soon)">
                <IconButton sx={{ color: 'text.primary'}}>
                    <SearchIcon />
                </IconButton>
            </Tooltip>

            {/* Notifications Icon - As per UI spec */}
            <Tooltip title="Notifications (Coming Soon)">
                <IconButton sx={{ color: 'text.primary', ml: 1}}>
                    <BellIcon />
                </IconButton>
            </Tooltip>
            
            {/* User Profile Avatar & Menu - As per UI spec */}
            <Tooltip title={authState.user?.email || "User Profile"}>
              <IconButton onClick={handleOpenUserMenu} sx={{ p: 0, ml: 2 }}>
                <Avatar sx={{ bgcolor: 'secondary.main', width: 32, height: 32 }}>
                  {authState.user?.email ? authState.user.email[0].toUpperCase() : 'U'}
                </Avatar>
              </IconButton>
            </Tooltip>
            <Menu
              sx={{ mt: '45px' }}
              id="menu-appbar"
              anchorEl={anchorElUser}
              anchorOrigin={{ vertical: 'top', horizontal: 'right' }}
              keepMounted
              transformOrigin={{ vertical: 'top', horizontal: 'right' }}
              open={Boolean(anchorElUser)}
              onClose={handleCloseUserMenu}
              PaperProps={{
                elevation: 0,
                sx: {
                  overflow: 'visible',
                  filter: 'drop-shadow(0px 2px 8px rgba(0,0,0,0.12))',
                  mt: 1.5,
                  borderRadius: '12px 0 12px 12px', // Kasavu-cut for menu
                  bgcolor: 'background.paper',
                  '& .MuiAvatar-root': {
                    width: 32, height: 32, ml: -0.5, mr: 1,
                  },
                  '&:before': { // Arrow pointing upwards
                    content: '""',
                    display: 'block', position: 'absolute',
                    top: 0, right: 14, width: 10, height: 10,
                    bgcolor: 'background.paper',
                    transform: 'translateY(-50%) rotate(45deg)',
                    zIndex: 0,
                  },
                },
              }}
            >
              <MenuItem onClick={handleCloseUserMenu}>
                <Avatar sx={{ bgcolor: 'secondary.main'}}/> {authState.user?.email}
              </MenuItem>
              <Divider />
              <MenuItem 
                onClick={() => {
                  handleCloseUserMenu();
                  navigate('/jira-settings');
                }}
              >
                <ListItemIcon><JiraIcon /></ListItemIcon>
                JIRA Settings
              </MenuItem>
              <MenuItem onClick={handleCloseUserMenu}>
                <ListItemIcon><Settings /></ListItemIcon>
                Settings
              </MenuItem>
              <MenuItem onClick={handleCloseUserMenu}>
                <ListItemIcon><HelpIcon /></ListItemIcon>
                Help
              </MenuItem>
              <Divider />
              <MenuItem onClick={handleLogout}>
                <ListItemIcon><LogOut /></ListItemIcon>
                Logout
              </MenuItem>
            </Menu>
          </Box>
        </Toolbar>
      </KasavuAppBar>
      
      {/* Main content area: Sidebar + Issue Workspace */}
      {/* This is where <Outlet /> will render nested routes like DashboardPage */}
      {/* TODO: Implement sidebar and main workspace layout as per UI spec */}
      <Box component="main" sx={{ flexGrow: 1, p: 3, bgcolor: 'background.default' }}>
        <Outlet />
      </Box>
    </Box>
  );
};

export default MainLayout;