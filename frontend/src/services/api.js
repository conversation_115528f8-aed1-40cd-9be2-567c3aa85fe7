import axios from "axios";

const API_BASE_URL =
  process.env.REACT_APP_API_BASE_URL || "http://localhost:5045";

const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    "Content-Type": "application/json",
  },
});

// Global auth handler for API responses
let authEventHandlers = {
  onAuthError: null,
  onLogout: null,
};

// Register auth handlers from App component
export const registerAuthHandlers = (handlers) => {
  authEventHandlers = { ...authEventHandlers, ...handlers };
};

// Flag to prevent multiple token refresh attempts
let isRefreshing = false;
let failedQueue = [];

const processQueue = (error, token = null) => {
  failedQueue.forEach(prom => {
    if (error) {
      prom.reject(error);
    } else {
      prom.resolve(token);
    }
  });
  
  failedQueue = [];
};

// Setup request interceptor to add auth token
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem("accessToken");
    if (token && !config.headers["Authorization"]) {
      config.headers["Authorization"] = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Setup response interceptor to handle auth errors
apiClient.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    // Handle authentication errors (401)
    if (error.response && error.response.status === 401 && !originalRequest._retry) {
      if (originalRequest.url.includes('/auth/refresh')) {
        // Refresh token is also invalid
        isRefreshing = false;
        processQueue(error, null);
        
        // Clean local storage
        localStorage.removeItem("accessToken");
        localStorage.removeItem("refreshToken");
        localStorage.removeItem("tenantId");

        // Clear auth header
        delete apiClient.defaults.headers.common["Authorization"];

        // Notify app about auth error
        if (authEventHandlers.onAuthError) {
          authEventHandlers.onAuthError();
        }

        // Redirect to login page (if not already there)
        if (!window.location.pathname.includes("/login")) {
          window.location.href = "/login";
        }
        
        return Promise.reject(error);
      }

      if (isRefreshing) {
        return new Promise(function(resolve, reject) {
          failedQueue.push({ resolve, reject });
        }).then(token => {
          originalRequest.headers['Authorization'] = 'Bearer ' + token;
          return apiClient(originalRequest);
        }).catch(err => {
          return Promise.reject(err);
        });
      }

      originalRequest._retry = true;
      isRefreshing = true;

      const refreshTokenValue = localStorage.getItem("refreshToken");
      const tenantId = localStorage.getItem("tenantId");

      if (!refreshTokenValue || !tenantId) {
        // No refresh token available
        isRefreshing = false;
        processQueue(error, null);
        
        // Clean up and redirect to login
        localStorage.removeItem("accessToken");
        localStorage.removeItem("refreshToken");
        localStorage.removeItem("tenantId");
        delete apiClient.defaults.headers.common["Authorization"];
        
        if (authEventHandlers.onAuthError) {
          authEventHandlers.onAuthError();
        }
        
        if (!window.location.pathname.includes("/login")) {
          window.location.href = "/login";
        }
        
        return Promise.reject(error);
      }

      try {
        // Temporarily remove the Authorization header for the refresh request
        delete apiClient.defaults.headers.common["Authorization"];
        
        const response = await apiClient.post(
          `/tenants/${tenantId}/auth/refresh`,
          {},
          {
            headers: {
              'Authorization': `Bearer ${refreshTokenValue}`
            }
          }
        );

        const { access_token } = response.data;
        
        // Store new access token
        localStorage.setItem("accessToken", access_token);
        apiClient.defaults.headers.common["Authorization"] = `Bearer ${access_token}`;
        
        isRefreshing = false;
        processQueue(null, access_token);
        
        // Retry the original request with new token
        originalRequest.headers['Authorization'] = 'Bearer ' + access_token;
        return apiClient(originalRequest);
        
      } catch (refreshError) {
        isRefreshing = false;
        processQueue(refreshError, null);
        
        // Refresh failed, clean up and redirect to login
        localStorage.removeItem("accessToken");
        localStorage.removeItem("refreshToken");
        localStorage.removeItem("tenantId");
        delete apiClient.defaults.headers.common["Authorization"];
        
        if (authEventHandlers.onAuthError) {
          authEventHandlers.onAuthError();
        }
        
        if (!window.location.pathname.includes("/login")) {
          window.location.href = "/login";
        }
        
        return Promise.reject(refreshError);
      }
    }

    return Promise.reject(error);
  }
);

// Set the auth token for API calls
export const setAuthToken = (token) => {
  if (token) {
    apiClient.defaults.headers.common["Authorization"] = `Bearer ${token}`;
  } else {
    delete apiClient.defaults.headers.common["Authorization"];
  }
};

// Authentication APIs
export const loginUser = (data) => {
  return apiClient
    .post(`/tenants/${data.tenantId}/auth/login`, {
      email: data.email,
      password: data.password,
    })
    .then((response) => response.data);
};

export const logoutUser = (data) => {
  // First call the logout endpoint if we have a refresh token
  if (data.refreshToken) {
    return apiClient
      .post(`/tenants/${data.tenantId}/auth/logout`, {
        refresh_token: data.refreshToken,
      })
      .then((response) => {
        // Clear the auth header after successful logout
        delete apiClient.defaults.headers.common["Authorization"];
        return response.data;
      })
      .catch((error) => {
        // Even if logout fails, clear the auth header
        delete apiClient.defaults.headers.common["Authorization"];
        // For 401 errors during logout, consider it successful
        if (error.response && error.response.status === 401) {
          return { message: "Logout successful" };
        }
        throw error;
      });
  } else {
    // If no refresh token, just clear the auth header
    delete apiClient.defaults.headers.common["Authorization"];
    return Promise.resolve({ message: "Logout successful" });
  }
};

export const refreshToken = (data) => {
  return apiClient
    .post(`/tenants/${data.tenantId}/auth/refresh`, {
      refresh_token: data.refreshToken,
    })
    .then((response) => response.data);
};

// Tenant APIs
export const resolveTenantByName = (tenantName) => {
  return apiClient
    .get(`/api/public/tenants/resolve`, {
      params: { name: tenantName },
    })
    .then((response) => response.data);
};

export const createTenant = (data) => {
  return apiClient
    .post("/admin/tenants", data)
    .then((response) => response.data);
};

export const getTenantDetails = (tenantId) => {
  return apiClient
    .get(`/admin/tenants/${tenantId}`)
    .then((response) => response.data);
};

// JIRA APIs
export const manageJiraCredentials = (tenantId, data) => {
  return apiClient
    .post(`/tenants/${tenantId}/jira/credentials`, data)
    .then((response) => response.data);
};

export const getJiraCredentialStatus = (tenantId) => {
  return apiClient
    .get(`/tenants/${tenantId}/jira/credentials`)
    .then((response) => response.data);
};

// Projects APIs
export const listProjects = (tenantId) => {
  return apiClient
    .get(`/tenants/${tenantId}/projects`)
    .then((response) => response.data);
};

export const trackProjectAccess = (tenantId, projectKey) => {
  return apiClient
    .post(`/tenants/${tenantId}/projects/${projectKey}/access`)
    .then((response) => response.data);
};

export const getRecentProjects = (tenantId, limit = 5) => {
  return apiClient
    .get(`/tenants/${tenantId}/user-activity/recent-projects?limit=${limit}`)
    .then((response) => response.data);
};

// Issues APIs
export const listIssuesForProject = (tenantId, projectKey, params = {}) => {
  const queryParams = new URLSearchParams(params);
  // Default to the first page and a standard page size if not provided
  queryParams.set("page", params.page || 1);
  queryParams.set("perPage", params.perPage || 25);
  return apiClient
    .get(`/tenants/${tenantId}/projects/${projectKey}/issues?${queryParams}`)
    .then((response) => response.data);
};

export const getIssueDetails = (tenantId, issueKey) => {
  return apiClient
    .get(`/tenants/${tenantId}/issues/${issueKey}`)
    .then((response) => response.data);
};

// Repository Source APIs
export const listRepositorySources = (tenantId) => {
  return apiClient
    .get(`/tenants/${tenantId}/repository-sources`)
    .then((response) => response.data);
};

export const createRepositorySource = (tenantId, data) => {
  return apiClient
    .post(`/tenants/${tenantId}/repository-sources`, data)
    .then((response) => response.data);
};

export const getRepositorySource = (tenantId, sourceId) => {
  return apiClient
    .get(`/tenants/${tenantId}/repository-sources/${sourceId}`)
    .then((response) => response.data);
};

export const syncRepositorySource = (tenantId, sourceId, force = false) => {
  return apiClient
    .post(`/tenants/${tenantId}/repository-sources/${sourceId}/sync`, { force })
    .then((response) => response.data);
};

export const deleteRepositorySource = (tenantId, sourceId) => {
  return apiClient
    .delete(`/tenants/${tenantId}/repository-sources/${sourceId}`)
    .then((response) => response.data);
};

// Repository APIs - Using repository-sources discovered repos
export const listDiscoveredRepositories = async (tenantId) => {
  try {
    // Try the direct repositories endpoint from repository_source blueprint
    const response = await apiClient.get(`/tenants/${tenantId}/repositories`);
    console.log("Direct repositories response:", response.data);

    // Standard format should have repositories array
    if (response.data && response.data.repositories !== undefined) {
      return response.data;
    }

    // Handle if it returns array directly (legacy format)
    if (Array.isArray(response.data)) {
      return { repositories: response.data };
    }

    return { repositories: [] };
  } catch (error) {
    console.error("Error fetching repositories:", error);
    return { repositories: [] };
  }
};

export const listAvailableRepositoriesForProject = (tenantId, projectKey) => {
  return apiClient
    .get(`/tenants/${tenantId}/projects/${projectKey}/available-repositories`)
    .then((response) => response.data);
};

export const linkRepositoryToProject = (
  tenantId,
  projectKey,
  repositoryId,
  data
) => {
  return apiClient
    .post(`/tenants/${tenantId}/projects/${projectKey}/repositories`, {
      repository_id: repositoryId,
      ...data,
    })
    .then((response) => response.data);
};

export const getProjectRepositories = (tenantId, projectKey) => {
  return apiClient
    .get(`/tenants/${tenantId}/projects/${projectKey}/repositories`)
    .then((response) => response.data);
};

export const setUserRepositoryPath = (
  tenantId,
  projectKey,
  repositoryId,
  localPath
) => {
  return apiClient
    .put(
      `/tenants/${tenantId}/projects/${projectKey}/repositories/${repositoryId}/user-path`,
      {
        local_path: localPath,
      }
    )
    .then((response) => response.data);
};

export const getProjectRepositoryCount = (tenantId, projectKey) => {
  return apiClient
    .get(`/tenants/${tenantId}/projects/${projectKey}/repositories`)
    .then((response) => response.data);
};

export const getUserRepositoryPath = (tenantId, projectKey, repositoryId) => {
  return apiClient
    .get(
      `/tenants/${tenantId}/projects/${projectKey}/repositories/${repositoryId}/user-path`
    )
    .then((response) => response.data);
};

// Direct repository path management (not tied to projects)
export const setDirectRepositoryPath = (tenantId, repositoryId, localPath) => {
  return apiClient
    .put(`/tenants/${tenantId}/repositories/${repositoryId}/path`, {
      local_path: localPath,
    })
    .then((response) => response.data);
};

export const getDirectRepositoryPath = (tenantId, repositoryId) => {
  return apiClient
    .get(`/tenants/${tenantId}/repositories/${repositoryId}/path`)
    .then((response) => response.data);
};

export const getAllDirectRepositoryPaths = (tenantId) => {
  return apiClient
    .get(`/tenants/${tenantId}/repositories/paths`)
    .then((response) => response.data);
};

export const deleteRepository = (
  tenantId,
  repositoryId,
  forceDelete = false
) => {
  return apiClient
    .delete(
      `/tenants/${tenantId}/repositories/${repositoryId}${
        forceDelete ? "?force=true" : ""
      }`
    )
    .then((response) => response.data);
};

// Agent execution APIs
export const executeAgentBugFix = (tenantId, issueKey, repositoryIds) => {
  return apiClient
    .post(`/tenants/${tenantId}/agents/execute-bug-fix`, {
      issue_key: issueKey,
      repository_ids: repositoryIds,
    })
    .then((response) => response.data);
};

export const getAgentExecutionStatus = (tenantId, executionId) => {
  return apiClient
    .get(`/tenants/${tenantId}/agents/execution-status/${executionId}`)
    .then((response) => response.data);
};

export const testSocketConnection = (tenantId) => {
  return apiClient
    .post(`/tenants/${tenantId}/agents/test-socket-connection`)
    .then((response) => response.data);
};

export const getSocketStatus = (tenantId) => {
  return apiClient
    .get(`/tenants/${tenantId}/agents/socket-status`)
    .then((response) => response.data);
};

// Functional Areas API functions
export const getFunctionalAreas = (tenantId) => {
  return apiClient
    .get(`/tenants/${tenantId}/functional-areas`)
    .then((response) => response.data);
};

export const createFunctionalArea = (tenantId, areaData) => {
  return apiClient
    .post(`/tenants/${tenantId}/functional-areas`, areaData)
    .then((response) => response.data);
};

export const getFunctionalArea = (tenantId, areaId) => {
  return apiClient
    .get(`/tenants/${tenantId}/functional-areas/${areaId}`)
    .then((response) => response.data);
};

export const updateFunctionalArea = (tenantId, areaId, areaData) => {
  return apiClient
    .put(`/tenants/${tenantId}/functional-areas/${areaId}`, areaData)
    .then((response) => response.data);
};

export const deleteFunctionalArea = (tenantId, areaId) => {
  return apiClient
    .delete(`/tenants/${tenantId}/functional-areas/${areaId}`)
    .then((response) => response.data);
};

export const linkProjectToFunctionalArea = (tenantId, areaId, projectData) => {
  return apiClient
    .post(
      `/tenants/${tenantId}/functional-areas/${areaId}/projects`,
      projectData
    )
    .then((response) => response.data);
};

export const unlinkProjectFromFunctionalArea = (
  tenantId,
  areaId,
  projectKey
) => {
  return apiClient
    .delete(
      `/tenants/${tenantId}/functional-areas/${areaId}/projects/${projectKey}`
    )
    .then((response) => response.data);
};

export const linkRepositoryToFunctionalArea = (
  tenantId,
  areaId,
  repositoryData
) => {
  return apiClient
    .post(
      `/tenants/${tenantId}/functional-areas/${areaId}/repositories`,
      repositoryData
    )
    .then((response) => response.data);
};

export const unlinkRepositoryFromFunctionalArea = (
  tenantId,
  areaId,
  repositoryId
) => {
  return apiClient
    .delete(
      `/tenants/${tenantId}/functional-areas/${areaId}/repositories/${repositoryId}`
    )
    .then((response) => response.data);
};

export const getFunctionalAreasForProject = (tenantId, projectKey) => {
  return apiClient
    .get(`/tenants/${tenantId}/projects/${projectKey}/functional-areas`)
    .then((response) => response.data);
};

export const getFunctionalAreasForRepository = (tenantId, repositoryId) => {
  return apiClient
    .get(`/tenants/${tenantId}/repositories/${repositoryId}/functional-areas`)
    .then((response) => response.data);
};

export const getFunctionalAreaSummary = (tenantId) => {
  return apiClient.get(`/tenants/${tenantId}/functional-areas/summary`);
};

// Repository agent configuration APIs
export const updateRepositoryAgentConfig = (tenantId, repositoryId, config) => {
  return apiClient
    .put(
      `/tenants/${tenantId}/repositories/${repositoryId}/agent-config`,
      config
    )
    .then((response) => response.data);
};

export const getAvailableTools = (tenantId) => {
  return apiClient
    .get(`/tenants/${tenantId}/tools`)
    .then((response) => response.data);
};

export default apiClient;
