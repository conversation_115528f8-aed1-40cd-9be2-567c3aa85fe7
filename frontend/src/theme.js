import { createTheme } from '@mui/material/styles';

// Monsoon Kasavu Theme Palette (initial)
const monsoonPalette = {
  primary: {
    main: '#2079A6', // --mk-sea-blue-500
  },
  secondary: {
    main: '#2E8F6E', // --mk-monsoon-green-500 (using as secondary for now)
  },
  accent: {
    main: '#C9A85D', // --mk-kasavu-gold-400
  },
  background: {
    default: '#F7F9F4', // --mk-mullappoo-white
    paper: '#E8ECEA',   // --mk-shadow-100 (for card surfaces)
  },
  text: {
    primary: '#754C24', // --mk-arecanut-brown-600 (for headings)
    secondary: '#4F4F4F', // From general UI spec, can be refined with Monsoon
  },
  success: { main: '#27AE60' }, // General UI Spec
  warning: { main: '#F2994A' }, // General UI Spec
  error: { main: '#EB5757' },   // General UI Spec
};

// Base MUI theme to be customized
const theme = createTheme({
  palette: monsoonPalette,
  typography: {
    fontFamily: ['Inter', 'Roboto', 'Helvetica', 'Arial', 'sans-serif'].join(','),
    fontSize: 14,
    h1: { fontWeight: 600, fontSize: '2.2rem' },
    h2: { fontWeight: 600, fontSize: '1.8rem' },
    h3: { fontWeight: 600, fontSize: '1.5rem' },
    h4: { fontWeight: 600, fontSize: '1.3rem' },
    h5: { fontWeight: 600, fontSize: '1.1rem' },
    h6: { fontWeight: 600, fontSize: '1rem' },
    body1: { fontWeight: 400, lineHeight: '20px' }, // Base body
  },
  shape: {
    // Base border radius, "Kasavu-cut" will be applied via custom styles or styled components
    borderRadius: 10, // From general UI spec (rounded-lg corners = 10px)
  },
  components: {
    // Example: Overriding MuiButton default props for the Kasavu-cut
    MuiButton: {
      styleOverrides: {
        root: {
          // borderRadius: '12px 0 12px 12px', // Kasavu-cut (top-right square)
          // textTransform: 'none', // Optional: many modern UIs avoid ALL CAPS buttons
          // padding: '8px 20px', // From Monsoon example
          // fontWeight: 500, // From Monsoon example
        },
        // Contained primary button theming example
        containedPrimary: {
          // backgroundColor: monsoonPalette.primary.main, // Already handled by palette
          // color: 'white',
          // '&:hover': {
          //   backgroundColor: '#1A668C', // darken(monsoonPalette.primary.main, 0.08) - approx.
          // },
        },
      },
    },
    MuiCard: {
        styleOverrides: {
            root: {
                // borderRadius: '12px 0 12px 12px', // Kasavu-cut
                // padding: '24px', // From Monsoon Card example
                // boxShadow: '0 2px 4px rgba(0,0,0,.06)', // General UI Spec subtle shadow
            }
        }
    },
    // Other component overrides will go here
  },
});

export default theme;