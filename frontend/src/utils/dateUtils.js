/**
 * Utility functions for date/time handling
 */

/**
 * Format UTC date to local time string
 * @param {string} utcDateString - UTC date string from backend
 * @returns {string} Formatted local date/time string
 */
export const formatUTCToLocal = (utcDateString) => {
  if (!utcDateString) return 'Never';
  
  // Create date object from UTC string
  const date = new Date(utcDateString);
  
  // Use browser's locale and timezone
  return date.toLocaleString(undefined, {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    hour12: true
  });
};

/**
 * Get relative time from UTC date
 * @param {string} utcDateString - UTC date string from backend
 * @returns {string} Relative time string (e.g., "5 min ago", "2 hours ago")
 */
export const getRelativeTime = (utcDateString) => {
  if (!utcDateString) return '';
  
  // Create date object from UTC string
  const date = new Date(utcDateString);
  const now = new Date();
  
  // Calculate time difference in milliseconds
  const diffMs = now - date;
  const diffMins = Math.floor(diffMs / 60000);
  
  if (diffMins < 1) return 'just now';
  if (diffMins < 60) return `${diffMins} min ago`;
  
  const diffHours = Math.floor(diffMins / 60);
  if (diffHours < 24) return `${diffHours} hour${diffHours === 1 ? '' : 's'} ago`;
  
  const diffDays = Math.floor(diffHours / 24);
  return `${diffDays} day${diffDays === 1 ? '' : 's'} ago`;
};

/**
 * Format ISO date string for display
 * @param {string} isoString - ISO date string
 * @returns {string} Formatted date string
 */
export const formatDate = (isoString) => {
  if (!isoString) return '';
  
  const date = new Date(isoString);
  return date.toLocaleDateString(undefined, {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};