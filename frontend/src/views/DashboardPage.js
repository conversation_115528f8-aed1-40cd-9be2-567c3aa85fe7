import React, { useState, useEffect, useContext } from 'react';
import { 
  Typography, 
  Box, 
  Paper, 
  Button, 
  Card, 
  CardContent, 
  CardActionArea,
  Grid,
  Divider,
  CircularProgress,
  Alert
} from '@mui/material';
import { Link, useNavigate } from 'react-router-dom';
import { AuthContext } from '../App';
import { getJiraCredentialStatus, getRecentProjects, trackProjectAccess, listProjects, getProjectRepositories } from '../services/api';

// Mock icon functions
const ProjectIcon = () => <span>🗂️</span>;
const RepositoryIcon = () => <span>📁</span>;

const DashboardPage = () => {
  const { authState } = useContext(AuthContext);
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [jiraConfigured, setJiraConfigured] = useState(false);
  const [error, setError] = useState(null);
  const [recentProjects, setRecentProjects] = useState([]);
  const [linkedRepos, setLinkedRepos] = useState({});

  useEffect(() => {
    const checkJiraConfiguration = async () => {
      setLoading(true);
      try {
        // Check if JIRA is configured
        const jiraStatus = await getJiraCredentialStatus(authState.tenantId);
        setJiraConfigured(jiraStatus.configured || false);
        
        // If JIRA is configured, fetch recent projects
        if (jiraStatus.configured) {
          try {
            // Get user's recent projects
            const recentData = await getRecentProjects(authState.tenantId, 3);
            
            let projects = [];
            if (recentData.projects && recentData.projects.length > 0) {
              // User has recent project activity
              projects = recentData.projects;
            } else {
              // No recent activity, fall back to most recent projects from the API
              const projectData = await listProjects(authState.tenantId);
              projects = Array.isArray(projectData.projects) 
                ? projectData.projects.slice(0, 3) // Take top 3 projects
                : [];
            }
            
            setRecentProjects(projects);
            
            // Fetch linked repositories for each project
            const linkedByProject = {};
            const repoPromises = projects.map(project => 
              getProjectRepositories(authState.tenantId, project.key)
                .then(response => ({
                  projectKey: project.key,
                  repositories: response.repositories || []
                }))
                .catch(err => ({
                  projectKey: project.key,
                  repositories: []
                }))
            );
            
            const repoResults = await Promise.all(repoPromises);
            for (const result of repoResults) {
              linkedByProject[result.projectKey] = result.repositories;
            }
            
            setLinkedRepos(linkedByProject);
          } catch (projectErr) {
            console.error('Error fetching projects:', projectErr);
            setError('Could not load recent projects.');
          }
        }
      } catch (err) {
        console.error('Error checking JIRA configuration:', err);
        setError('Failed to check JIRA configuration status.');
      } finally {
        setLoading(false);
      }
    };

    if (authState.tenantId) {
      checkJiraConfiguration();
    } else {
      setLoading(false);
    }
  }, [authState.tenantId]);

  const handleProjectClick = async (projectKey) => {
    try {
      // Record the project access
      await trackProjectAccess(authState.tenantId, projectKey);
      // Navigate to the project issues page
      navigate(`/projects/${projectKey}`);
    } catch (err) {
      console.error('Error tracking project access:', err);
      // Still navigate even if tracking fails
      navigate(`/projects/${projectKey}`);
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Paper 
      elevation={0} 
      sx={{
        padding: 3,
      }}
    >
      <Typography variant="h4" gutterBottom color="text.primary">
        Welcome to JIFFY BUG FIX
      </Typography>
      <Typography variant="body1" color="text.secondary" paragraph>
        Your centralized hub for tracking and managing JIRA bug issues across all your projects.
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {!jiraConfigured ? (
        // Show JIRA setup prompt if not configured
        <Box sx={{ mt: 4, p: 3, bgcolor: 'rgba(25, 118, 210, 0.04)', borderRadius: '12px 0 12px 12px' }}>
          <Typography variant="h6" color="primary" gutterBottom>Set Up JIRA Integration</Typography>
          <Typography variant="body1" paragraph>
            To start using JIFFY BUG FIX, you need to connect your JIRA account first.
          </Typography>
          <Button 
            variant="contained" 
            color="primary" 
            component={Link}
            to="/jira-settings"
            sx={{ borderRadius: '12px 0 12px 12px' }}
          >
            Configure JIRA Settings
          </Button>
        </Box>
      ) : (
        // Show recent projects if JIRA is configured
        <Box sx={{ mt: 3 }}>
          <Typography variant="h5" color="text.primary" gutterBottom>
            Recent Projects
          </Typography>
          
          {recentProjects.length === 0 ? (
            <Box sx={{ p: 3, bgcolor: 'rgba(25, 118, 210, 0.04)', borderRadius: '12px 0 12px 12px' }}>
              <Typography variant="body1" color="text.secondary">
                No projects found. Go to the Projects section to view and access your JIRA projects.
              </Typography>
              <Button 
                variant="contained" 
                color="primary" 
                component={Link}
                to="/projects"
                sx={{ mt: 2, borderRadius: '12px 0 12px 12px' }}
              >
                Browse Projects
              </Button>
            </Box>
          ) : (
            <Grid container spacing={3}>
              {recentProjects.map((project) => {
                const hasLinkedRepos = linkedRepos[project.key]?.length > 0;
                return (
                  <Grid item xs={12} sm={6} md={4} key={project.key}>
                    <Card 
                      sx={{ 
                        height: '100%', 
                        display: 'flex', 
                        flexDirection: 'column',
                        borderRadius: '12px 0 12px 12px',
                        transition: 'transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out',
                        background: hasLinkedRepos ? 'linear-gradient(135deg, #f5f5f5 0%, #e8f5e9 100%)' : '#fff',
                        border: hasLinkedRepos ? '2px solid #81c784' : '1px solid #e0e0e0',
                        '&:hover': {
                          transform: 'translateY(-4px)',
                          boxShadow: '0 6px 12px rgba(0,0,0,0.1)'
                        }
                      }}
                      elevation={hasLinkedRepos ? 3 : 1}
                    >
                      <CardActionArea 
                        sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column', alignItems: 'stretch' }}
                        onClick={() => handleProjectClick(project.key)}
                      >
                        <CardContent sx={{ flexGrow: 1 }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                            <ProjectIcon />
                            <Typography variant="h6" component="div" sx={{ ml: 1 }}>
                              {project.name}
                            </Typography>
                            {hasLinkedRepos && (
                              <Typography
                                variant="caption"
                                sx={{
                                  bgcolor: 'success.main',
                                  color: 'white',
                                  px: 1,
                                  py: 0.2,
                                  borderRadius: '12px',
                                  ml: 'auto',
                                  fontWeight: 'medium'
                                }}
                              >
                                Linked
                              </Typography>
                            )}
                          </Box>
                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                            <Typography 
                              variant="caption" 
                              sx={{ 
                                bgcolor: 'rgba(86, 204, 242, 0.2)', 
                                color: '#2079A6',
                                px: 1,
                                py: 0.5,
                                borderRadius: '4px',
                                fontWeight: 'medium'
                              }}
                            >
                              {project.key}
                            </Typography>
                          </Box>
                          <Divider sx={{ mb: 2 }} />
                          <Typography variant="body2" color="text.secondary">
                            {project.description || 'No description available.'}
                          </Typography>
                          
                          {linkedRepos[project.key]?.length > 0 && (
                            <Box sx={{ mt: 2 }}>
                              <Typography variant="body2" sx={{ mb: 1, fontWeight: 'bold' }}>
                                Linked Repositories:
                              </Typography>
                              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                                {linkedRepos[project.key].map(repo => (
                                  <Box
                                    key={repo.id}
                                    sx={{
                                      display: 'flex',
                                      alignItems: 'center',
                                      backgroundColor: 'primary.light',
                                      borderRadius: '16px',
                                      px: 1.5,
                                      py: 0.5,
                                      border: '1px solid',
                                      borderColor: 'primary.main',
                                      fontSize: '0.75rem'
                                    }}
                                  >
                                    <RepositoryIcon />
                                    <Typography variant="caption" sx={{ ml: 0.5 }}>
                                      {repo.display_name || repo.name}
                                    </Typography>
                                  </Box>
                                ))}
                              </Box>
                            </Box>
                          )}
                        </CardContent>
                      </CardActionArea>
                    </Card>
                  </Grid>
                );
              })}
            </Grid>
          )}
          
          <Box sx={{ mt: 4, display: 'flex', justifyContent: 'center' }}>
            <Button 
              variant="outlined" 
              color="primary" 
              component={Link}
              to="/projects"
              sx={{ 
                borderRadius: '12px 0 12px 12px',
                minWidth: 200
              }}
            >
              View All Projects
            </Button>
          </Box>
        </Box>
      )}
    </Paper>
  );
};

export default DashboardPage;