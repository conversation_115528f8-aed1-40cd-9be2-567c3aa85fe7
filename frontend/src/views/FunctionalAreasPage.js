import React, { useState, useEffect, useContext } from 'react';
import {
  Box,
  Typo<PERSON>,
  Button,
  Grid,
  Card,
  CardContent,
  CardActions,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  Alert,
  Fab,
  Menu,
  ListItemIcon,
  ListItemText,
  Tooltip,
  Breadcrumbs,
  Link,
  Skeleton,
  Autocomplete
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  MoreVert as MoreVertIcon,
  Group as GroupIcon,
  Code as CodeIcon,
  Storage as StorageIcon,
  Launch as LaunchIcon,
  Folder as FolderIcon,
  Settings as SettingsIcon
} from '@mui/icons-material';
import { AuthContext } from '../App';
import { 
  getFunctionalAreas, 
  createFunctionalArea, 
  updateFunctionalArea, 
  deleteFunctionalArea,
  listProjects,
  listDiscoveredRepositories
} from '../services/api';
import FunctionalAreaManagementDialog from '../components/FunctionalAreaManagementDialog';


const FunctionalAreasPage = () => {
  const { authState } = useContext(AuthContext);
  const [functionalAreas, setFunctionalAreas] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [managementDialogOpen, setManagementDialogOpen] = useState(false);
  const [selectedArea, setSelectedArea] = useState(null);
  const [menuAnchor, setMenuAnchor] = useState(null);
  
  // Form state
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    selectedProjects: [],
    selectedRepositories: []
  });
  const [availableProjects, setAvailableProjects] = useState([]);
  const [availableRepositories, setAvailableRepositories] = useState([]);

  const fetchFunctionalAreas = async () => {
    try {
      console.log('Fetching functional areas for tenant:', authState.tenantId);
      setLoading(true);
      const response = await getFunctionalAreas(authState.tenantId);
      console.log('Functional areas response:', response);
      console.log('First functional area projects:', response.functional_areas?.[0]?.projects);
      setFunctionalAreas(response.functional_areas || []);
      setError(null);
    } catch (err) {
      console.error('Error fetching functional areas:', err);
      setError('Failed to load functional areas');
    } finally {
      setLoading(false);
    }
  };

  const fetchAvailableProjects = async () => {
    try {
      console.log('Fetching available projects for tenant:', authState.tenantId);
      const response = await listProjects(authState.tenantId);
      console.log('Available projects response:', response);
      setAvailableProjects(response.projects || []);
    } catch (err) {
      console.error('Error fetching projects:', err);
      setError('Failed to load projects');
    }
  };

  const fetchAvailableRepositories = async () => {
    try {
      console.log('Fetching available repositories for tenant:', authState.tenantId);
      const response = await listDiscoveredRepositories(authState.tenantId);
      console.log('Available repositories response:', response);
      setAvailableRepositories(response.repositories || []);
    } catch (err) {
      console.error('Error fetching repositories:', err);
      setError('Failed to load repositories');
    }
  };

  useEffect(() => {
    if (authState.tenantId) {
      fetchFunctionalAreas();
      fetchAvailableProjects();
      fetchAvailableRepositories();
    }
  }, [authState.tenantId]);

  const handleCreateSubmit = async () => {
    try {
      console.log('Creating functional area with data:', formData);
      console.log('Auth state:', authState);
      
      if (!authState.tenantId) {
        console.error('No tenant_id found in auth state');
        setError('User not properly authenticated');
        return;
      }
      
      if (!formData.name.trim()) {
        console.error('Name is required');
        setError('Name is required');
        return;
      }
      
      await createFunctionalArea(authState.tenantId, formData);
      setCreateDialogOpen(false);
      setFormData({ name: '', description: '', selectedProjects: [], selectedRepositories: [] });
      fetchFunctionalAreas();
    } catch (err) {
      console.error('Error creating functional area:', err);
      setError('Failed to create functional area');
    }
  };

  const handleEditSubmit = async () => {
    try {
      await updateFunctionalArea(authState.tenantId, selectedArea.id, formData);
      setEditDialogOpen(false);
      setSelectedArea(null);
      setFormData({ name: '', description: '', selectedProjects: [], selectedRepositories: [] });
      fetchFunctionalAreas();
    } catch (err) {
      console.error('Error updating functional area:', err);
      setError('Failed to update functional area');
    }
  };

  const handleDelete = async () => {
    try {
      await deleteFunctionalArea(authState.tenantId, selectedArea.id);
      setDeleteDialogOpen(false);
      setSelectedArea(null);
      fetchFunctionalAreas();
    } catch (err) {
      console.error('Error deleting functional area:', err);
      setError('Failed to delete functional area');
    }
  };

  const openEditDialog = (area) => {
    setSelectedArea(area);
    // Convert project_key format to the format expected by autocomplete
    const selectedProjects = (area.projects || []).map(project => ({
      key: project.project_key,
      name: project.project_key // We'll need to get the actual name from the projects list
    }));
    // Convert repository format to the format expected by autocomplete
    const selectedRepositories = (area.repositories || []).map(repo => ({
      id: repo.repository_id,
      name: repo.repository_name
    }));
    setFormData({
      name: area.name,
      description: area.description || '',
      selectedProjects: selectedProjects,
      selectedRepositories: selectedRepositories
    });
    setEditDialogOpen(true);
    setMenuAnchor(null);
  };

  const openDeleteDialog = (area) => {
    setSelectedArea(area);
    setDeleteDialogOpen(true);
    setMenuAnchor(null);
  };

  const openManagementDialog = (area) => {
    setSelectedArea(area);
    setManagementDialogOpen(true);
    setMenuAnchor(null);
  };


  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString();
  };

  if (loading) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography variant="h4" sx={{ mb: 3 }}>
          Functional Areas
        </Typography>
        <Grid container spacing={3}>
          {[1, 2, 3, 4].map((item) => (
            <Grid item xs={12} md={6} lg={4} key={item}>
              <Card sx={{ borderRadius: '12px 0 12px 12px' }}>
                <CardContent>
                  <Skeleton variant="text" width="60%" height={32} />
                  <Skeleton variant="text" width="40%" height={24} sx={{ mt: 1 }} />
                  <Skeleton variant="text" width="80%" height={20} sx={{ mt: 2 }} />
                  <Box sx={{ mt: 2, display: 'flex', gap: 1 }}>
                    <Skeleton variant="rounded" width={60} height={24} />
                    <Skeleton variant="rounded" width={80} height={24} />
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Breadcrumbs>
            <Link color="inherit" href="/dashboard">
              Dashboard
            </Link>
            <Typography color="text.primary">Functional Areas</Typography>
          </Breadcrumbs>
          <Typography variant="h4" sx={{ mt: 1 }}>
            Functional Areas
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Organize your projects and repositories into logical functional areas
          </Typography>
        </Box>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => {
            console.log('Create Area button clicked');
            setCreateDialogOpen(true);
          }}
          sx={{ borderRadius: '12px 0 12px 12px' }}
        >
          Create Area
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 3, borderRadius: '12px 0 12px 12px' }}>
          {error}
        </Alert>
      )}

      {functionalAreas.length === 0 ? (
        <Card sx={{ textAlign: 'center', p: 4, borderRadius: '12px 0 12px 12px' }}>
          <GroupIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
          <Typography variant="h6" gutterBottom>
            No functional areas yet
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            Create your first functional area to group related projects and repositories
          </Typography>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => {
              console.log('Create Functional Area (empty state) button clicked');
              setCreateDialogOpen(true);
            }}
            sx={{ borderRadius: '12px 0 12px 12px' }}
          >
            Create Functional Area
          </Button>
        </Card>
      ) : (
        <Grid container spacing={3}>
          {functionalAreas.map((area) => (
            <Grid item xs={12} md={6} lg={4} key={area.id}>
              <Card sx={{ 
                borderRadius: '12px 0 12px 12px',
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                position: 'relative'
              }}>
                <CardContent sx={{ flexGrow: 1 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                    <Typography variant="h6" component="h2" sx={{ fontWeight: 600 }}>
                      {area.name}
                    </Typography>
                    <IconButton
                      size="small"
                      onClick={(e) => {
                        setSelectedArea(area);
                        setMenuAnchor(e.currentTarget);
                      }}
                    >
                      <MoreVertIcon />
                    </IconButton>
                  </Box>

                  {area.description && (
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      {area.description}
                    </Typography>
                  )}

                  <Box sx={{ display: 'flex', gap: 1, mb: 2, flexWrap: 'wrap' }}>
                    {area.projects && area.projects.slice(0, 2).map((project) => (
                      <Chip
                        key={project.project_key}
                        label={project.project_key}
                        size="small"
                        icon={<CodeIcon />}
                        variant="outlined"
                        color="primary"
                      />
                    ))}
                    {area.repositories && area.repositories.slice(0, 2).map((repo) => (
                      <Chip
                        key={repo.repository_id}
                        label={repo.repository_name}
                        size="small"
                        icon={<StorageIcon />}
                        variant="outlined"
                        color="secondary"
                      />
                    ))}
                    {(area.projects?.length > 2 || area.repositories?.length > 2) && (
                      <Chip
                        label={`+${Math.max(0, (area.projects?.length || 0) - 2) + Math.max(0, (area.repositories?.length || 0) - 2)} more`}
                        size="small"
                        variant="outlined"
                        sx={{ backgroundColor: '#f5f5f5' }}
                      />
                    )}
                  </Box>

                  <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                      <CodeIcon fontSize="small" color="action" />
                      <Typography variant="body2" color="text.secondary">
                        {area.project_count} Projects
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                      <StorageIcon fontSize="small" color="action" />
                      <Typography variant="body2" color="text.secondary">
                        {area.repository_count} Repositories
                      </Typography>
                    </Box>
                  </Box>

                  <Typography variant="caption" color="text.secondary">
                    Created: {formatDate(area.created_at)}
                  </Typography>
                </CardContent>

                <CardActions sx={{ pt: 0 }}>
                  <Button
                    size="small"
                    startIcon={<SettingsIcon />}
                    onClick={() => openManagementDialog(area)}
                  >
                    Manage
                  </Button>
                  <Button
                    size="small"
                    startIcon={<EditIcon />}
                    onClick={() => openEditDialog(area)}
                  >
                    Edit
                  </Button>
                </CardActions>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}

      {/* Context Menu */}
      <Menu
        anchorEl={menuAnchor}
        open={Boolean(menuAnchor)}
        onClose={() => setMenuAnchor(null)}
      >
        <MenuItem onClick={() => openManagementDialog(selectedArea)}>
          <ListItemIcon>
            <SettingsIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Manage</ListItemText>
        </MenuItem>
        <MenuItem onClick={() => openEditDialog(selectedArea)}>
          <ListItemIcon>
            <EditIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Edit</ListItemText>
        </MenuItem>
        <MenuItem onClick={() => openDeleteDialog(selectedArea)} sx={{ color: 'error.main' }}>
          <ListItemIcon>
            <DeleteIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Delete</ListItemText>
        </MenuItem>
      </Menu>

      {/* Create Dialog */}
      <Dialog 
        open={createDialogOpen} 
        onClose={() => setCreateDialogOpen(false)}
        maxWidth="sm" 
        fullWidth
        PaperProps={{ sx: { borderRadius: '12px 0 12px 12px' } }}
      >
        <DialogTitle>Create Functional Area</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            name="name"
            label="Name"
            fullWidth
            variant="outlined"
            value={formData.name}
            onChange={(e) => setFormData({ ...formData, name: e.target.value })}
            sx={{ mb: 2 }}
          />
          <TextField
            margin="dense"
            name="description"
            label="Description"
            fullWidth
            multiline
            rows={3}
            variant="outlined"
            value={formData.description}
            onChange={(e) => setFormData({ ...formData, description: e.target.value })}
            sx={{ mb: 2 }}
          />
          <Autocomplete
            multiple
            options={availableProjects}
            getOptionLabel={(option) => `${option.key} - ${option.name}`}
            value={formData.selectedProjects}
            onChange={(event, newValue) => {
              setFormData({ ...formData, selectedProjects: newValue });
            }}
            renderTags={(value, getTagProps) =>
              value.map((option, index) => (
                <Chip
                  variant="outlined"
                  label={option.key}
                  {...getTagProps({ index })}
                  key={option.key}
                />
              ))
            }
            renderInput={(params) => (
              <TextField
                {...params}
                variant="outlined"
                label="Select JIRA Projects"
                placeholder="Choose projects to include in this functional area"
                margin="dense"
              />
            )}
            sx={{ mb: 2 }}
          />
          <Autocomplete
            multiple
            options={availableRepositories}
            getOptionLabel={(option) => `${option.name}`}
            value={formData.selectedRepositories}
            onChange={(event, newValue) => {
              setFormData({ ...formData, selectedRepositories: newValue });
            }}
            renderTags={(value, getTagProps) =>
              value.map((option, index) => (
                <Chip
                  variant="outlined"
                  label={option.name}
                  {...getTagProps({ index })}
                  key={option.id}
                />
              ))
            }
            renderInput={(params) => (
              <TextField
                {...params}
                variant="outlined"
                label="Select Repositories"
                placeholder="Choose repositories to include in this functional area"
                margin="dense"
              />
            )}
            sx={{ mb: 2 }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCreateDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleCreateSubmit} variant="contained">
            Create
          </Button>
        </DialogActions>
      </Dialog>

      {/* Edit Dialog */}
      <Dialog 
        open={editDialogOpen} 
        onClose={() => setEditDialogOpen(false)}
        maxWidth="sm" 
        fullWidth
        PaperProps={{ sx: { borderRadius: '12px 0 12px 12px' } }}
      >
        <DialogTitle>Edit Functional Area</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            name="name"
            label="Name"
            fullWidth
            variant="outlined"
            value={formData.name}
            onChange={(e) => setFormData({ ...formData, name: e.target.value })}
            sx={{ mb: 2 }}
          />
          <TextField
            margin="dense"
            name="description"
            label="Description"
            fullWidth
            multiline
            rows={3}
            variant="outlined"
            value={formData.description}
            onChange={(e) => setFormData({ ...formData, description: e.target.value })}
            sx={{ mb: 2 }}
          />
          <Autocomplete
            multiple
            options={availableProjects}
            getOptionLabel={(option) => `${option.key} - ${option.name}`}
            value={formData.selectedProjects}
            onChange={(event, newValue) => {
              setFormData({ ...formData, selectedProjects: newValue });
            }}
            renderTags={(value, getTagProps) =>
              value.map((option, index) => (
                <Chip
                  variant="outlined"
                  label={option.key}
                  {...getTagProps({ index })}
                  key={option.key}
                />
              ))
            }
            renderInput={(params) => (
              <TextField
                {...params}
                variant="outlined"
                label="Select JIRA Projects"
                placeholder="Choose projects to include in this functional area"
                margin="dense"
              />
            )}
            sx={{ mb: 2 }}
          />
          <Autocomplete
            multiple
            options={availableRepositories}
            getOptionLabel={(option) => `${option.name}`}
            value={formData.selectedRepositories}
            onChange={(event, newValue) => {
              setFormData({ ...formData, selectedRepositories: newValue });
            }}
            renderTags={(value, getTagProps) =>
              value.map((option, index) => (
                <Chip
                  variant="outlined"
                  label={option.name}
                  {...getTagProps({ index })}
                  key={option.id}
                />
              ))
            }
            renderInput={(params) => (
              <TextField
                {...params}
                variant="outlined"
                label="Select Repositories"
                placeholder="Choose repositories to include in this functional area"
                margin="dense"
              />
            )}
            sx={{ mb: 2 }}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleEditSubmit} variant="contained">
            Save Changes
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Dialog */}
      <Dialog 
        open={deleteDialogOpen} 
        onClose={() => setDeleteDialogOpen(false)}
        PaperProps={{ sx: { borderRadius: '12px 0 12px 12px' } }}
      >
        <DialogTitle>Delete Functional Area</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete "{selectedArea?.name}"? This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleDelete} color="error" variant="contained">
            Delete
          </Button>
        </DialogActions>
      </Dialog>

      {/* Management Dialog */}
      <FunctionalAreaManagementDialog
        open={managementDialogOpen}
        onClose={() => setManagementDialogOpen(false)}
        functionalArea={selectedArea}
        onUpdate={fetchFunctionalAreas}
      />
    </Box>
  );
};

export default FunctionalAreasPage;