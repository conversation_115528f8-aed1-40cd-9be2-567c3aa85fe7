import React, { useState, useEffect, useContext } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Typography,
  Box,
  Paper,
  Card,
  CardContent,
  Chip,
  CircularProgress,
  Alert,
  Button,
  Divider,
  Grid,
  List,
  ListItem
} from '@mui/material';
import { Build as BuildIcon } from '@mui/icons-material';
import { AuthContext } from '../App';
import { getIssueDetails } from '../services/api';
import BugFixConsole from '../components/BugFixConsole';

// Helper functions from IssuesPage.js
const getStatusColor = (status) => {
  const statusLower = status?.toLowerCase() || '';

  if (statusLower.includes('todo') || statusLower.includes('to do') || statusLower.includes('backlog')) {
    return 'default';
  } else if (statusLower.includes('progress') || statusLower.includes('in dev')) {
    return 'primary';
  } else if (statusLower.includes('review') || statusLower.includes('qa')) {
    return 'info';
  } else if (statusLower.includes('done') || statusLower.includes('resolved') || statusLower.includes('closed')) {
    return 'success';
  } else if (statusLower.includes('blocked') || statusLower.includes('impediment')) {
    return 'error';
  }

  return 'default';
};

const getPriorityDisplay = (priority) => {
  const priorityLower = priority?.toLowerCase() || '';

  if (priorityLower.includes('highest') || priorityLower.includes('blocker')) {
    return { text: 'Highest', color: 'error' };
  } else if (priorityLower.includes('high')) {
    return { text: 'High', color: 'warning' };
  } else if (priorityLower.includes('medium') || priorityLower.includes('normal')) {
    return { text: 'Medium', color: 'info' };
  } else if (priorityLower.includes('low')) {
    return { text: 'Low', color: 'success' };
  } else if (priorityLower.includes('lowest')) {
    return { text: 'Lowest', color: 'default' };
  }

  return { text: priority || 'Unknown', color: 'default' };
};

// Format date for human readability
const formatDate = (dateString) => {
  if (!dateString) return 'Unknown';

  const date = new Date(dateString);
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date);
};

// Helper function to safely render JIRA content that might be an object
const renderJiraContent = (content) => {
  if (!content) return '';

  // If content is a string, return it directly
  if (typeof content === 'string') {
    return content;
  }

  // If content is an object with Atlassian Document Format (ADF)
  if (typeof content === 'object') {
    // Check if it's an ADF object with content, type, and version properties
    if (content.content && content.type && content.version) {
      try {
        // For now, just extract text from the content
        // This is a simplified approach - a full ADF renderer would be more complex
        return extractTextFromADF(content);
      } catch (error) {
        console.error('Error rendering ADF content:', error);
        return 'Error rendering content';
      }
    }

    // If it's some other kind of object, try to stringify it
    try {
      return JSON.stringify(content);
    } catch (error) {
      console.error('Error stringifying content:', error);
      return 'Error rendering content';
    }
  }

  // Fallback
  return String(content);
};

// Helper function to extract text from Atlassian Document Format (ADF)
const extractTextFromADF = (adf) => {
  if (!adf?.content) return '';

  let text = '';

  // Recursively extract text from content
  const extractText = (node) => {
    if (typeof node === 'string') {
      return node;
    }

    if (node.text) {
      return node.text;
    }

    if (node.content && Array.isArray(node.content)) {
      return node.content.map(extractText).join('');
    }

    return '';
  };

  if (Array.isArray(adf.content)) {
    text = adf.content.map(extractText).join('\n\n');
  }

  return text;
};

const IssueDetailsPage = () => {
  const { issueKey } = useParams();
  const navigate = useNavigate();
  const { authState } = useContext(AuthContext);
  const [issue, setIssue] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [consoleOpen, setConsoleOpen] = useState(false);

  useEffect(() => {
    if (!issueKey) {
      navigate('/projects');
      return;
    }

    const fetchIssueDetails = async () => {
      try {
        setLoading(true);
        setError(null);

        console.log(`Fetching details for issue ${issueKey}`);
        const data = await getIssueDetails(authState.tenantId, issueKey);

        console.log('Issue details received:', data);

        if (!data?.key) {
          console.error('Invalid issue data received:', data);
          throw new Error('Invalid issue data received from server');
        }

        setIssue(data);
      } catch (err) {
        console.error('Error fetching issue details:', err);
        let errorMessage = 'Failed to load issue details';

        // Extract error details from response if available
        if (err.response?.data?.detail) {
          errorMessage = err.response.data.detail;
        } else if (err.message) {
          errorMessage = err.message;
        }

        // Check for specific error conditions
        if (err.response?.status === 401) {
          errorMessage = 'Your JIRA API credentials are invalid or expired. Please update them in settings.';
        } else if (err.response?.status === 404) {
          errorMessage = `Issue "${issueKey}" not found in JIRA or you don't have access to it.`;
        } else if (err.response?.status === 403) {
          errorMessage = 'You do not have permission to access this issue in JIRA.';
        }

        setError(errorMessage);
      } finally {
        setLoading(false);
      }
    };

    fetchIssueDetails();
  }, [issueKey, authState.tenantId, navigate]);

  const handleBackToIssues = () => {
    // Extract project key from issue key (e.g., "ABC-123" -> "ABC")
    const projectKey = issueKey.split('-')[0];
    navigate(`/projects/${projectKey}`);
  };

  // Render different content based on loading/error/data state
  const renderContent = () => {
    if (loading) {
      return (
        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
          <CircularProgress />
        </Box>
      );
    }

    if (error) {
      return (
        <Alert severity="error" sx={{ mt: 2 }}>
          {error}
        </Alert>
      );
    }

    if (!issue) {
      return (
        <Alert severity="info" sx={{ mt: 2 }}>
          No issue found with key {issueKey}.
        </Alert>
      );
    }

    // Issue data is available
    return (
      <Box>
        <Box sx={{ mb: 3 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h4" color="text.primary">
              {issue.key} - {issue.fields?.summary}
            </Typography>
            {issue.fields?.issuetype?.name === 'Bug' && (
              <Button
                variant="contained"
                color="primary"
                startIcon={<BuildIcon />}
                onClick={() => setConsoleOpen(true)}
                sx={{ borderRadius: '12px 0 12px 12px' }}
              >
                Fix Bug
              </Button>
            )}
          </Box>

          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2, gap: 2 }}>
            <Chip
              label={issue.fields?.status?.name || 'Unknown Status'}
              color={getStatusColor(issue.fields?.status?.name)}
              sx={{ borderRadius: '12px 0 12px 12px' }}
            />

            {issue.fields?.priority && (
              <Chip
                label={getPriorityDisplay(issue.fields.priority.name).text}
                color={getPriorityDisplay(issue.fields.priority.name).color}
                sx={{ borderRadius: '12px 0 12px 12px' }}
              />
            )}
          </Box>
        </Box>

        <Grid container spacing={3}>
          <Grid item xs={12} md={8}>
            <Card sx={{ mb: 3, borderRadius: '12px 0 12px 12px', overflow: 'hidden' }}>
              <CardContent>
                <Typography variant="h6" gutterBottom color="primary">
                  Description
                </Typography>
                <Typography variant="body1" component="div" sx={{ whiteSpace: 'pre-line' }}>
                  {renderJiraContent(issue.fields?.description) || 'No description provided.'}
                </Typography>
              </CardContent>
            </Card>

            {issue.fields?.comment?.comments && issue.fields.comment.comments.length > 0 && (
              <Card sx={{ mb: 3, borderRadius: '12px 0 12px 12px', overflow: 'hidden' }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom color="primary">
                    Comments ({issue.fields.comment.comments.length})
                  </Typography>
                  <List>
                    {issue.fields.comment.comments.map((comment, index) => (
                      <React.Fragment key={comment.id}>
                        {index > 0 && <Divider variant="middle" sx={{ my: 2 }} />}
                        <ListItem alignItems="flex-start" sx={{ flexDirection: 'column' }}>
                          <Box sx={{ display: 'flex', width: '100%', mb: 1, justifyContent: 'space-between' }}>
                            <Typography variant="subtitle2" color="text.primary">
                              {comment.author?.displayName || 'Unknown User'}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {formatDate(comment.created)}
                            </Typography>
                          </Box>
                          <Typography variant="body2" sx={{ whiteSpace: 'pre-line' }}>
                            {renderJiraContent(comment.body)}
                          </Typography>
                        </ListItem>
                      </React.Fragment>
                    ))}
                  </List>
                </CardContent>
              </Card>
            )}
          </Grid>

          <Grid item xs={12} md={4}>
            <Card sx={{ mb: 3, borderRadius: '12px 0 12px 12px', overflow: 'hidden' }}>
              <CardContent>
                <Typography variant="h6" gutterBottom color="primary">
                  Details
                </Typography>

                <Box sx={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 2 }}>
                  <Box>
                    <Typography variant="caption" color="text.secondary">Assignee</Typography>
                    <Typography variant="body2">
                      {issue.fields?.assignee?.displayName || 'Unassigned'}
                    </Typography>
                  </Box>

                  <Box>
                    <Typography variant="caption" color="text.secondary">Reporter</Typography>
                    <Typography variant="body2">
                      {issue.fields?.reporter?.displayName || 'Unknown'}
                    </Typography>
                  </Box>

                  <Box>
                    <Typography variant="caption" color="text.secondary">Created</Typography>
                    <Typography variant="body2">
                      {formatDate(issue.fields?.created)}
                    </Typography>
                  </Box>

                  <Box>
                    <Typography variant="caption" color="text.secondary">Updated</Typography>
                    <Typography variant="body2">
                      {formatDate(issue.fields?.updated)}
                    </Typography>
                  </Box>

                  {issue.fields?.components && issue.fields.components.length > 0 && (
                    <Box sx={{ gridColumn: '1 / span 2' }}>
                      <Typography variant="caption" color="text.secondary">Components</Typography>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 0.5 }}>
                        {issue.fields.components.map(component => (
                          <Chip
                            key={component.id || component.name}
                            label={component.name}
                            size="small"
                            sx={{ borderRadius: '8px 0 8px 8px' }}
                          />
                        ))}
                      </Box>
                    </Box>
                  )}

                  {issue.fields?.labels && issue.fields.labels.length > 0 && (
                    <Box sx={{ gridColumn: '1 / span 2' }}>
                      <Typography variant="caption" color="text.secondary">Labels</Typography>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 0.5 }}>
                        {issue.fields.labels.map(label => (
                          <Chip
                            key={label}
                            label={label}
                            size="small"
                            variant="outlined"
                            sx={{ borderRadius: '8px 0 8px 8px' }}
                          />
                        ))}
                      </Box>
                    </Box>
                  )}
                </Box>
              </CardContent>
            </Card>

            <Card sx={{ borderRadius: '12px 0 12px 12px', overflow: 'hidden' }}>
              <CardContent>
                <Typography variant="h6" gutterBottom color="primary">
                  Open in JIRA
                </Typography>
                <Button
                  variant="contained"
                  color="primary"
                  fullWidth
                  component="a"
                  href={issue.self ? issue.self.replace('/rest/api/3/issue/', '/browse/') : `https://jira.example.com/browse/${issue.key}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  sx={{ borderRadius: '8px 0 8px 8px' }}
                >
                  View in JIRA
                </Button>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Box>
    );
  };

  return (
    <Paper
      elevation={0}
      sx={{
        padding: 3,
        height: '100%',
        overflow: 'auto'
      }}
    >
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Box>
          <Button
            variant="outlined"
            color="primary"
            onClick={handleBackToIssues}
            sx={{ mb: 2 }}
          >
            ← Back to Issues
          </Button>
        </Box>
      </Box>

      {renderContent()}
      
      {/* Bug Fix Console */}
      <BugFixConsole
        open={consoleOpen}
        onClose={() => setConsoleOpen(false)}
        issueKey={issueKey}
        issueDetails={issue}
        tenantId={authState.tenantId}
        projectKey={issueKey.split('-')[0]}
      />
    </Paper>
  );
};

export default IssueDetailsPage;