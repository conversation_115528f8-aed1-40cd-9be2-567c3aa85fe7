import React, { useState, useEffect, useContext } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate, Link } from 'react-router-dom';
import {
  Typography,
  Box,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  Chip,
  CircularProgress,
  Alert,
  Pagination,
  TextField,
  InputAdornment,
  IconButton
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import ClearIcon from '@mui/icons-material/Clear';
import { AuthContext } from '../App';
import { listIssuesForProject, trackProjectAccess } from '../services/api';

// Status color mapping for issue status
const getStatusColor = (status) => {
  const statusLower = status?.toLowerCase() || '';
  
  if (statusLower.includes('todo') || statusLower.includes('to do') || statusLower.includes('backlog')) {
    return 'default';
  } else if (statusLower.includes('progress') || statusLower.includes('in dev')) {
    return 'primary';
  } else if (statusLower.includes('review') || statusLower.includes('qa')) {
    return 'info';
  } else if (statusLower.includes('done') || statusLower.includes('resolved') || statusLower.includes('closed')) {
    return 'success';
  } else if (statusLower.includes('blocked') || statusLower.includes('impediment')) {
    return 'error';
  }
  
  return 'default';
};

// Priority level to display format
const getPriorityDisplay = (priority) => {
  const priorityLower = priority?.toLowerCase() || '';
  
  if (priorityLower.includes('highest') || priorityLower.includes('blocker')) {
    return { text: 'Highest', color: 'error' };
  } else if (priorityLower.includes('high')) {
    return { text: 'High', color: 'warning' };
  } else if (priorityLower.includes('medium') || priorityLower.includes('normal')) {
    return { text: 'Medium', color: 'info' };
  } else if (priorityLower.includes('low')) {
    return { text: 'Low', color: 'success' };
  } else if (priorityLower.includes('lowest')) {
    return { text: 'Lowest', color: 'default' };
  }
  
  return { text: priority || 'Unknown', color: 'default' };
};

// Format date to relative time (e.g., "2 days ago")
const formatRelativeTime = (dateString) => {
  if (!dateString) return 'Unknown';
  
  const date = new Date(dateString);
  const now = new Date();
  const diffInMs = now - date;
  const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));
  
  if (diffInDays === 0) {
    const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));
    if (diffInHours === 0) {
      const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
      return diffInMinutes <= 1 ? 'Just now' : `${diffInMinutes} minutes ago`;
    }
    return diffInHours === 1 ? '1 hour ago' : `${diffInHours} hours ago`;
  } else if (diffInDays === 1) {
    return 'Yesterday';
  } else if (diffInDays < 30) {
    return `${diffInDays} days ago`;
  } else {
    return date.toLocaleDateString();
  }
};

const IssuesPage = () => {
  const { projectKey } = useParams();
  const navigate = useNavigate();
  const { authState } = useContext(AuthContext);
  const [issues, setIssues] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [projectInfo, setProjectInfo] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState('');
  
  // Debounce the search query
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);
      setPage(1); // Reset to first page when search changes
    }, 500); // 500ms delay

    return () => clearTimeout(timer);
  }, [searchQuery]);
  
  useEffect(() => {
    if (!projectKey) {
      navigate('/projects');
      return;
    }
    
    // Track that the user viewed this project
    const trackProject = async () => {
      try {
        await trackProjectAccess(authState.tenantId, projectKey);
      } catch (err) {
        console.error('Error tracking project access:', err);
        // Continue even if tracking fails
      }
    };
    
    trackProject();
    
    const fetchIssues = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Fetching issues from JIRA API via our backend
        console.log(`Fetching issues for project ${projectKey}, page ${page}, search: ${debouncedSearchQuery}`);
        const data = await listIssuesForProject(authState.tenantId, projectKey, { 
          page, 
          search: debouncedSearchQuery 
        });
        
        console.log('Issues data received:', data);
        
        // Validate the data structure
        if (!data || !Array.isArray(data.issues)) {
          console.error('Invalid data format received:', data);
          throw new Error('Invalid data format received from server');
        }
        
        // Set state with the received data
        setIssues(data.issues);
        setTotalPages(Math.ceil((data.total || 0) / 25)); // Assuming 25 items per page
        setProjectInfo(data.project || { key: projectKey, name: projectKey });
        
        console.log(`Loaded ${data.issues.length} issues for project ${projectKey}`);
      } catch (err) {
        console.error('Error fetching issues:', err);
        let errorMessage = 'Failed to load issues for this project';
        
        // Extract error details from response if available
        if (err.response?.data?.detail) {
          errorMessage = err.response.data.detail;
        } else if (err.message) {
          errorMessage = err.message;
        }
        
        // Check for specific error conditions
        if (err.response?.status === 401) {
          errorMessage = 'Your JIRA API credentials are invalid or expired. Please update them in settings.';
        } else if (err.response?.status === 404) {
          errorMessage = `Project "${projectKey}" not found in JIRA or you don't have access to it.`;
        } else if (err.response?.status === 403) {
          errorMessage = 'You do not have permission to access this project in JIRA.';
        } else if (err.response?.status === 400) {
          // JIRA API specific errors
          if (err.response?.data?.detail?.includes("does not exist")) {
            errorMessage = `${err.response.data.detail} Please check that this project exists in your JIRA instance.`;
          } else {
            errorMessage = `JIRA API Error: ${err.response.data.detail || 'Unknown error'}`;
          }
        } else if (err.response?.status === 500) {
          // Check if it's a configuration error with JIRA credentials
          if (err.response?.data?.detail?.includes('API key') || 
              err.response?.data?.detail?.includes('encrypted') ||
              err.response?.data?.title === 'Configuration Error') {
            errorMessage = `Configuration error: ${err.response.data.detail}. Please update your JIRA credentials in settings.`;
          } else {
            errorMessage = `A server error occurred while fetching issues. ${err.response?.data?.detail || ''}`;
          }
        }
        
        setError(errorMessage);
      } finally {
        setLoading(false);
      }
    };
    
    fetchIssues();
  }, [projectKey, authState.tenantId, page, debouncedSearchQuery, navigate]);
  
  const handlePageChange = (event, value) => {
    setPage(value);
  };
  
  const handleRowClick = (issueKey) => {
    // Navigate to issue details page
    navigate(`/issues/${issueKey}`);
  };
  
  const handleBackToProjects = () => {
    navigate('/projects');
  };
  
  return (
    <Paper
      elevation={0}
      sx={{
        padding: 3,
        height: '100%'
      }}
    >
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Box>
          <Button 
            variant="outlined" 
            color="primary" 
            onClick={handleBackToProjects}
            sx={{ mb: 2 }}
          >
            ← Back to Projects
          </Button>
          <Typography variant="h4" gutterBottom color="text.primary">
            {projectInfo?.name || 'Project'} Issues
          </Typography>
          <Typography variant="body1" color="text.secondary" paragraph>
            Viewing bug issues for project {projectKey}
          </Typography>
        </Box>
      </Box>
      
      {/* Search Box */}
      <Box sx={{ mb: 3 }}>
        <TextField
          fullWidth
          variant="outlined"
          placeholder="Search issues by summary, description, or key..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
            endAdornment: searchQuery && (
              <InputAdornment position="end">
                <IconButton
                  size="small"
                  onClick={() => setSearchQuery('')}
                  edge="end"
                >
                  <ClearIcon />
                </IconButton>
              </InputAdornment>
            ),
          }}
          sx={{
            '& .MuiOutlinedInput-root': {
              borderRadius: '12px 0 12px 12px',
            }
          }}
        />
      </Box>
      
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
          <CircularProgress />
        </Box>
      ) : error ? (
        <Box sx={{ mt: 2 }}>
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
          {error.includes('JIRA credentials') || 
            error.includes('Configuration error') ||
            error.includes('API key') ||
            error.includes('authentication') ||
            error.includes('expired') ? (
            <Button 
              variant="contained" 
              color="primary" 
              component={Link}
              to="/jira-settings"
              sx={{ mt: 1, borderRadius: '12px 0 12px 12px' }}
            >
              Update JIRA Settings
            </Button>
          ) : error.includes('permission') || error.includes('access') ? (
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              This may be due to insufficient permissions in your JIRA account.
              Check with your JIRA administrator to ensure you have access to this project.
            </Typography>
          ) : error.includes('not found') || error.includes('does not exist') ? (
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              Check that the project key is correct and exists in your JIRA instance.
            </Typography>
          ) : null}
        </Box>
      ) : issues.length === 0 ? (
        <Box sx={{ mt: 4, p: 3, bgcolor: 'rgba(25, 118, 210, 0.04)', borderRadius: '12px 0 12px 12px' }}>
          <Typography variant="h6" color="primary" gutterBottom>No Issues Found</Typography>
          <Typography variant="body1" paragraph>
            There are no bug issues in this project, or your access is limited.
          </Typography>
        </Box>
      ) : (
        <>
          <TableContainer
            component={Paper}
            elevation={1}
            sx={{
              borderRadius: '12px 0 12px 12px',
              overflow: 'hidden',
              mb: 2
            }}
          >
            <Table sx={{ minWidth: 650 }}>
              <TableHead sx={{ bgcolor: 'rgba(32, 121, 166, 0.08)' }}>
                <TableRow>
                  <TableCell><Typography variant="subtitle2">Key</Typography></TableCell>
                  <TableCell><Typography variant="subtitle2">Summary</Typography></TableCell>
                  <TableCell><Typography variant="subtitle2">Status</Typography></TableCell>
                  <TableCell><Typography variant="subtitle2">Priority</Typography></TableCell>
                  <TableCell><Typography variant="subtitle2">Assignee</Typography></TableCell>
                  <TableCell><Typography variant="subtitle2">Updated</Typography></TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {issues.map((issue) => (
                  <TableRow
                    key={issue.key}
                    hover
                    onClick={() => handleRowClick(issue.key)}
                    sx={{
                      cursor: 'pointer',
                      transition: 'all 0.15s ease-out',
                      '&:hover': {
                        backgroundColor: 'rgba(32, 121, 166, 0.04)',
                        transform: 'translateY(-2px)'
                      }
                    }}
                  >
                    <TableCell>
                      <Typography variant="body2">{issue.key}</Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">{issue.fields?.summary || 'No summary'}</Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={issue.fields?.status?.name || 'Unknown'}
                        size="small"
                        color={getStatusColor(issue.fields?.status?.name)}
                        sx={{ minWidth: 80 }}
                      />
                    </TableCell>
                    <TableCell>
                      {issue.fields?.priority && (
                        <Chip
                          label={getPriorityDisplay(issue.fields.priority.name).text}
                          size="small"
                          color={getPriorityDisplay(issue.fields.priority.name).color}
                          sx={{ minWidth: 70 }}
                        />
                      )}
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {issue.fields?.assignee?.displayName || 'Unassigned'}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {formatRelativeTime(issue.fields?.updated)}
                      </Typography>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
          
          {totalPages > 1 && (
            <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
              <Pagination
                count={totalPages}
                page={page}
                onChange={handlePageChange}
                color="primary"
                showFirstButton
                showLastButton
                sx={{
                  '& .MuiPaginationItem-root': {
                    borderRadius: '12px 0 12px 12px'
                  }
                }}
              />
            </Box>
          )}
        </>
      )}
    </Paper>
  );
};

export default IssuesPage;