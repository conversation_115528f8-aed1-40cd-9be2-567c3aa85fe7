import React, { useState, useEffect, useContext } from 'react';
import {
  Typo<PERSON>,
  Box,
  Paper,
  TextField,
  Button,
  Alert,
  Card,
  CardContent,
  CardHeader,
  Divider,
  CircularProgress,
  IconButton,
  InputAdornment
} from '@mui/material';
import { AuthContext } from '../App';
import { getJiraCredentialStatus, manageJiraCredentials } from '../services/api';

// Mock for Lucide React Icons
const VisibilityIcon = () => <span>👁️</span>;
const VisibilityOffIcon = () => <span>👁️‍🗨️</span>;
const SaveIcon = () => <span>💾</span>;
const DeleteIcon = () => <span>🗑️</span>;

const JiraSettingsPage = () => {
  const { authState } = useContext(AuthContext);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [deleting, setDeleting] = useState(false);
  const [error, setError] = useState(null);
  const [success, setSuccess] = useState(null);
  const [credentials, setCredentials] = useState({
    configured: false,
    base_url: '',
    last_updated: null,
    configured_by: null
  });
  const [formData, setFormData] = useState({
    base_url: '',
    api_key: '',
    email: ''
  });
  const [showApiKey, setShowApiKey] = useState(false);

  // Fetch current JIRA credential status
  useEffect(() => {
    if (!authState.tenantId) return;
    
    setLoading(true);
    getJiraCredentialStatus(authState.tenantId)
      .then(data => {
        setCredentials(data);
        
        // Set warning if credentials are configured but have invalid format
        if (data.configured && data.valid_format === false) {
          setError('JIRA credentials are stored but may be corrupted. Consider updating them.');
        }
        
        // Update form with existing base URL if available
        if (data.configured && data.base_url) {
          setFormData(prev => ({
            ...prev,
            base_url: data.base_url,
            email: data.email || '' // Add email if available
          }));
        }
      })
      .catch(err => {
        console.error('Error fetching JIRA credentials:', err);
        
        // Handle 401 errors (API interceptor will handle the redirect)
        if (err.response && err.response.status === 401) {
          setError('Your session has expired. You will be redirected to login.');
          return;
        }
        
        setError('Failed to fetch JIRA credential status');
      })
      .finally(() => {
        setLoading(false);
      });
  }, [authState.tenantId]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSave = async (e) => {
    e.preventDefault();
    
    // Form validation
    if (!formData.base_url) {
      setError('JIRA Base URL is required');
      return;
    }
    
    if (!formData.api_key) {
      setError('JIRA API Key is required');
      return;
    }
    
    if (!formData.email) {
      setError('Email associated with the API key is required');
      return;
    }
    
    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      setError('Please enter a valid email address');
      return;
    }
    
    // Normalize JIRA base URL (remove trailing slash)
    const normalizedBaseUrl = formData.base_url.endsWith('/') 
      ? formData.base_url.slice(0, -1) 
      : formData.base_url;
    
    const normalizedFormData = {
      ...formData,
      base_url: normalizedBaseUrl
    };
    
    setError(null);
    setSaving(true);
    
    try {
      const response = await manageJiraCredentials(authState.tenantId, normalizedFormData);
      
      if (response && (response.success || response.message)) {
        setSuccess('JIRA credentials saved successfully!');
        
        // Update credential status
        const updatedStatus = await getJiraCredentialStatus(authState.tenantId);
        setCredentials(updatedStatus);
        
        // Clear API key for security, keep email and base URL
        setFormData(prev => ({
          ...prev,
          api_key: '',
          base_url: normalizedBaseUrl,
          email: formData.email
        }));
      } else {
        setError('Failed to save JIRA credentials. Please try again.');
      }
    } catch (err) {
      console.error('Error saving JIRA credentials:', err);
      // Extract error details from various possible response formats
      const errorDetail = err.response?.data?.detail || 
                          err.response?.data?.message || 
                          err.message || 
                          'Failed to save JIRA credentials';
      setError(errorDetail);
    } finally {
      setSaving(false);
    }
  };

  const handleDelete = async () => {
    if (!window.confirm('Are you sure you want to delete your JIRA credentials?')) {
      return;
    }
    
    setError(null);
    setDeleting(true);
    
    try {
      // Use the API base URL from our configured API client
      const baseUrl = process.env.REACT_APP_API_BASE_URL || 'http://localhost:5045';
      const response = await fetch(`${baseUrl}/tenants/${authState.tenantId}/jira/credentials`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${authState.token}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        
        if (data.success) {
          setSuccess('JIRA credentials deleted successfully');
          
          // Update credential status
          const updatedStatus = await getJiraCredentialStatus(authState.tenantId);
          setCredentials(updatedStatus);
          
          // Clear form
          setFormData({
            base_url: '',
            api_key: '',
            email: ''
          });
        } else {
          setError(data.message || 'Failed to delete JIRA credentials');
        }
      } else {
        try {
          const errorData = await response.json();
          setError(errorData.detail || `Failed to delete JIRA credentials (${response.status})`);
        } catch (e) {
          // If parsing the error response fails
          setError(`Failed to delete JIRA credentials. Server returned ${response.status}`);
        }
      }
    } catch (err) {
      console.error('Error deleting JIRA credentials:', err);
      setError(`Network error: ${err.message || 'Failed to connect to server'}`);
    } finally {
      setDeleting(false);
    }
  };

  // Clear success message after 5 seconds
  useEffect(() => {
    if (success) {
      const timer = setTimeout(() => {
        setSuccess(null);
      }, 5000);
      
      return () => clearTimeout(timer);
    }
  }, [success]);

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Paper elevation={0} sx={{ padding: 3 }}>
      <Typography variant="h4" gutterBottom color="text.primary">
        JIRA Settings
      </Typography>
      
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}
      
      {success && (
        <Alert severity="success" sx={{ mb: 3 }}>
          {success}
        </Alert>
      )}
      
      {credentials.configured && (
        <Card sx={{ mb: 4, bgcolor: 'rgba(25, 118, 210, 0.04)', borderRadius: '12px 0 12px 12px' }}>
          <CardHeader 
            title="Current JIRA Configuration" 
            sx={{ backgroundColor: 'primary.main', color: 'white', borderRadius: '12px 0 0 0' }}
          />
          <CardContent>
            <Typography variant="body1" gutterBottom>
              <strong>Base URL:</strong> {credentials.base_url}
            </Typography>
            
            {credentials.last_updated && (
              <Typography variant="body2" color="text.secondary">
                <strong>Last Updated:</strong> {new Date(credentials.last_updated).toLocaleString()}
              </Typography>
            )}
            
            {credentials.configured_by && (
              <Typography variant="body2" color="text.secondary">
                <strong>Configured By:</strong> {credentials.configured_by}
              </Typography>
            )}
            
            <Box sx={{ mt: 2 }}>
              <Button
                variant="outlined" 
                color="error"
                startIcon={<DeleteIcon />}
                onClick={handleDelete}
                disabled={deleting}
              >
                {deleting ? 'Deleting...' : 'Delete Credentials'}
              </Button>
            </Box>
          </CardContent>
        </Card>
      )}
      
      <Card sx={{ bgcolor: 'background.paper', borderRadius: '12px 0 12px 12px' }}>
        <CardHeader 
          title={credentials.configured ? "Update JIRA Credentials" : "Add JIRA Credentials"} 
          sx={{ backgroundColor: 'secondary.main', color: 'white', borderRadius: '12px 0 0 0' }}
        />
        <CardContent>
          <Typography variant="body2" color="text.secondary" paragraph>
            {credentials.configured 
              ? "Update your JIRA credentials below. Your API key will be encrypted and stored securely."
              : "Connect to your JIRA instance by providing your JIRA base URL and API key. The API key will be encrypted and stored securely."}
          </Typography>
          
          <Box component="form" onSubmit={handleSave} sx={{ mt: 2 }}>
            <TextField
              fullWidth
              label="JIRA Base URL"
              name="base_url"
              value={formData.base_url}
              onChange={handleInputChange}
              margin="normal"
              placeholder="https://your-domain.atlassian.net"
              helperText="The base URL of your JIRA instance (e.g., https://your-domain.atlassian.net)"
              required
            />
            
            <TextField
              fullWidth
              label="Email Address"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              margin="normal"
              type="email"
              placeholder="<EMAIL>"
              helperText="The email address associated with your JIRA API token"
              required
            />
            
            <TextField
              fullWidth
              label="JIRA API Key"
              name="api_key"
              value={formData.api_key}
              onChange={handleInputChange}
              margin="normal"
              type={showApiKey ? "text" : "password"}
              placeholder="Enter your JIRA API key"
              helperText="Your personal API key for JIRA. This will be encrypted before storage."
              required
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      onClick={() => setShowApiKey(!showApiKey)}
                      edge="end"
                    >
                      {showApiKey ? <VisibilityOffIcon /> : <VisibilityIcon />}
                    </IconButton>
                  </InputAdornment>
                )
              }}
            />
            
            <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}>
              <Button
                type="submit"
                variant="contained"
                color="primary"
                startIcon={<SaveIcon />}
                disabled={saving}
              >
                {saving ? 'Saving...' : 'Save Credentials'}
              </Button>
            </Box>
          </Box>
        </CardContent>
      </Card>
      
      <Box sx={{ mt: 4 }}>
        <Divider sx={{ mb: 2 }} />
        <Typography variant="h6" gutterBottom>Need Help?</Typography>
        <Typography variant="body2" color="text.secondary">
          To set up your JIRA credentials:
          <ol>
            <li>Log in to your Atlassian account</li>
            <li>Go to Account Settings &gt; Security &gt; API tokens</li>
            <li>Create and copy a new API token</li>
            <li>Use your Atlassian account email and the new token in the form above</li>
          </ol>
          <Typography variant="body2" paragraph sx={{ mt: 1, fontWeight: 'bold' }}>
            Important: The email address must be the one associated with your Atlassian account.
          </Typography>
          <Box sx={{ mt: 1 }}>
            <a href="https://support.atlassian.com/atlassian-account/docs/manage-api-tokens-for-your-atlassian-account/" 
               target="_blank" 
               rel="noopener noreferrer">
              Atlassian API Token Documentation
            </a>
          </Box>
        </Typography>
      </Box>
    </Paper>
  );
};

export default JiraSettingsPage;