import React, { useState, useContext } from 'react';
import { useNavigate } from 'react-router-dom';
import { AuthContext } from '../App'; // Adjust path if AuthContext is moved
import { Box, Button, TextField, Typography, Paper, Container, CircularProgress, Alert } from '@mui/material';
import { loginUser, setAuthToken, resolveTenantByName } from '../services/api'; 
import { jwtDecode } from 'jwt-decode'; // Utility to decode JWT tokens

// Helper for Kasavu-cut styling
const KasavuCard = (props) => (
  <Paper 
    elevation={3} 
    sx={{
      padding: 4, // Approx 24px based on 4pt grid (theme.spacing(6))
      marginTop: 8, // theme.spacing(8)
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      borderRadius: '12px 0 12px 12px', // Kasavu-cut
      boxShadow: '0 2px 4px rgba(0,0,0,.06)', // General UI Spec subtle shadow
      bgcolor: 'background.paper' // Uses --mk-shadow-100 from theme
    }}
    {...props}
  />
);

const LoginPage = () => {
  const navigate = useNavigate();
  const { login: updateAuthContextLogin } = useContext(AuthContext); // Renamed to avoid confusion
  const [email, setEmail] = useState('<EMAIL>'); // Pre-fill for easier testing
  const [password, setPassword] = useState('password123'); // Pre-fill for easier testing
  const [tenantNameOrSlug, setTenantNameOrSlug] = useState('DefaultTenant'); // Pre-fill with seeded tenant name
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (event) => {
    event.preventDefault();
    setLoading(true);
    setError(''); // Clear previous errors
    const data = new FormData(event.currentTarget);
    const tenantName = data.get('tenantName');
    const email = data.get('email');
    const password = data.get('password');

    console.log('[LoginPage] Attempting to resolve tenant:', tenantName);

    if (!tenantName || !email || !password) {
      setError('All fields are required.');
      console.error('[LoginPage] Missing fields');
      setLoading(false);
      return;
    }

    try {
      // Step 1: Resolve tenant name to tenant ID
      const tenantResponse = await resolveTenantByName(tenantName);
      console.log('[LoginPage] Tenant resolution response:', tenantResponse);

      if (!tenantResponse || !tenantResponse.id) {
        setError(tenantResponse?.detail || `Could not find tenant: ${tenantName}`);
        console.error('[LoginPage] Tenant not found or invalid response:', tenantResponse);
        setLoading(false);
        return;
      }
      const tenantId = tenantResponse.id;
      console.log('[LoginPage] Tenant ID resolved:', tenantId);

      // Step 2: Log in user with tenant ID
      console.log('[LoginPage] Attempting login for user:', email, 'with tenantId:', tenantId);
      const loginResponse = await loginUser({ tenantId, email, password });
      console.log('[LoginPage] Login response:', loginResponse);

      if (loginResponse && loginResponse.access_token && loginResponse.refresh_token) {
        setAuthToken(loginResponse.access_token);

        let decodedToken;
        try {
          decodedToken = jwtDecode(loginResponse.access_token);
        } catch (e) {
          console.error("Invalid token received:", e);
          setError("Received an invalid token from the server.");
          setLoading(false);
          return;
        }
        
        const authData = {
          access_token: loginResponse.access_token,
          refresh_token: loginResponse.refresh_token,
          email: decodedToken.email, 
          roles: decodedToken.roles || [],
          userId: decodedToken.sub, // 'sub' is the standard claim for user ID
          tenantId: decodedToken.tenant_id || tenantId, // Ensure this matches currentTenantId or handle mismatch
        };

        if (authData.tenantId !== tenantId) {
          console.warn("Tenant ID from token does not match resolved tenant ID. Using token's tenant ID.");
          // This might indicate an issue or a specific backend logic. For AuthContext, use what token says.
        }

        updateAuthContextLogin(authData); // Update AuthContext
        navigate('/'); 
      } else {
        setError(loginResponse?.detail || loginResponse?.msg || 'Login failed. Please check your credentials.');
        console.error('[LoginPage] Login failed, response:', loginResponse);
      }
    } catch (err) {
      console.error('[LoginPage] Error during login process:', err);
      let errorMessage = 'An unexpected error occurred.';
      if (err.response && err.response.data && err.response.data.detail) {
        errorMessage = err.response.data.detail;
      } else if (err.message) {
        errorMessage = err.message;
      }
      setError(errorMessage);
    }
    setLoading(false);
  };

  return (
    <Container component="main" maxWidth="xs">
      <KasavuCard>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 2 }}>
          <Box 
            component="img"
            src="/assets/repair.png"
            alt="JIFFY BUG FIX Logo"
            sx={{ 
              height: 40, 
              mr: 1
            }}
          />
          <Typography component="h1" variant="h5" color="text.primary">
            JIFFY BUG FIX
          </Typography>
        </Box>
        <Typography variant="subtitle1" sx={{ mt: 2, mb: 2, color: 'text.secondary' }}>
          Sign In
        </Typography>
        
        <Box component="form" onSubmit={handleSubmit} noValidate sx={{ mt: 1, width: '100%' }}>
          <TextField
            variant="outlined"
            margin="normal"
            required
            fullWidth
            id="tenantName"
            label="Tenant Name/Slug"
            name="tenantName"
            autoComplete="organization"
            value={tenantNameOrSlug}
            onChange={(e) => setTenantNameOrSlug(e.target.value)}
            sx={{ borderRadius: '12px 0 12px 12px' }}
            autoFocus
          />
          <TextField
            variant="outlined"
            margin="normal"
            required
            fullWidth
            id="email"
            label="Email Address"
            name="email"
            autoComplete="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            sx={{ borderRadius: '12px 0 12px 12px' }}
          />
          <TextField
            variant="outlined"
            margin="normal"
            required
            fullWidth
            name="password"
            label="Password"
            type="password"
            id="password"
            autoComplete="current-password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            sx={{ borderRadius: '12px 0 12px 12px' }}
          />
          <Button
            type="submit"
            fullWidth
            variant="contained"
            color="primary"
            disabled={loading}
            sx={{
              mt: 3,
              mb: 2,
              padding: '10px 20px',
              fontWeight: 500,
              borderRadius: '12px 0 12px 12px', // Kasavu-cut
              '&:hover': { backgroundColor: (theme) => theme.palette.primary.dark }
            }}
          >
            {loading ? <CircularProgress size={24} color="inherit" /> : 'Sign In'}
          </Button>
          {error && (
            <Alert severity="error" sx={{ width: '100%', mt: 1, borderRadius: '12px 0 12px 12px'}}>{error}</Alert>
          )}
        </Box>
      </KasavuCard>
    </Container>
  );
};

export default LoginPage;