import React, { useState, useEffect, useContext } from 'react';
import { 
  Typography, 
  Box, 
  Paper, 
  List, 
  ListItem, 
  ListItemText, 
  ListItemIcon, 
  CircularProgress, 
  Alert,
  Divider,
  Chip,
  Button,
  IconButton,
  Menu,
  MenuItem
} from '@mui/material';
// Settings icon as component
const SettingsIcon = () => <span>⚙️</span>;
const RepositoryIcon = () => <span>💻</span>;
import { Link, useNavigate } from 'react-router-dom';
import { AuthContext } from '../App';
import { listProjects, getJiraCredentialStatus, trackProjectAccess, getProjectRepositoryCount } from '../services/api';
import ProjectConfigDialog from '../components/ProjectConfigDialog';
import RepositoryBrowserDialog from '../components/RepositoryBrowserDialog';

// Mock icon function (replace with actual icons in production)
const ProjectIcon = () => <span>🗂️</span>;

const ProjectsPage = () => {
  const { authState } = useContext(AuthContext);
  const navigate = useNavigate();
  const [projects, setProjects] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [errorReason, setErrorReason] = useState('unknown');
  const [jiraStatus, setJiraStatus] = useState(null);
  const [hoveredProject, setHoveredProject] = useState(null);
  const [configDialogOpen, setConfigDialogOpen] = useState(false);
  const [repoDialogOpen, setRepoDialogOpen] = useState(false);
  const [dialogLoading, setDialogLoading] = useState(false);
  const [selectedProject, setSelectedProject] = useState(null);
  const [projectRepositories, setProjectRepositories] = useState({});
  const [anchorEl, setAnchorEl] = useState(null);

  // Debug effect to monitor state changes
  useEffect(() => {
    console.log('State updated:', {
      repoDialogOpen,
      selectedProject: selectedProject?.key,
      anchorEl: !!anchorEl
    });
  }, [repoDialogOpen, selectedProject, anchorEl]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);
        setErrorReason('unknown');
        
        // First check JIRA credentials status
        try {
          const jiraCredStatus = await getJiraCredentialStatus(authState.tenantId);
          setJiraStatus(jiraCredStatus);
          
          // If JIRA credentials are not configured, we can stop here
          if (!jiraCredStatus.configured) {
            setErrorReason('no_credentials');
            setLoading(false);
            return;
          }
        } catch (jiraErr) {
          // If we can't even get the status, assume it's a configuration issue
          console.error('Error checking JIRA credentials:', jiraErr);
          setJiraStatus(null);
          setErrorReason('credentials_error');
          setLoading(false);
          return;
        }
        
        // If we got here, JIRA is configured, so try to fetch projects
        try {
          const data = await listProjects(authState.tenantId);
          setProjects(data.projects || []);
          
          // If we didn't get any projects but the API call succeeded, 
          // it means the user doesn't have any projects or no access
          if ((data.projects || []).length === 0) {
            setErrorReason('no_projects_or_access');
          } else {
            // Fetch repository info for each project
            const repoInfo = {};
            const projectList = data.projects || [];
            for (const project of projectList) {
              try {
                const repoData = await getProjectRepositoryCount(authState.tenantId, project.key);
                repoInfo[project.key] = repoData.repositories ? repoData.repositories.length : 0;
              } catch (err) {
                // Silent fail - just don't show repo count for this project
                repoInfo[project.key] = 0;
              }
            }
            setProjectRepositories(repoInfo);
          }
        } catch (err) {
          console.error('Error fetching projects:', err);
          
          // Check error response for specific error codes
          if (err.response?.status === 401 || err.response?.status === 403) {
            setErrorReason('invalid_credentials');
          } else if (err.response?.status === 500 && err.response?.data?.detail?.includes('JIRA API')) {
            setErrorReason('jira_api_error');
          } else {
            setErrorReason('unknown');
            setError(err.response?.data?.detail || 'Failed to load projects. Please check your JIRA connection.');
          }
        }
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [authState.tenantId]);
  
  const handleProjectClick = async (projectKey) => {
    try {
      // Record the project access in the backend
      await trackProjectAccess(authState.tenantId, projectKey);
      // Navigate to project issues page
      navigate(`/projects/${projectKey}`);
    } catch (err) {
      console.error('Error tracking project access:', err);
      // Still navigate even if tracking fails
      navigate(`/projects/${projectKey}`);
    }
  };

  const handleRepoChipClick = (event, project) => {
    event.stopPropagation(); // Prevent navigation to project
    setSelectedProject(project);
    setRepoDialogOpen(true);
  };

  const handleMenuClick = (event, project) => {
    event.stopPropagation(); // Prevent project click navigation
    console.log('Menu clicked for project:', project);
    setSelectedProject(project);
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    // Don't clear selectedProject here - we need it for dialogs
  };

  const handleConfigClick = () => {
    setConfigDialogOpen(true);
    handleMenuClose();
  };

  const handleRepoClick = () => {
    console.log('Opening repository dialog for project:', selectedProject);
    console.log('Current repoDialogOpen state:', repoDialogOpen);
    handleMenuClose();
    // Use setTimeout to ensure state is updated after menu closes
    setTimeout(() => {
      setRepoDialogOpen(true);
      console.log('Set repoDialogOpen to true');
    }, 0);
  };

  const handleConfigClose = () => {
    setConfigDialogOpen(false);
    // Clear selected project after dialog is closed
    setTimeout(() => setSelectedProject(null), 200);
  };

  const handleRepoClose = () => {
    setRepoDialogOpen(false);
    // Reload repository counts when dialog closes
    fetchRepositoryCounts();
    // Clear selected project after dialog is closed
    setTimeout(() => setSelectedProject(null), 200);
  };

  const fetchRepositoryCounts = async () => {
    const repoInfo = {};
    for (const project of projects) {
      try {
        const repoData = await getProjectRepositoryCount(authState.tenantId, project.key);
        repoInfo[project.key] = repoData.repositories ? repoData.repositories.length : 0;
      } catch (err) {
        repoInfo[project.key] = 0;
      }
    }
    setProjectRepositories(repoInfo);
  };

  return (
    <Paper 
      elevation={0} 
      sx={{
        padding: 3,
        height: '100%'
      }}
    >
      <Typography variant="h4" gutterBottom color="text.primary">
        Projects
      </Typography>
      <Typography variant="body1" color="text.secondary" paragraph>
        View all JIRA projects you have access to through your configured JIRA integration.
      </Typography>

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
          <CircularProgress />
        </Box>
      ) : error ? (
        <Alert severity="error" sx={{ mt: 2 }}>
          {error}
        </Alert>
      ) : projects.length === 0 ? (
        <Box sx={{ mt: 4, p: 3, bgcolor: 'rgba(25, 118, 210, 0.04)', borderRadius: '12px 0 12px 12px' }}>
          <Typography variant="h6" color="primary" gutterBottom>No Projects Found</Typography>
          
          {errorReason === 'no_credentials' && (
            <>
              <Typography variant="body1" paragraph>
                <strong>You haven't set up your JIRA integration yet.</strong>
              </Typography>
              <Typography variant="body2" paragraph>
                Configure your JIRA credentials to access your projects.
              </Typography>
              <Button 
                variant="contained" 
                color="primary" 
                component={Link} 
                to="/jira-settings"
                sx={{ borderRadius: '12px 0 12px 12px', mt: 1 }}
              >
                Configure JIRA Settings
              </Button>
            </>
          )}
          
          {errorReason === 'credentials_error' && (
            <>
              <Typography variant="body1" paragraph>
                <strong>There was an issue accessing your JIRA credentials.</strong>
              </Typography>
              <Typography variant="body2" paragraph>
                Please check your JIRA settings and ensure the application has the correct permissions.
              </Typography>
              <Button 
                variant="contained" 
                color="primary" 
                component={Link} 
                to="/jira-settings"
                sx={{ borderRadius: '12px 0 12px 12px', mt: 1 }}
              >
                Check JIRA Settings
              </Button>
            </>
          )}
          
          {errorReason === 'invalid_credentials' && (
            <>
              <Typography variant="body1" paragraph>
                <strong>Your JIRA credentials appear to be invalid or have expired.</strong>
              </Typography>
              <Typography variant="body2" paragraph>
                Please update your JIRA API key in the settings.
              </Typography>
              <Button 
                variant="contained" 
                color="primary" 
                component={Link} 
                to="/jira-settings"
                sx={{ borderRadius: '12px 0 12px 12px', mt: 1 }}
              >
                Update JIRA Credentials
              </Button>
            </>
          )}
          
          {errorReason === 'jira_api_error' && (
            <>
              <Typography variant="body1" paragraph>
                <strong>There was an error communicating with the JIRA API.</strong>
              </Typography>
              <Typography variant="body2" paragraph>
                This could be due to network issues or the JIRA server being unavailable. Please try again later.
              </Typography>
            </>
          )}
          
          {errorReason === 'no_projects_or_access' && (
            <>
              <Typography variant="body1" paragraph>
                <strong>Your account doesn't have access to any projects.</strong>
              </Typography>
              <Typography variant="body2" paragraph>
                We successfully connected to your JIRA instance, but no projects were found.
              </Typography>
              <Typography variant="body2" paragraph>
                This could be because:
              </Typography>
              <ul>
                <li><Typography variant="body2"><strong>Permission issue:</strong> The API token doesn't have permission to access any projects</Typography></li>
                <li><Typography variant="body2"><strong>Empty instance:</strong> There are no projects in your JIRA instance</Typography></li>
                <li><Typography variant="body2"><strong>Project filter:</strong> Projects exist but aren't visible to your account</Typography></li>
              </ul>
              <Typography variant="body2" sx={{ mt: 2 }}>
                Please check your JIRA permissions or create a new project in your JIRA instance.
              </Typography>
              <Button 
                variant="outlined" 
                color="primary" 
                component="a"
                href="https://jiffy-ai.atlassian.net"
                target="_blank"
                rel="noopener noreferrer"
                sx={{ mt: 2, borderRadius: '12px 0 12px 12px' }}
              >
                Open JIRA Instance
              </Button>
            </>
          )}
          
          {errorReason === 'unknown' && (
            <>
              <Typography variant="body1" paragraph>
                <strong>No projects were found in your JIRA instance.</strong>
              </Typography>
              <Typography variant="body2">
                This could be due to:
              </Typography>
              <ul>
                <li><Typography variant="body2">Your JIRA credentials don't have access to any projects</Typography></li>
                <li><Typography variant="body2">You haven't set up your JIRA integration yet</Typography></li>
                <li><Typography variant="body2">There are no projects in your JIRA instance</Typography></li>
              </ul>
            </>
          )}
        </Box>
      ) : (
        <Box sx={{ mt: 2 }}>
          <Paper 
            elevation={1} 
            sx={{ 
              borderRadius: '12px 0 12px 12px',
              overflow: 'hidden' 
            }}
          >
            <List>
              {projects.map((project, index) => (
                <React.Fragment key={project.key}>
                  {index > 0 && <Divider />}
                  <ListItem 
                    button 
                    onClick={() => handleProjectClick(project.key)}
                    onMouseEnter={() => setHoveredProject(project.key)}
                    onMouseLeave={() => setHoveredProject(null)}
                    sx={{ 
                      transition: 'all 0.15s ease-out',
                      position: 'relative',
                      '&:hover': { 
                        backgroundColor: 'rgba(32, 121, 166, 0.08)',
                        transform: 'translateY(-2px)'
                      }
                    }}
                  >
                    <ListItemIcon>
                      <ProjectIcon />
                    </ListItemIcon>
                    <ListItemText 
                      primary={
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <Typography variant="subtitle1" color="text.primary" sx={{ fontWeight: 500 }}>
                            {project.name}
                          </Typography>
                          <Chip 
                            label={project.key} 
                            size="small" 
                            sx={{ ml: 1, backgroundColor: 'rgba(86, 204, 242, 0.2)', color: '#2079A6' }} 
                          />
                          {projectRepositories[project.key] > 0 && (
                            <Chip
                              icon={<span>📁</span>}
                              label={`${projectRepositories[project.key]} Repo${projectRepositories[project.key] > 1 ? 's' : ''}`}
                              size="small"
                              color="success"
                              sx={{ ml: 1, cursor: 'pointer' }}
                              onClick={(e) => handleRepoChipClick(e, project)}
                            />
                          )}
                          {projectRepositories[project.key] === 0 && (
                            <Chip
                              icon={<span>⚠️</span>}
                              label="No Repos"
                              size="small"
                              color="warning"
                              sx={{ ml: 1, cursor: 'pointer' }}
                              onClick={(e) => handleRepoChipClick(e, project)}
                            />
                          )}
                          {project.bugCount && (
                            <Chip 
                              label={`${project.bugCount} bugs`} 
                              size="small" 
                              color="error"
                              sx={{ ml: 1 }} 
                            />
                          )}
                        </Box>
                      }
                      secondary={project.description || 'No description available'}
                    />
                    {hoveredProject === project.key && (
                      <IconButton
                        onClick={(e) => handleMenuClick(e, project)}
                        sx={{
                          position: 'absolute',
                          right: 16,
                          top: '50%',
                          transform: 'translateY(-50%)',
                          bgcolor: 'rgba(255, 255, 255, 0.9)',
                          '&:hover': {
                            bgcolor: 'rgba(255, 255, 255, 1)',
                          }
                        }}
                      >
                        <SettingsIcon />
                      </IconButton>
                    )}
                  </ListItem>
                </React.Fragment>
              ))}
            </List>
          </Paper>
        </Box>
      )}

      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={handleRepoClick}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <RepositoryIcon />
            <Typography sx={{ ml: 1 }}>Manage Repositories</Typography>
          </Box>
        </MenuItem>
        <MenuItem onClick={handleConfigClick}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <SettingsIcon />
            <Typography sx={{ ml: 1 }}>Set Local Path</Typography>
          </Box>
        </MenuItem>
      </Menu>

      {selectedProject && (
        <ProjectConfigDialog
          open={configDialogOpen}
          onClose={handleConfigClose}
          projectKey={selectedProject.key}
          projectName={selectedProject.name}
          tenantId={authState.tenantId}
        />
      )}

      {selectedProject && (
        <RepositoryBrowserDialog
          open={repoDialogOpen}
          onClose={handleRepoClose}
          projectKey={selectedProject.key}
          tenantId={authState.tenantId}
          onRepositoriesLinked={handleRepoClose}
        />
      )}
    </Paper>
  );
};

export default ProjectsPage;