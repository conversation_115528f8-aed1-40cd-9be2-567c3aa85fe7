import React, { useState, useEffect, useContext } from 'react';
import { 
  Typography, 
  Box, 
  Paper, 
  List, 
  ListItem, 
  ListItemText, 
  ListItemIcon, 
  CircularProgress, 
  Alert,
  Divider,
  Chip,
  Button,
  IconButton,
  Menu,
  MenuItem
} from '@mui/material';
import { Link, useNavigate } from 'react-router-dom';
import { AuthContext } from '../App';
import { 
  listProjects, 
  getJiraCredentialStatus, 
  trackProjectAccess,
  getProjectRepositories
} from '../services/api';
import ProjectRepositoryDialog from '../components/ProjectRepositoryDialog';
import RepositoryPathDialog from '../components/RepositoryPathDialog';

// Icons
const ProjectIcon = () => <span>🗂️</span>;
const SettingsIcon = () => <span>⚙️</span>;
const RepoIcon = () => <span>📁</span>;

const ProjectsPageV2 = () => {
  const { authState } = useContext(AuthContext);
  const navigate = useNavigate();
  const [projects, setProjects] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [errorReason, setErrorReason] = useState('unknown');
  const [jiraStatus, setJiraStatus] = useState(null);
  const [hoveredProject, setHoveredProject] = useState(null);
  const [selectedProject, setSelectedProject] = useState(null);
  const [repositoryDialogOpen, setRepositoryDialogOpen] = useState(false);
  const [pathDialogOpen, setPathDialogOpen] = useState(false);
  const [selectedRepository, setSelectedRepository] = useState(null);
  const [projectRepositories, setProjectRepositories] = useState({});
  const [menuAnchor, setMenuAnchor] = useState(null);
  const [menuProject, setMenuProject] = useState(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);
        setErrorReason('unknown');
        
        // First check JIRA credentials status
        try {
          const jiraCredStatus = await getJiraCredentialStatus(authState.tenantId);
          setJiraStatus(jiraCredStatus);
          
          if (!jiraCredStatus.configured) {
            setErrorReason('no_credentials');
            setLoading(false);
            return;
          }
        } catch (jiraErr) {
          console.error('Error checking JIRA credentials:', jiraErr);
          setJiraStatus(null);
          setErrorReason('credentials_error');
          setLoading(false);
          return;
        }
        
        // Fetch projects
        try {
          const data = await listProjects(authState.tenantId);
          setProjects(data.projects || []);
          
          if ((data.projects || []).length === 0) {
            setErrorReason('no_projects_or_access');
          } else {
            // Fetch repositories for each project
            const repoPromises = data.projects.map(project => 
              getProjectRepositories(authState.tenantId, project.key)
                .then(repoData => ({ projectKey: project.key, repositories: repoData.repositories }))
                .catch(() => ({ projectKey: project.key, repositories: [] }))
            );
            
            const repoResults = await Promise.all(repoPromises);
            const repoMap = {};
            repoResults.forEach(result => {
              repoMap[result.projectKey] = result.repositories;
            });
            setProjectRepositories(repoMap);
          }
        } catch (err) {
          console.error('Error fetching projects:', err);
          
          if (err.response?.status === 401 || err.response?.status === 403) {
            setErrorReason('invalid_credentials');
          } else if (err.response?.status === 500 && err.response?.data?.detail?.includes('JIRA API')) {
            setErrorReason('jira_api_error');
          } else {
            setErrorReason('unknown');
            setError(err.response?.data?.detail || 'Failed to load projects. Please check your JIRA connection.');
          }
        }
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [authState.tenantId]);
  
  const handleProjectClick = async (projectKey) => {
    try {
      await trackProjectAccess(authState.tenantId, projectKey);
      navigate(`/projects/${projectKey}`);
    } catch (err) {
      console.error('Error tracking project access:', err);
      navigate(`/projects/${projectKey}`);
    }
  };

  const handleMenuOpen = (event, project) => {
    event.stopPropagation();
    setMenuAnchor(event.currentTarget);
    setMenuProject(project);
  };

  const handleMenuClose = () => {
    setMenuAnchor(null);
    setMenuProject(null);
  };

  const handleManageRepositories = () => {
    setSelectedProject(menuProject);
    setRepositoryDialogOpen(true);
    handleMenuClose();
  };

  const handleSetPath = (repository) => {
    setSelectedRepository(repository);
    setPathDialogOpen(true);
  };

  return (
    <Paper 
      elevation={0} 
      sx={{
        padding: 3,
        height: '100%'
      }}
    >
      <Typography variant="h4" gutterBottom color="text.primary">
        Projects
      </Typography>
      <Typography variant="body1" color="text.secondary" paragraph>
        View all JIRA projects you have access to through your configured JIRA integration.
      </Typography>

      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
          <CircularProgress />
        </Box>
      ) : error ? (
        <Alert severity="error" sx={{ mt: 2 }}>
          {error}
        </Alert>
      ) : projects.length === 0 ? (
        <Box sx={{ mt: 4, p: 3, bgcolor: 'rgba(25, 118, 210, 0.04)', borderRadius: '12px 0 12px 12px' }}>
          <Typography variant="h6" color="primary" gutterBottom>No Projects Found</Typography>
          {/* Error messages here - same as original */}
        </Box>
      ) : (
        <Box sx={{ mt: 2 }}>
          <Paper 
            elevation={1} 
            sx={{ 
              borderRadius: '12px 0 12px 12px',
              overflow: 'hidden' 
            }}
          >
            <List>
              {projects.map((project, index) => (
                <React.Fragment key={project.key}>
                  {index > 0 && <Divider />}
                  <ListItem 
                    button 
                    onClick={() => handleProjectClick(project.key)}
                    onMouseEnter={() => setHoveredProject(project.key)}
                    onMouseLeave={() => setHoveredProject(null)}
                    sx={{ 
                      transition: 'all 0.15s ease-out',
                      position: 'relative',
                      '&:hover': { 
                        backgroundColor: 'rgba(32, 121, 166, 0.08)',
                        transform: 'translateY(-2px)'
                      }
                    }}
                  >
                    <ListItemIcon>
                      <ProjectIcon />
                    </ListItemIcon>
                    <ListItemText 
                      primary={
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <Typography variant="subtitle1" color="text.primary" sx={{ fontWeight: 500 }}>
                            {project.name}
                          </Typography>
                          <Chip 
                            label={project.key} 
                            size="small" 
                            sx={{ ml: 1, backgroundColor: 'rgba(86, 204, 242, 0.2)', color: '#2079A6' }} 
                          />
                          {project.bugCount && (
                            <Chip 
                              label={`${project.bugCount} bugs`} 
                              size="small" 
                              color="error"
                              sx={{ ml: 1 }} 
                            />
                          )}
                          {projectRepositories[project.key]?.length > 0 && (
                            <Chip
                              icon={<RepoIcon />}
                              label={`${projectRepositories[project.key].length} repos`}
                              size="small"
                              sx={{ ml: 1, backgroundColor: 'rgba(76, 175, 80, 0.2)' }}
                            />
                          )}
                        </Box>
                      }
                      secondary={
                        <Box>
                          <Typography variant="body2" color="text.secondary">
                            {project.description || 'No description available'}
                          </Typography>
                          {projectRepositories[project.key]?.length > 0 && (
                            <Box sx={{ mt: 1, display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                              {projectRepositories[project.key].map(repo => (
                                <Chip
                                  key={repo.id}
                                  label={repo.display_name}
                                  size="small"
                                  variant="outlined"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleSetPath(repo);
                                  }}
                                  sx={{ 
                                    cursor: 'pointer',
                                    '&:hover': { 
                                      backgroundColor: 'rgba(0, 0, 0, 0.04)'
                                    }
                                  }}
                                />
                              ))}
                            </Box>
                          )}
                        </Box>
                      }
                    />
                    {hoveredProject === project.key && (
                      <IconButton
                        onClick={(e) => handleMenuOpen(e, project)}
                        sx={{
                          position: 'absolute',
                          right: 16,
                          top: '50%',
                          transform: 'translateY(-50%)',
                          bgcolor: 'rgba(255, 255, 255, 0.9)',
                          '&:hover': {
                            bgcolor: 'rgba(255, 255, 255, 1)',
                          }
                        }}
                      >
                        <SettingsIcon />
                      </IconButton>
                    )}
                  </ListItem>
                </React.Fragment>
              ))}
            </List>
          </Paper>
        </Box>
      )}

      <Menu
        anchorEl={menuAnchor}
        open={Boolean(menuAnchor)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={handleManageRepositories}>
          Manage Repositories
        </MenuItem>
      </Menu>

      {selectedProject && (
        <ProjectRepositoryDialog
          open={repositoryDialogOpen}
          onClose={() => {
            setRepositoryDialogOpen(false);
            setSelectedProject(null);
          }}
          projectKey={selectedProject.key}
          projectName={selectedProject.name}
          tenantId={authState.tenantId}
        />
      )}

      {selectedRepository && (
        <RepositoryPathDialog
          open={pathDialogOpen}
          onClose={() => {
            setPathDialogOpen(false);
            setSelectedRepository(null);
          }}
          repository={selectedRepository}
          tenantId={authState.tenantId}
        />
      )}
    </Paper>
  );
};

export default ProjectsPageV2;