import React, { useState, useEffect, useContext } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Typography,
  Box,
  Paper,
  Grid,
  Card,
  CardContent,
  CardActionArea,
  CardActions,
  Button,
  Chip,
  TextField,
  InputAdornment,
  IconButton,
  Menu,
  MenuItem,
  CircularProgress,
  Alert,
  Tooltip
} from '@mui/material';
import { AuthContext } from '../App';
import { listProjects, listDiscoveredRepositories, getProjectRepositories } from '../services/api';
import RepositoryBrowserDialog from '../components/RepositoryBrowserDialog';
import UserRepositoryPathDialog from '../components/UserRepositoryPathDialog';

// Icons
const SearchIcon = () => <span>🔍</span>;
const ProjectIcon = () => <span>📁</span>;
const MoreVertIcon = () => <span>⋮</span>;
const RepositoryIcon = () => <span>🗂️</span>;
const PathIcon = () => <span>📍</span>;
const LinkIcon = () => <span>🔗</span>;

const ProjectsPageV3 = () => {
  const { authState } = useContext(AuthContext);
  const navigate = useNavigate();
  
  const [projects, setProjects] = useState([]);
  const [repositories, setRepositories] = useState([]);
  const [projectRepos, setProjectRepos] = useState({});
  const [linkedRepos, setLinkedRepos] = useState({});
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [error, setError] = useState(null);
  
  // Menu state
  const [anchorEl, setAnchorEl] = useState(null);
  const [selectedProject, setSelectedProject] = useState(null);
  
  // Dialog state
  const [browserDialogOpen, setBrowserDialogOpen] = useState(false);
  const [pathDialogOpen, setPathDialogOpen] = useState(false);
  const [selectedRepository, setSelectedRepository] = useState(null);

  useEffect(() => {
    loadData();
  }, [authState.tenantId]);

  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Load projects and repositories in parallel
      const [projectsResponse, reposResponse] = await Promise.all([
        listProjects(authState.tenantId),
        listDiscoveredRepositories(authState.tenantId)
      ]);
      
      const projectList = projectsResponse.projects || [];
      const repoList = reposResponse.repositories || [];
      
      console.log('Available repositories:', repoList);
      
      setProjects(projectList);
      setRepositories(repoList);
      
      // Group repositories by project - fetch linked repos for each project
      const reposByProject = {};
      const linkedByProject = {};
      
      // Fetch linked repositories for each project
      const linkedRepoPromises = projectList.map(project => 
        getProjectRepositories(authState.tenantId, project.key)
          .then(response => {
            console.log(`Repositories for ${project.key}:`, JSON.stringify(response, null, 2));
            return {
              projectKey: project.key,
              repositories: response.repositories || []
            };
          })
          .catch(err => {
            console.error(`Error fetching repos for ${project.key}:`, err.response?.data || err.message);
            return {
              projectKey: project.key,
              repositories: []
            };
          })
      );
      
      const linkedResults = await Promise.all(linkedRepoPromises);
      
      // Process the results
      for (const project of projectList) {
        reposByProject[project.key] = [];
        linkedByProject[project.key] = [];
      }
      
      for (const result of linkedResults) {
        linkedByProject[result.projectKey] = result.repositories;
      }
      
      console.log('Linked repos by project:', JSON.stringify(linkedByProject, null, 2));
      // Log projects with linked repos
      const projectsWithRepos = Object.entries(linkedByProject)
        .filter(([key, repos]) => repos.length > 0)
        .map(([key, repos]) => `${key}: ${repos.length} repos`);
      console.log('Projects with linked repos:', projectsWithRepos);
      
      setProjectRepos(reposByProject);
      setLinkedRepos(linkedByProject);
    } catch (err) {
      setError('Failed to load projects');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handleProjectClick = (projectKey) => {
    navigate(`/projects/${projectKey}`);
  };

  const handleMenuClick = (event, project) => {
    event.stopPropagation();
    setAnchorEl(event.currentTarget);
    setSelectedProject(project);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    // Only clear selectedProject if the dialog is not open
    if (!browserDialogOpen) {
      setSelectedProject(null);
    }
  };

  const handleManageRepositories = () => {
    setBrowserDialogOpen(true);
    setAnchorEl(null);  // Close menu without clearing selectedProject
  };

  const handleSetPath = (repository) => {
    setSelectedRepository(repository);
    setPathDialogOpen(true);
  };

  const handleRepositoriesLinked = (projectKey) => {
    // Refresh the page to show new links
    console.log(`Repositories linked for project ${projectKey}. Refreshing data...`);
    loadData();
  };

  const filteredProjects = projects
    .filter(project =>
      project.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      project.key.toLowerCase().includes(searchQuery.toLowerCase())
    )
    .sort((a, b) => {
      // Sort by linked repos count (descending)
      const aRepos = linkedRepos[a.key]?.length || 0;
      const bRepos = linkedRepos[b.key]?.length || 0;
      
      if (aRepos !== bRepos) {
        return bRepos - aRepos; // Projects with more linked repos come first
      }
      
      // If both have the same number of repos, sort alphabetically by name
      return a.name.localeCompare(b.name);
    });

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Paper elevation={0} sx={{ padding: 3, height: '100%' }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" color="text.primary">
          Projects
        </Typography>
        <TextField
          placeholder="Search projects..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          sx={{ width: 300 }}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
        />
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      <Grid container spacing={3}>
        {filteredProjects.map((project) => {
          const hasLinkedRepos = linkedRepos[project.key]?.length > 0;
          console.log(`Project ${project.key} linked repos:`, linkedRepos[project.key]);
          return (
            <Grid item xs={12} sm={6} md={4} key={project.key}>
              <Card 
                elevation={hasLinkedRepos ? 3 : 1} 
                sx={{ 
                  height: '100%', 
                  display: 'flex', 
                  flexDirection: 'column',
                  background: hasLinkedRepos ? 'linear-gradient(135deg, #f5f5f5 0%, #e8f5e9 100%)' : '#fff',
                  border: hasLinkedRepos ? '2px solid #81c784' : '1px solid #e0e0e0',
                  transition: 'all 0.3s ease'
                }}
              >
                <CardActionArea
                  onClick={() => handleProjectClick(project.key)}
                  sx={{ flexGrow: 1, alignItems: 'flex-start' }}
                >
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                      <ProjectIcon />
                      <Typography variant="h6" sx={{ ml: 1 }}>
                        {project.name}
                      </Typography>
                      {hasLinkedRepos && (
                        <Chip 
                          label="Linked"
                          size="small"
                          color="success"
                          sx={{ ml: 'auto' }}
                        />
                      )}
                    </Box>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      Key: {project.key}
                    </Typography>
                    {linkedRepos[project.key]?.length > 0 && (
                      <Box>
                        <Typography variant="body2" sx={{ mb: 1, fontWeight: 'bold' }}>
                          Linked Repositories:
                        </Typography>
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                          {linkedRepos[project.key].map(repo => (
                            <Chip
                              key={repo.id}
                              label={repo.display_name || repo.name}
                              size="small"
                              icon={<RepositoryIcon />}
                              color="primary"
                              variant="outlined"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleSetPath(repo);
                              }}
                            />
                          ))}
                        </Box>
                      </Box>
                    )}
                  </CardContent>
                </CardActionArea>
                <CardActions sx={{ justifyContent: 'space-between' }}>
                  <Button size="small" onClick={() => handleProjectClick(project.key)}>
                    View Issues
                  </Button>
                  <IconButton
                    size="small"
                    onClick={(e) => handleMenuClick(e, project)}
                  >
                    <MoreVertIcon />
                  </IconButton>
                </CardActions>
              </Card>
            </Grid>
          );
        })}
      </Grid>

      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={handleManageRepositories}>
          <LinkIcon /> <Box sx={{ ml: 1 }}>Manage Repositories</Box>
        </MenuItem>
      </Menu>

      {selectedProject && (
        <RepositoryBrowserDialog
          open={browserDialogOpen}
          onClose={() => {
            setBrowserDialogOpen(false);
            setSelectedProject(null);
          }}
          projectKey={selectedProject.key}
          onRepositoriesLinked={() => handleRepositoriesLinked(selectedProject.key)}
        />
      )}

      {selectedRepository && selectedProject && (
        <UserRepositoryPathDialog
          open={pathDialogOpen}
          onClose={() => setPathDialogOpen(false)}
          repository={selectedRepository}
          projectKey={selectedProject.key}
        />
      )}
    </Paper>
  );
};

export default ProjectsPageV3;