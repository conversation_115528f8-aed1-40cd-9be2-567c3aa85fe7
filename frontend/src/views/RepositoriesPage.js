import React, { useState, useEffect, useContext } from 'react';
import {
  Typography,
  Box,
  Paper,
  Card,
  CardContent,
  CardActions,
  Grid,
  Button,
  Chip,
  TextField,
  InputAdornment,
  CircularProgress,
  Alert,
  Tooltip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material';
import { AuthContext } from '../App';
import { listDiscoveredRepositories, getAllDirectRepositoryPaths, setDirectRepositoryPath, listRepositorySources, syncRepositorySource } from '../services/api';
import UserRepositoryPathDialog from '../components/UserRepositoryPathDialog';
import RepositoryAgentConfigDialog from '../components/RepositoryAgentConfigDialog';

// Icons
const SearchIcon = () => <span>🔍</span>;
const RepositoryIcon = () => <span>📁</span>;
const PathIcon = () => <span>📍</span>;
const EditIcon = () => <span>✏️</span>;
const ClockIcon = () => <span>🕐</span>;
const SourceIcon = () => <span>🔗</span>;
const AgentIcon = () => <span>🤖</span>;

const RepositoriesPage = () => {
  const { authState } = useContext(AuthContext);
  const [repositories, setRepositories] = useState([]);
  const [sources, setSources] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [userPaths, setUserPaths] = useState({});
  const [selectedRepository, setSelectedRepository] = useState(null);
  const [pathDialogOpen, setPathDialogOpen] = useState(false);
  const [agentConfigDialogOpen, setAgentConfigDialogOpen] = useState(false);
  const [selectedRepositoryForAgent, setSelectedRepositoryForAgent] = useState(null);
  const [selectedSource, setSelectedSource] = useState(null);
  const [syncing, setSyncing] = useState({});

  useEffect(() => {
    loadRepositories();
  }, [authState.tenantId]);

  const loadRepositories = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Load sources, repositories, and paths in parallel
      const [sourcesResponse, reposResponse, pathsResponse] = await Promise.all([
        listRepositorySources(authState.tenantId),
        listDiscoveredRepositories(authState.tenantId),
        getAllDirectRepositoryPaths(authState.tenantId)
      ]);
      
      console.log('Sources response:', sourcesResponse);
      console.log('Repositories response:', reposResponse);
      console.log('Paths response:', pathsResponse);
      
      const sourcesList = sourcesResponse.sources || [];
      const reposList = reposResponse.repositories || [];
      const pathsData = pathsResponse.paths || {};
      
      setSources(sourcesList);
      setRepositories(reposList);
      
      // Set user paths from the API response
      const pathsMap = {};
      Object.keys(pathsData).forEach(repoId => {
        pathsMap[repoId] = pathsData[repoId].local_path;
      });
      setUserPaths(pathsMap);
      
    } catch (err) {
      setError('Failed to load repositories');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handleSetPath = (repository) => {
    setSelectedRepository(repository);
    setPathDialogOpen(true);
  };

  const handlePathSaved = () => {
    // Refresh to show the updated path
    loadRepositories();
  };

  const handleConfigureAgent = (repository) => {
    setSelectedRepositoryForAgent(repository);
    setAgentConfigDialogOpen(true);
  };

  const handleAgentConfigSaved = () => {
    // Refresh to show the updated configuration
    loadRepositories();
  };

  const handleSyncSource = async (sourceId) => {
    try {
      setSyncing(prev => ({ ...prev, [sourceId]: true }));
      // Force sync to get fresh data
      await syncRepositorySource(authState.tenantId, sourceId, true);
      // Reload after sync
      await loadRepositories();
    } catch (error) {
      console.error('Error syncing source:', error);
      setError('Failed to sync repository source');
    } finally {
      setSyncing(prev => ({ ...prev, [sourceId]: false }));
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'Unknown';
    const date = new Date(dateString);
    const now = new Date();
    const diffInMs = now - date;
    const diffInHours = diffInMs / (1000 * 60 * 60);
    
    if (diffInHours < 1) {
      const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
      return `${diffInMinutes} minute${diffInMinutes !== 1 ? 's' : ''} ago`;
    } else if (diffInHours < 24) {
      const hours = Math.floor(diffInHours);
      return `${hours} hour${hours !== 1 ? 's' : ''} ago`;
    } else if (diffInHours < 168) { // 7 days
      const days = Math.floor(diffInHours / 24);
      return `${days} day${days !== 1 ? 's' : ''} ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  const filteredRepositories = repositories.filter(repo =>
    repo.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    repo.full_name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    repo.description?.toLowerCase().includes(searchQuery.toLowerCase())
  );

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Paper elevation={0} sx={{ padding: 3, height: '100%' }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" color="text.primary">
          Repositories
        </Typography>
        <TextField
          placeholder="Search repositories..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          sx={{ width: 300 }}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
        />
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Repository Sources Section */}
      {sources.length > 0 && (
        <Box sx={{ mb: 4 }}>
          <Typography variant="h5" sx={{ mb: 2 }}>
            Repository Sources
          </Typography>
          <Grid container spacing={3}>
            {sources.map((source) => {
              const sourceRepos = repositories.filter(repo => repo.source_id === source.id);
              return (
                <Grid item xs={12} sm={6} md={4} key={source.id}>
                  <Card 
                    elevation={2}
                    sx={{ 
                      bgcolor: 'primary.light',
                      border: '2px solid',
                      borderColor: 'primary.main',
                      position: 'relative'
                    }}
                  >
                    <CardContent>
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                        <SourceIcon />
                        <Typography variant="h6" sx={{ ml: 1 }}>
                          {source.name}
                        </Typography>
                      </Box>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                        Type: {source.source_type}
                      </Typography>
                      <Typography variant="body2" sx={{ mb: 2 }}>
                        {sourceRepos.length} repositories discovered
                      </Typography>
                      {source.last_sync_at && (
                        <Typography variant="caption" color="text.secondary">
                          Last synced: {formatDate(source.last_sync_at)}
                        </Typography>
                      )}
                    </CardContent>
                    <CardActions>
                      <Button
                        size="small"
                        variant="contained"
                        onClick={() => handleSyncSource(source.id)}
                        disabled={syncing[source.id]}
                      >
                        {syncing[source.id] ? 'Syncing...' : 'Sync Now'}
                      </Button>
                      <Button
                        size="small"
                        onClick={() => setSelectedSource(source)}
                      >
                        View Repos
                      </Button>
                    </CardActions>
                  </Card>
                </Grid>
              );
            })}
          </Grid>
        </Box>
      )}

      {/* All Repositories Section */}
      <Typography variant="h5" sx={{ mb: 2 }}>
        All Repositories
      </Typography>
      <Grid container spacing={3}>
        {filteredRepositories.map((repository) => (
          <Grid item xs={12} sm={6} md={4} key={repository.id}>
            <Card 
              elevation={1} 
              sx={{ 
                height: '100%', 
                display: 'flex', 
                flexDirection: 'column',
                position: 'relative',
                '&:hover': {
                  boxShadow: 3
                }
              }}
            >
              <CardContent sx={{ flexGrow: 1 }}>
                <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 2 }}>
                  <RepositoryIcon />
                  <Box sx={{ ml: 1, flexGrow: 1 }}>
                    <Typography variant="h6" gutterBottom>
                      {repository.full_name || repository.name}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {repository.description || 'No description'}
                    </Typography>
                  </Box>
                </Box>
                
                <Box sx={{ mb: 2 }}>
                  <Chip
                    icon={<SourceIcon />}
                    label={repository.source_name || 'Unknown Source'}
                    size="small"
                    sx={{ mr: 1 }}
                  />
                  {repository.default_branch && (
                    <Chip
                      label={repository.default_branch}
                      size="small"
                      variant="outlined"
                      sx={{ mr: 1 }}
                    />
                  )}
                  {(repository.agent_instruction || repository.agent_description) && (
                    <Chip
                      icon={<AgentIcon />}
                      label="AI Configured"
                      size="small"
                      color="primary"
                      variant="outlined"
                    />
                  )}
                </Box>
                
                {userPaths[repository.id] ? (
                  <Box sx={{ mt: 2, p: 1.5, bgcolor: 'success.light', borderRadius: 1 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <PathIcon />
                        <Typography variant="body2" sx={{ ml: 1, fontWeight: 'medium' }}>
                          Local Path:
                        </Typography>
                      </Box>
                      <IconButton 
                        size="small"
                        onClick={() => handleSetPath(repository)}
                        sx={{ p: 0.5 }}
                      >
                        <EditIcon />
                      </IconButton>
                    </Box>
                    <Typography 
                      variant="body2" 
                      sx={{ 
                        mt: 0.5,
                        fontFamily: 'monospace',
                        fontSize: '0.85rem',
                        wordBreak: 'break-all'
                      }}
                    >
                      {userPaths[repository.id]}
                    </Typography>
                  </Box>
                ) : (
                  <Box sx={{ mt: 2, p: 1.5, bgcolor: 'grey.100', borderRadius: 1 }}>
                    <Typography variant="body2" color="text.secondary" align="center">
                      No local path set
                    </Typography>
                  </Box>
                )}
                
                <Box sx={{ mt: 2, display: 'flex', alignItems: 'center' }}>
                  <ClockIcon />
                  <Typography variant="caption" color="text.secondary" sx={{ ml: 0.5 }}>
                    Updated {formatDate(repository.last_updated || repository.discovered_at)}
                  </Typography>
                </Box>
              </CardContent>
              
              <CardActions>
                <Button
                  size="small"
                  onClick={() => handleSetPath(repository)}
                  startIcon={<PathIcon />}
                >
                  {userPaths[repository.id] ? 'Change Path' : 'Set Path'}
                </Button>
                <Button
                  size="small"
                  onClick={() => handleConfigureAgent(repository)}
                  startIcon={<AgentIcon />}
                  color="primary"
                >
                  Configure AI
                </Button>
                {repository.clone_url && (
                  <Tooltip title="Copy clone URL">
                    <Button
                      size="small"
                      onClick={() => navigator.clipboard.writeText(repository.clone_url)}
                    >
                      Copy URL
                    </Button>
                  </Tooltip>
                )}
              </CardActions>
            </Card>
          </Grid>
        ))}
      </Grid>
      
      {filteredRepositories.length === 0 && (
        <Box sx={{ textAlign: 'center', py: 8 }}>
          <Typography variant="h6" color="text.secondary">
            {searchQuery ? 'No repositories match your search' : 'No repositories found'}
          </Typography>
        </Box>
      )}
      
      {selectedRepository && (
        <UserRepositoryPathDialog
          open={pathDialogOpen}
          onClose={() => {
            setPathDialogOpen(false);
            setSelectedRepository(null);
          }}
          repository={selectedRepository}
          projectKey={null}
          onPathSaved={handlePathSaved}
        />
      )}
      
      {selectedRepositoryForAgent && (
        <RepositoryAgentConfigDialog
          open={agentConfigDialogOpen}
          onClose={() => {
            setAgentConfigDialogOpen(false);
            setSelectedRepositoryForAgent(null);
          }}
          repository={selectedRepositoryForAgent}
          onConfigSaved={handleAgentConfigSaved}
        />
      )}
    </Paper>
  );
};

export default RepositoriesPage;