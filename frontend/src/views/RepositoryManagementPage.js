import React, { useState, useEffect, useContext } from 'react';
import {
  Typo<PERSON>,
  Box,
  Paper,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Chip,
  Alert,
  CircularProgress
} from '@mui/material';
import { AuthContext } from '../App';
import { 
  listRepositories, 
  createRepository, 
  updateRepository, 
  deleteRepository,
  listProjects
} from '../services/api';

// Icons
const EditIcon = () => <span>✏️</span>;
const DeleteIcon = () => <span>🗑️</span>;
const AddIcon = () => <span>➕</span>;

const RepositoryManagementPage = () => {
  const { authState } = useContext(AuthContext);
  const [repositories, setRepositories] = useState([]);
  const [projects, setProjects] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [repoToDelete, setRepoToDelete] = useState(null);
  const [deleteType, setDeleteType] = useState('soft'); // 'soft' or 'hard'
  const [editingRepo, setEditingRepo] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    display_name: '',
    repository_type: 'github',
    remote_url: '',
    default_branch: 'main',
    description: ''
  });

  useEffect(() => {
    fetchData();
  }, [authState.tenantId]);

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Fetch repositories and projects in parallel
      const [reposData, projectsData] = await Promise.all([
        listRepositories(authState.tenantId),
        listProjects(authState.tenantId)
      ]);
      
      setRepositories(reposData.repositories || []);
      setProjects(projectsData.projects || []);
    } catch (err) {
      console.error('Error fetching data:', err);
      setError('Failed to load repositories');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateRepository = () => {
    setEditingRepo(null);
    setFormData({
      name: '',
      display_name: '',
      repository_type: 'github',
      remote_url: '',
      default_branch: 'main',
      description: ''
    });
    setDialogOpen(true);
  };

  const handleEditRepository = (repo) => {
    setEditingRepo(repo);
    setFormData({
      name: repo.name,
      display_name: repo.display_name,
      repository_type: repo.repository_type,
      remote_url: repo.remote_url,
      default_branch: repo.default_branch,
      description: repo.description || ''
    });
    setDialogOpen(true);
  };

  const handleSaveRepository = async () => {
    try {
      if (editingRepo) {
        await updateRepository(authState.tenantId, editingRepo.id, formData);
      } else {
        await createRepository(authState.tenantId, formData);
      }
      setDialogOpen(false);
      await fetchData();
    } catch (err) {
      console.error('Error saving repository:', err);
      setError('Failed to save repository');
    }
  };

  const handleDeleteRepository = (repo) => {
    setRepoToDelete(repo);
    setDeleteType('soft');
    setDeleteDialogOpen(true);
  };

  const confirmDelete = async () => {
    if (!repoToDelete) return;
    
    try {
      await deleteRepository(authState.tenantId, repoToDelete.id, deleteType === 'hard');
      setDeleteDialogOpen(false);
      setRepoToDelete(null);
      await fetchData();
    } catch (err) {
      console.error('Error deleting repository:', err);
      setError('Failed to delete repository');
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  // Check if user is admin
  const isAdmin = authState.user?.roles?.includes('tenant_admin');
  if (!isAdmin) {
    return (
      <Paper elevation={0} sx={{ padding: 3, height: '100%' }}>
        <Alert severity="error">
          You must be a tenant administrator to access this page.
        </Alert>
      </Paper>
    );
  }

  return (
    <Paper elevation={0} sx={{ padding: 3, height: '100%' }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" color="text.primary">
          Repository Management
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleCreateRepository}
          sx={{ borderRadius: '12px 0 12px 12px' }}
        >
          Add Repository
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      <TableContainer component={Paper} elevation={1} sx={{ borderRadius: '12px 0 12px 12px' }}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Name</TableCell>
              <TableCell>Type</TableCell>
              <TableCell>Remote URL</TableCell>
              <TableCell>Default Branch</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {repositories.map((repo) => (
              <TableRow 
                key={repo.id}
                sx={{ 
                  opacity: repo.is_active ? 1 : 0.6,
                  backgroundColor: repo.is_active ? 'transparent' : 'action.hover'
                }}
              >
                <TableCell>
                  <Box>
                    <Typography variant="subtitle2" color={repo.is_active ? 'text.primary' : 'text.disabled'}>
                      {repo.display_name}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {repo.name}
                    </Typography>
                  </Box>
                </TableCell>
                <TableCell>
                  <Chip 
                    label={repo.repository_type} 
                    size="small"
                    color={repo.repository_type === 'github' ? 'primary' : 'secondary'}
                  />
                </TableCell>
                <TableCell>
                  <Typography variant="body2" sx={{ maxWidth: 300, overflow: 'hidden', textOverflow: 'ellipsis' }}>
                    {repo.remote_url}
                  </Typography>
                </TableCell>
                <TableCell>{repo.default_branch}</TableCell>
                <TableCell>
                  <Chip
                    label={repo.is_active ? 'Active' : 'Inactive'}
                    size="small"
                    color={repo.is_active ? 'success' : 'default'}
                  />
                </TableCell>
                <TableCell>
                  <IconButton onClick={() => handleEditRepository(repo)}>
                    <EditIcon />
                  </IconButton>
                  <IconButton 
                    onClick={() => handleDeleteRepository(repo)}
                    disabled={!repo.is_active}
                  >
                    <DeleteIcon />
                  </IconButton>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      <Dialog
        open={dialogOpen}
        onClose={() => setDialogOpen(false)}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: { borderRadius: '12px 0 12px 12px' }
        }}
      >
        <DialogTitle>
          {editingRepo ? 'Edit Repository' : 'Add Repository'}
        </DialogTitle>
        <DialogContent>
          <TextField
            fullWidth
            label="Repository Name"
            value={formData.name}
            onChange={(e) => setFormData({ ...formData, name: e.target.value })}
            margin="normal"
            required
            helperText="Unique identifier for the repository"
          />
          <TextField
            fullWidth
            label="Display Name"
            value={formData.display_name}
            onChange={(e) => setFormData({ ...formData, display_name: e.target.value })}
            margin="normal"
            helperText="User-friendly name"
          />
          <FormControl fullWidth margin="normal">
            <InputLabel>Repository Type</InputLabel>
            <Select
              value={formData.repository_type}
              onChange={(e) => setFormData({ ...formData, repository_type: e.target.value })}
              label="Repository Type"
            >
              <MenuItem value="github">GitHub</MenuItem>
              <MenuItem value="bitbucket">Bitbucket</MenuItem>
            </Select>
          </FormControl>
          <TextField
            fullWidth
            label="Remote URL"
            value={formData.remote_url}
            onChange={(e) => setFormData({ ...formData, remote_url: e.target.value })}
            margin="normal"
            required
            helperText="URL of the remote repository"
          />
          <TextField
            fullWidth
            label="Default Branch"
            value={formData.default_branch}
            onChange={(e) => setFormData({ ...formData, default_branch: e.target.value })}
            margin="normal"
            helperText="Default branch name"
          />
          <TextField
            fullWidth
            label="Description"
            value={formData.description}
            onChange={(e) => setFormData({ ...formData, description: e.target.value })}
            margin="normal"
            multiline
            rows={3}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDialogOpen(false)}>Cancel</Button>
          <Button 
            onClick={handleSaveRepository} 
            variant="contained"
            sx={{ borderRadius: '12px 0 12px 12px' }}
          >
            {editingRepo ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>

      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: { borderRadius: '12px 0 12px 12px' }
        }}
      >
        <DialogTitle>
          Delete Repository
        </DialogTitle>
        <DialogContent>
          <Typography variant="body1" paragraph>
            Are you sure you want to delete the repository "{repoToDelete?.display_name}"?
          </Typography>
          
          <FormControl fullWidth sx={{ mt: 2 }}>
            <InputLabel>Delete Type</InputLabel>
            <Select
              value={deleteType}
              onChange={(e) => setDeleteType(e.target.value)}
              label="Delete Type"
            >
              <MenuItem value="soft">
                <Box>
                  <Typography variant="body2">Soft Delete (Deactivate)</Typography>
                  <Typography variant="caption" color="text.secondary">
                    Repository will be marked as inactive but can be restored
                  </Typography>
                </Box>
              </MenuItem>
              <MenuItem value="hard">
                <Box>
                  <Typography variant="body2" color="error">Permanent Delete</Typography>
                  <Typography variant="caption" color="text.secondary">
                    Repository and all associated data will be permanently removed
                  </Typography>
                </Box>
              </MenuItem>
            </Select>
          </FormControl>
          
          {deleteType === 'hard' && (
            <Alert severity="warning" sx={{ mt: 2 }}>
              Warning: This action is irreversible. All project links and user paths will be permanently deleted.
            </Alert>
          )}
        </DialogContent>
        <DialogActions>
          <Button 
            onClick={() => setDeleteDialogOpen(false)}
            sx={{ borderRadius: '12px 0 12px 12px' }}
          >
            Cancel
          </Button>
          <Button 
            onClick={confirmDelete}
            variant="contained"
            color={deleteType === 'hard' ? 'error' : 'primary'}
            sx={{ borderRadius: '12px 0 12px 12px' }}
          >
            {deleteType === 'hard' ? 'Permanently Delete' : 'Deactivate'}
          </Button>
        </DialogActions>
      </Dialog>
    </Paper>
  );
};

export default RepositoryManagementPage;