import React, { useState, useEffect, useContext } from 'react';
import {
  Typo<PERSON>,
  Box,
  Paper,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Chip,
  Alert,
  CircularProgress,
  InputAdornment,
  FormHelperText,
  Tooltip,
  Snackbar
} from '@mui/material';
import { AuthContext } from '../App';
import { 
  listRepositorySources,
  createRepositorySource,
  syncRepositorySource,
  deleteRepositorySource
} from '../services/api';
import { formatUTCToLocal, getRelativeTime } from '../utils/dateUtils';

// Icons
const AddIcon = () => <span>➕</span>;
const SyncIcon = () => <span>🔄</span>;
const ViewIcon = () => <span>👁️</span>;
const DeleteIcon = () => <span>🗑️</span>;
const GitHubIcon = () => <span>🐙</span>;
const BitbucketIcon = () => <span>🪣</span>;
const GitLabIcon = () => <span>🦊</span>;

const RepositorySourcesPage = () => {
  const { authState } = useContext(AuthContext);
  const [sources, setSources] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [sourceToDelete, setSourceToDelete] = useState(null);
  const [syncing, setSyncing] = useState({});
  const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'info' });
  const [formData, setFormData] = useState({
    name: '',
    source_type: 'bitbucket',
    auth_config: {
      username: '',
      app_password: '',
      token: ''
    },
    settings: {
      workspace_id: '',
      org_name: '',
      gitlab_url: ''
    }
  });

  useEffect(() => {
    loadSources();
  }, [authState.tenantId]);

  const loadSources = async () => {
    try {
      setLoading(true);
      const response = await listRepositorySources(authState.tenantId);
      setSources(response.sources || []);
    } catch (err) {
      setError('Failed to load repository sources');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateSource = () => {
    setFormData({
      name: '',
      source_type: 'bitbucket',
      auth_config: {
        username: '',
        app_password: '',
        token: ''
      },
      settings: {
        workspace_id: '',
        org_name: '',
        gitlab_url: ''
      }
    });
    setDialogOpen(true);
  };

  const handleSubmit = async () => {
    try {
      setError(null);
      
      // Prepare data based on source type
      const data = {
        name: formData.name,
        source_type: formData.source_type,
        auth_config: {},
        settings: {}
      };
      
      if (formData.source_type === 'bitbucket') {
        data.auth_config = {
          username: formData.auth_config.username,
          app_password: formData.auth_config.app_password
        };
        data.settings = {
          workspace_id: formData.settings.workspace_id
        };
      } else if (formData.source_type === 'github') {
        data.auth_config = {
          token: formData.auth_config.token
        };
        data.settings = {
          org_name: formData.settings.org_name
        };
      } else if (formData.source_type === 'gitlab') {
        data.auth_config = {
          token: formData.auth_config.token
        };
        data.settings = {
          gitlab_url: formData.settings.gitlab_url
        };
      }
      
      await createRepositorySource(authState.tenantId, data);
      setDialogOpen(false);
      loadSources();
    } catch (err) {
      setError(`Failed to create source: ${err.response?.data?.error || err.message}`);
    }
  };

  const handleSyncSource = async (sourceId, force = false) => {
    try {
      setSyncing(prev => ({ ...prev, [sourceId]: true }));
      const result = await syncRepositorySource(authState.tenantId, sourceId, force);
      
      // Show different message based on cache usage
      const message = result.cache_used 
        ? `Loaded ${result.repository_count} repositories from cache`
        : `Synced ${result.repository_count} repositories from source`;
      
      setSnackbar({
        open: true,
        message: message,
        severity: 'success'
      });
      
      loadSources();
    } catch (err) {
      setSnackbar({
        open: true,
        message: `Failed to sync source: ${err.response?.data?.error || err.message}`,
        severity: 'error'
      });
    } finally {
      setSyncing(prev => ({ ...prev, [sourceId]: false }));
    }
  };

  const handleDeleteSource = (source) => {
    setSourceToDelete(source);
    setDeleteDialogOpen(true);
  };

  const confirmDelete = async () => {
    if (!sourceToDelete) return;

    try {
      await deleteRepositorySource(authState.tenantId, sourceToDelete.id);
      setDeleteDialogOpen(false);
      setSourceToDelete(null);
      setSnackbar({
        open: true,
        message: `Repository source "${sourceToDelete.name}" has been deleted`,
        severity: 'success'
      });
      loadSources();
    } catch (err) {
      console.error('Error deleting repository source:', err);
      setError('Failed to delete repository source');
    }
  };

  const getSourceIcon = (sourceType) => {
    switch (sourceType) {
      case 'github':
        return <GitHubIcon />;
      case 'bitbucket':
        return <BitbucketIcon />;
      case 'gitlab':
        return <GitLabIcon />;
      default:
        return null;
    }
  };

  // Check if user is admin
  const isAdmin = authState.user?.roles?.includes('tenant_admin');
  if (!isAdmin) {
    return (
      <Paper elevation={0} sx={{ padding: 3, height: '100%' }}>
        <Alert severity="error">
          You must be a tenant administrator to access this page.
        </Alert>
      </Paper>
    );
  }

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Paper elevation={0} sx={{ padding: 3, height: '100%' }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" color="text.primary">
          Repository Sources
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={handleCreateSource}
          sx={{ borderRadius: '12px 0 12px 12px' }}
        >
          Add Source
        </Button>
      </Box>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      <TableContainer component={Paper} elevation={1} sx={{ borderRadius: '12px 0 12px 12px' }}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Source</TableCell>
              <TableCell>Type</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Repositories</TableCell>
              <TableCell>Last Sync</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {sources.map((source) => (
              <TableRow key={source.id}>
                <TableCell>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    {getSourceIcon(source.source_type)}
                    <Typography variant="subtitle2">{source.name}</Typography>
                  </Box>
                </TableCell>
                <TableCell>
                  <Chip 
                    label={source.source_type} 
                    size="small"
                    color="primary"
                  />
                </TableCell>
                <TableCell>
                  <Chip
                    label={source.is_active ? 'Active' : 'Inactive'}
                    size="small"
                    color={source.is_active ? 'success' : 'default'}
                  />
                </TableCell>
                <TableCell>{source.repository_count}</TableCell>
                <TableCell>
                  <Box>
                    {formatUTCToLocal(source.last_sync_at)}
                    {source.last_sync_at && (
                      <Typography variant="caption" color="text.secondary" display="block">
                        {getRelativeTime(source.last_sync_at)}
                      </Typography>
                    )}
                  </Box>
                </TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <IconButton 
                      onClick={() => handleSyncSource(source.id)}
                      disabled={syncing[source.id]}
                      title="Refresh (uses cache if recent)"
                    >
                      {syncing[source.id] ? <CircularProgress size={20} /> : <SyncIcon />}
                    </IconButton>
                    <IconButton 
                      onClick={() => handleSyncSource(source.id, true)}
                      disabled={syncing[source.id]}
                      title="Force refresh (bypass cache)"
                      color="primary"
                    >
                      {syncing[source.id] ? <CircularProgress size={20} /> : '🔃'}
                    </IconButton>
                    <IconButton 
                      onClick={() => handleDeleteSource(source)}
                      title="Delete source"
                      color="error"
                    >
                      <DeleteIcon />
                    </IconButton>
                  </Box>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      <Dialog 
        open={dialogOpen} 
        onClose={() => setDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Add Repository Source</DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2, display: 'flex', flexDirection: 'column', gap: 2 }}>
            <TextField
              label="Source Name"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              required
              fullWidth
              helperText="A descriptive name for this source"
            />

            <FormControl fullWidth required>
              <InputLabel>Source Type</InputLabel>
              <Select
                value={formData.source_type}
                onChange={(e) => setFormData(prev => ({ ...prev, source_type: e.target.value }))}
                label="Source Type"
              >
                <MenuItem value="bitbucket">
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <BitbucketIcon /> Bitbucket
                  </Box>
                </MenuItem>
                <MenuItem value="github">
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <GitHubIcon /> GitHub
                  </Box>
                </MenuItem>
                <MenuItem value="gitlab">
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <GitLabIcon /> GitLab
                  </Box>
                </MenuItem>
              </Select>
            </FormControl>

            {formData.source_type === 'bitbucket' && (
              <>
                <TextField
                  label="Workspace ID"
                  value={formData.settings.workspace_id}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    settings: { ...prev.settings, workspace_id: e.target.value }
                  }))}
                  required
                  fullWidth
                  helperText="Your Bitbucket workspace ID or slug"
                />
                <TextField
                  label="Username"
                  value={formData.auth_config.username}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    auth_config: { ...prev.auth_config, username: e.target.value }
                  }))}
                  required
                  fullWidth
                  helperText="Your Bitbucket username"
                />
                <TextField
                  label="App Password"
                  type="password"
                  value={formData.auth_config.app_password}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    auth_config: { ...prev.auth_config, app_password: e.target.value }
                  }))}
                  required
                  fullWidth
                  helperText="Create an app password in Bitbucket settings"
                />
              </>
            )}

            {formData.source_type === 'github' && (
              <>
                <TextField
                  label="Organization Name"
                  value={formData.settings.org_name}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    settings: { ...prev.settings, org_name: e.target.value }
                  }))}
                  required
                  fullWidth
                  helperText="Your GitHub organization name"
                />
                <TextField
                  label="Personal Access Token"
                  type="password"
                  value={formData.auth_config.token}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    auth_config: { ...prev.auth_config, token: e.target.value }
                  }))}
                  required
                  fullWidth
                  helperText="Create a token with repo access in GitHub settings"
                />
              </>
            )}

            {formData.source_type === 'gitlab' && (
              <>
                <TextField
                  label="GitLab URL"
                  value={formData.settings.gitlab_url}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    settings: { ...prev.settings, gitlab_url: e.target.value }
                  }))}
                  required
                  fullWidth
                  helperText="Your GitLab instance URL (e.g., https://gitlab.com)"
                />
                <TextField
                  label="Personal Access Token"
                  type="password"
                  value={formData.auth_config.token}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    auth_config: { ...prev.auth_config, token: e.target.value }
                  }))}
                  required
                  fullWidth
                  helperText="Create a token with api scope in GitLab settings"
                />
              </>
            )}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleSubmit} variant="contained">
            Create Source
          </Button>
        </DialogActions>
      </Dialog>
      
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={() => setSnackbar({ ...snackbar, open: false })}
        message={snackbar.message}
      />

      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: { borderRadius: '12px 0 12px 12px' }
        }}
      >
        <DialogTitle>
          Delete Repository Source
        </DialogTitle>
        <DialogContent>
          <Typography variant="body1" paragraph>
            Are you sure you want to delete the repository source "{sourceToDelete?.name}"?
          </Typography>
          <Alert severity="warning" sx={{ mt: 2 }}>
            Warning: This will remove the source configuration and all associated repository discoveries. 
            The repositories themselves will not be deleted, but they will be disconnected from this source.
          </Alert>
        </DialogContent>
        <DialogActions>
          <Button 
            onClick={() => setDeleteDialogOpen(false)}
            sx={{ borderRadius: '12px 0 12px 12px' }}
          >
            Cancel
          </Button>
          <Button 
            onClick={confirmDelete}
            variant="contained"
            color="error"
            sx={{ borderRadius: '12px 0 12px 12px' }}
          >
            Delete Source
          </Button>
        </DialogActions>
      </Dialog>
    </Paper>
  );
};

export default RepositorySourcesPage;