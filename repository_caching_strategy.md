# Repository Caching Strategy

## Overview

Due to the slow performance of Bitbucket API calls (taking over a minute to fetch 50 repositories), the repository source management system implements a caching strategy to improve performance.

## Cache Implementation

### 1. Cache Duration
- Repository metadata is cached for **1 hour** by default
- <PERSON><PERSON> can be bypassed using the `force` parameter

### 2. Cache Storage
Repositories are cached in the database:
- `RepositorySource.last_sync_at` - Timestamp of last sync
- `Repository` table - Stores all discovered repositories
- `Repository.metadata_json` - Cached API response data
- `Repository.last_updated` - When metadata was last updated

### 3. Cache Behavior

#### Initial Load
1. User adds a new repository source
2. First sync is performed (slow, 1-2 minutes for Bitbucket)
3. Repositories are stored in the database
4. `last_sync_at` timestamp is updated

#### Subsequent Loads
1. User views repositories
2. System checks `last_sync_at` timestamp
3. If less than 1 hour old, returns cached data (fast, < 1 second)
4. If older than 1 hour, performs new sync

#### Force Refresh
Users can force a refresh by:
1. Clicking "Sync Now" button (sets `force: true`)
2. System ignores cache and fetches fresh data
3. Updates cache with new data

## API Endpoints

### Get Repositories (with cache)
```
GET /tenants/{tid}/repository-sources/{sid}/repositories
```
Returns cached repositories if available.

### Sync Repositories
```
POST /tenants/{tid}/repository-sources/{sid}/sync
{
  "force": false  // true to bypass cache
}
```

Response includes:
```json
{
  "synced": true,
  "cache_used": true,  // indicates if cache was used
  "last_sync": "2024-01-01T10:00:00Z",
  "repository_count": 385,
  "repositories": [...]
}
```

## Frontend Recommendations

1. **Show Last Sync Time**: Display when repositories were last synced
2. **Loading States**: Show different states for cached vs fresh data
3. **Manual Refresh Button**: Allow users to force refresh
4. **Auto-refresh**: Consider auto-refreshing if cache is very old (> 24 hours)

## Performance Benefits

- Initial sync: 60-120 seconds (Bitbucket API limitation)
- Cached load: < 1 second
- Reduces API rate limiting issues
- Better user experience with instant loads

## Future Enhancements

1. **Configurable cache duration** per source
2. **Background sync** to refresh cache automatically
3. **Partial updates** for changed repositories only
4. **Webhook integration** for real-time updates