# Repository Source Management System Summary

This document summarizes the new repository source management system that has been implemented to handle N:N relationships between JIRA projects and repositories.

## Architecture Overview

The system implements a three-tier architecture:

1. **Repository Sources** - Configuration for Bitbucket workspaces, GitHub organizations, etc.
2. **Repositories** - Individual repositories discovered from sources
3. **Project-Repository Links** - N:N relationships between JIRA projects and repositories
4. **User Repository Paths** - User-specific local paths for linked repositories

## Key Components

### Database Models

1. **RepositorySource**
   - Stores authentication credentials (encrypted)
   - Supports Bitbucket, GitHub, GitLab
   - Contains source-specific settings (workspace ID, org name, etc.)

2. **Repository**
   - Cached repositories discovered from sources
   - Contains clone URLs, descriptions, metadata
   - References the source it was discovered from

3. **ProjectRepository**
   - Junction table linking JIRA projects to repositories
   - Supports marking primary repository for a project

4. **UserRepositoryPath**
   - User-specific local paths for linked repositories
   - References project-repository relationships

### Service Layer

**RepositorySourceService** provides:
- Create/read/update/delete repository sources
- Sync repositories from external sources
- Encryption/decryption of credentials

### API Endpoints

#### Admin Endpoints
- `GET /tenants/{tid}/repository-sources` - List sources
- `POST /tenants/{tid}/repository-sources` - Create source
- `GET /tenants/{tid}/repository-sources/{sid}` - Get source
- `PUT /tenants/{tid}/repository-sources/{sid}` - Update source
- `DELETE /tenants/{tid}/repository-sources/{sid}` - Delete source
- `POST /tenants/{tid}/repository-sources/{sid}/sync` - Sync repositories

#### User Endpoints
- `GET /tenants/{tid}/repositories` - List all discovered repositories
- `GET /tenants/{tid}/projects/{pkey}/available-repositories` - List linkable repositories
- `POST /tenants/{tid}/projects/{pkey}/repositories` - Link repository to project

### Frontend Components

1. **RepositorySourcesPage** - Admin interface for managing sources
2. **RepositoryBrowserDialog** - User interface for linking repositories
3. **ProjectsPageV3** - Enhanced projects page with repository integration

## Security Features

- AES-256-GCM encryption for credentials
- Tenant isolation for all data
- Role-based access control (admin vs user)
- Base64 encoding for encrypted data storage

## Testing

Created comprehensive test script (`test_repository_sources.py`) that covers:
- Source creation with encrypted credentials
- Source listing and retrieval
- Source updates
- Repository sync (mock)
- Source deletion

## Migration from Old System

The system replaces the previous `UserProjectPath` model with the new architecture.
A database reset script (`reset_database.py`) was created for development.

## Next Steps

1. Implement actual repository sync from Bitbucket/GitHub APIs
2. Add frontend UI for user local path configuration
3. Implement Git integration using configured paths
4. Add validation for repository accessibility
5. Implement caching and refresh strategies