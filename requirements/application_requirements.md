# JIRA Bug Browser Web Application – Detailed Design Document

*Version 1.1 · May 14 2025* (Supersedes v1.0)

---

## 1 · Pur<PERSON> & Scope

This document specifies the **end‑to‑end design** of a *multi‑tenant SaaS* web application that lets an authenticated user browse JIRA projects and drill into individual Bug issues.
Key principles:

* **Tenant isolation** – every customer (tenant) supplies *their own* JIRA base‑URL + API key(s); data is siloed logically (row‑level) and optionally physically (schema‑per‑tenant).
* **Incremental authentication** – MVP ships with a local username/password store **per tenant** + personal JIRA API token; later releases provide Google OAuth and SSO via OIDC.
* **Read‑only** integration first (view bugs); future road‑map adds write capabilities, CI/CD hooks and automated remediation.

Out of scope: automatic bug fixing, deployment orchestration, or non‑JIRA trackers.

---

## 2 · Glossary

| Term           | Definition                                                                        |
| -------------- | --------------------------------------------------------------------------------- |
| *Tenant*       | A paying customer of the SaaS platform; mapped 1‑to‑1 to an organisation domain   |
| *JIRA API key* | Long‑lived personal token generated in Atlassian Account; required for REST calls |
| *Issue*        | A JIRA ticket of type *Bug* (other types filtered out)                            |
| *Session*      | Front‑end JWT representing an authenticated app user                              |
| *Backend*      | The NestJS service that brokers all JIRA calls and stores metadata                |

---

## 3 · Stakeholders

* **Tenant Admin** – manages users and JIRA connection for their organisation.
* **Product Owner** – defines feature backlog & priority.
* **Frontend Engineers** – build React UI.
* **Backend Engineers** – implement API façade & persistence.
* **Security Lead** – reviews crypto, tenancy isolation, OAuth migration.
* **DevOps/SRE** – owns CI/CD, monitoring, infra cost.

---

## 4 · High‑Level Use‑Case Flow (per Tenant)

1. **Tenant provisioning** → Platform owner signs‑up tenant or Self‑Serve signup (future).
2. **Tenant Admin login** → enters local credentials → receives tenant‑scoped JWT.
3. **Connect JIRA** (first run) → supplies base‑URL + personal API key → token encrypted & verified.
4. **Project List** → `/project/search` pulled via tenant’s JIRA → rendered.
5. **Select Project** → fetch Bug issues (paginated).
6. **Issue Details** → show summary, comments, attachments.

---

## 5 · Functional Requirements

### 5.1 Authentication

* **REQ‑A1** Users authenticate **within their tenant namespace** (email + password hashed with Argon2).
* **REQ‑A2** JWT contains `tenant_id`, `user_id`, `roles`, expires in 15 min; refresh token 7 d.
* **REQ‑A3** Cross‑tenant requests are rejected with HTTP 403.

### 5.2 Tenant & JIRA On‑Boarding

* **REQ‑T1** Tenant Admin can create/update/delete JIRA credential(s) for their instance(s).
* **REQ‑T2** Credential is validated via JIRA `/myself`; stored AES‑256‑GCM with tenant‑specific KMS CMK.
* **REQ‑T3** Tenant Admin can rotate or revoke tokens without service downtime.

### 5.3 Project & Issue Browsing

* **REQ‑P1** `GET /tenants/{tid}/projects` returns accessible projects (cached 30 s).
* **REQ‑I1** `GET /tenants/{tid}/projects/{pkey}/issues?type=Bug&page=…` paginates 25 items.
* **REQ‑I2** Selecting row fires `GET /tenants/{tid}/issues/{ikey}`.

### 5.4 Filtering, Sorting, Search

Identical to v1.0 but endpoints are prefixed by `/tenants/{tid}`.

### 5.5 Session & Logout

Unchanged from v1.0, but refresh endpoint validates `tenant_id` consistency.

---

## 6 · Non‑Functional Requirements

| Category            | Goal                                                                                           |
| ------------------- | ---------------------------------------------------------------------------------------------- |
| **Performance**     | p99: project list < 1 s for 50 projects; issue list < 1.5 s for 200 items (per tenant).        |
| **Scalability**     | *Multi‑tenant SaaS* – 10 000 tenants, 100 concurrent per tenant; stateless pods autoscale HPA. |
| **Security**        | Full tenant isolation; no cross‑tenant data leaks; Keys encrypted; TLS everywhere.             |
| **Usability**       | WCAG 2.1 AA, keyboard navigation, mobile‑responsive.                                           |
| **Maintainability** | 90 % unit‑test coverage; ESLint + Prettier; schema migrations via Prisma or Liquibase.         |
| **Observability**   | Logs & metrics tagged by `tenant_id`; per‑tenant dashboards & cost reports.                    |

---

## 7 · System Architecture

```text
┌──────────────────────────┐  HTTPS   ┌────────────────────────┐  HTTPS  ┌──────────────┐
│       React SPA          │────────▶│  API Gateway / Backend │────────▶│  JIRA Cloud  │
└──────────────────────────┘         │  (NestJS + Multi‑Tenant)│        └──────────────┘
        ▲  WebSockets                │• Auth ∙ TenantGuard    │
        │  (issue push)              │• JiraService           │
        │                            │• TenantAwareRepos      │
        │                            └────────────┬───────────┘
        │                                         │
        ▼                                         ▼
┌───────────────────┐        Row‑level          ┌─────────────────────┐
│  Redis (cache)    │        isolation         │  PostgreSQL / Aurora │
└───────────────────┘                           └─────────────────────┘
```

* **TenantGuard** injects `tenant_id` from JWT into request context.
* **Row‑level security (RLS)** enforced at DB: `tenant_id = current_setting('app.tenant_id')`.
* Optional **schema‑per‑tenant** for large enterprise plans.

### 7.1 Frontend

Same stack as v1.0; now stores `tenant_id` in auth context and prefixes API routes.

### 7.2 Backend Enhancements

* `TenantModule` provides guards, interceptors & decorators.
* DB connection pool sets `SET app.tenant_id = :tid` on every request.

### 7.3 Persistence Schema

| Table              | Key Columns                                                                                 | Notes |
| ------------------ | ------------------------------------------------------------------------------------------- | ----- |
| `tenants`          | `id` UUID PK, `name`, `plan`, `created_at`                                                  |       |
| `users`            | `id` UUID PK, `tenant_id` FK → `tenants.id`, `email`, `password_hash`, `role`, `created_at` |       |
| `jira_credentials` | `id` PK, `tenant_id` FK, `user_id` FK nullable, `base_url`, `api_key_enc`, `updated_at`     |       |
| `project_cache`    | `id` PK, `tenant_id` FK, `project_key`, `json`, `fetched_at`                                |       |
| `issue_cache`      | `id` PK, `tenant_id` FK, `issue_key`, `json`, `fetched_at`                                  |       |
| `refresh_tokens`   | `id` PK, `tenant_id` FK, `user_id` FK, `token_hash`, `expires_at`                           |       |

---

## 8 · Detailed Component Design (delta)

Only changes from v1.0:

* `<TenantSelector>` component (future) for users belonging to multiple tenants.
* Server `TenantGuard` + `TenantInterceptor` inject `tenant_id` into logs, DB session & Redis key prefixes.

---

## 9 · API Specification (tenant‑scoped)

| Method & Path                               | Auth?  | Purpose                        |
| ------------------------------------------- | ------ | ------------------------------ |
| `POST /tenants/{tid}/auth/login`            | No     | Login within tenant namespace. |
| `POST /tenants/{tid}/auth/refresh`          | Yes RT | Rotate tokens.                 |
| `POST /tenants/{tid}/jira/credentials`      | TA     | Add/rotate JIRA credential.    |
| `GET /tenants/{tid}/projects`               | Yes    | List projects.                 |
| `GET /tenants/{tid}/projects/{pkey}/issues` | Yes    | Paginate Bug issues.           |
| `GET /tenants/{tid}/issues/{ikey}`          | Yes    | Issue details.                 |
| `POST /tenants/{tid}/auth/logout`           | Yes    | Invalidate RT.                 |

TA = Tenant Admin privilege required.

All error responses remain RFC 7807.

---

## 10 · Error‑Handling & Edge Cases (additions)

| #   | Scenario                   | Behaviour                                    |
| --- | -------------------------- | -------------------------------------------- |
| E‑8 | Missing `tenant_id` in JWT | 400 "Tenant context required"                |
| E‑9 | Tenant suspended           | 423 Locked – UI shows billing/renewal banner |

---

## 11 · Security Considerations (additions)

* **Row‑Level Security (RLS)** in Postgres + TenantGuard in code.
* **Per‑tenant KMS keys** for JIRA token encryption (optional enterprise).
* **Rate limiting** – sliding window per `tenant_id` to avoid noisy neighbour.

---

## 12 · DevOps / Observability (delta)

* **Istio** or **NGINX‑IC** adds `X‑Tenant‑ID` header forwarding for traceability.
* **Prometheus** labels every metric with `tenant_id`; Grafana dashboards templated by tenant.
* **Billing** pipeline aggregates per‑tenant API usage/costs nightly.

---

## 13 · Testing Strategy (additions)

* Tenant isolation unit tests → verify cross‑tenant data access blocked.
* Chaos testing: simulate one tenant’s heavy load; confirm others unaffected.

---

## 14 · Roadmap Updates

8. **Self‑serve Tenant Sign‑Up & Plan Upgrades** (Stripe + SaaS metrics).
9. **JIRA Data Webhooks** – incremental sync instead of polling.

---

## 15 · Open Questions

* Will we offer *schema‑per‑tenant* for premium plans or rely solely on RLS?
* Desired onboarding flow: invite link vs SSO automatic provisioning?
* Do we cap per‑tenant JIRA API throughput or implement dynamic back‑off per token?

---

© 2025 ACME Solutions – All rights reserved.
