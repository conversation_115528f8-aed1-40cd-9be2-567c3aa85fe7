### High-level visual language & brand tone

| Aspect                | Direction                                                                                                                                                     |
| --------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **Overall feel**      | Clean · data-dense · enterprise-grade—similar to Atlassian Cloud, but with lighter chrome so the **issue data** is the hero.                                  |
| **Colour palette**    | Brand primary #2F80ED (buttons, active states) • Accent #56CCF2 (links, badges) • Greys #F7F8FA → #1F1F1F • Success #27AE60 • Warning #F2994A • Error #EB5757 |
| **Typography**        | Inter / Roboto, 14 px base, 20 px line-height • 600 weight for headings • 400 for body.                                                                       |
| **Component library** | Material-UI 6 with custom theme overrides (rounded-lg corners = 10 px, subtle shadow *0 2 4 rgba(0,0,0,.06)*).                                                |
| **Spacing rhythm**    | 4-pt grid; section gutters = 24 px, component padding = 12 px.                                                                                                |
| **Motion**            | Framer-motion 150 ms ease-out for panel slides, table row hover lift (translateY-2).                                                                          |

---

## 1 Login / Tenant gateway

```
┌──────────────────────────────────────────────┐
│              ACME JIRA Browser              │
│ ──────────────────────────────────────────── │
│ [ Tenant sub-domain dropdown ▾ ]            │
│ – or –                                      │
│ [ custom tenant slug ]  .app.example.com    │
│                                              │
│ Email  _______________________              │
│ Password _____________________ ⃝            │
│ [ Log in ]                                  │
│                                              │
│  Forgot password?   •  Sign up new tenant   │
└──────────────────────────────────────────────┘
```

*Centered card on pale grey (#F7F8FA) canvas; brand logo top; tenant selector first, establishing multi-tenant mindset.*

---

## 2 Main application shell (after auth)

```
┌───────────────────────────────────────────────────────────────────────────┐
│  ▌ Tenant: ACME Inc.                     🔍  [Quick search…]  🛎  👤 RJ │
└───────────────────────────────────────────────────────────────────────────┘
  ↑ Global top-bar (60 px)  ·  left-aligned product name, tenant badge, dark text on white
```

### 2.1 Primary layout (Desktop ≥1024 px)

```
┌───────────────────────────┬──────────────────────────────────────────────┐
│  ■  Projects sidebar      │  Issue workspace                             │
│  ───────────────────────  │ ┌──────────────────────────────────────────┐ │
│  ⌕ Filter projects …      │ │ ▼ Project: WEB (152 open bugs)          │ │
│  --------------------------------------------------------------         │
│  • WEB-App                │ │ 🟢  Search bugs …           ⏩ Filters… │ │
│  • MOBILE-App             │ │ ------------------------------------------------│
│  • BACKEND-Core           │ │ Key  Summary      Status  Pri  Assignee Updated │
│  … virtualised list       │ │ ------------------------------------------------│
│                           │ │ WEB-123 page crash  InProg  High  Alice   2 d  │
│                           │ │ … rows paginate 25 …                              │
│                           │ └──────────────────────────────────────────────────┘
│                           │ ┌──── Issue detail pane (slide-in) ──────────────┐│
│                           │ │  WEB-123  Null pointer on dashboard           ││
│                           │ │  Status: In Progress • Priority: High        ││
│                           │ │  Description …                               ││
│                           │ │  ⭮ Comments | 📎 Attachments | 🔗 Links …    ││
│                           │ └───────────────────────────────────────────────┘│
└───────────────────────────┴──────────────────────────────────────────────┘
```

| Area                 | Notes & UX behaviour                                                                                                                              |
| -------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------- |
| **Projects sidebar** | 240 px fixed, collapsible (`⌘+B`) • search-as-you-type filter • shows project avatar + key + red badge for open-bug count.                        |
| **Issue table**      | TanStack Table with **sticky header**, resizable columns • infinite-scroll loads next page • row hover reveals “↗ Open in JIRA” icon.             |
| **Detail pane**      | Slides from the right (max-width = 420 px) • maintains scroll state if user clicks back to table • tabs lazy-load content (comments/attachments). |
| **Empty state**      | Grey illustration + message “No open bugs in this project 🎉” • CTA to change filters.                                                            |
| **Error banner**     | Toast top-right for 5 s; persistent red banner under top-bar for JIRA auth failures.                                                              |

### 2.2 Responsive rules

| Breakpoint             | Adaptation                                                                              |
| ---------------------- | --------------------------------------------------------------------------------------- |
| **Tablet 768-1023 px** | Sidebar overlays (hamburger) • issue detail becomes full-screen drawer.                 |
| **Mobile < 768 px**    | Single-column: project dropdown on top, table becomes cards (key, summary, pill chips). |

---

## 3 Visual samples

*Colour usage*

* Primary buttons solid #2F80ED, hover darken 6 %.
* Table header text #4F4F4F on #FAFBFC; zebra rows #FFFFFF / #F9FAFB.
* Status chips: “To Do” #BDBDBD, “In Prog” #2D9CDB, “Done” #27AE60.

*Iconography*

* Lucide-react icon set—consistent stroke-width 1.5 px.
* Each priority level shows a chevron-up filled icon in chip colour scale.

*Micro-interactions*

* Sidebar collapse animates width 240 → 72 px in 150 ms.
* Skeleton loaders shimmer while JIRA calls in-flight.
* Press **k/j** keys to move table selection up/down (command-palette vibe).

---

## 4 Accessibility & theming

* ARIA roles for tables, tabs, live-region for toast updates.
* Prefers-color-scheme **dark mode**: background #161A20, surfaces #1F242B, text #EDEFF1, primary #3780FF (lighten in dark).
* All interactive elements meet 4.5:1 contrast.
* Focus ring 2 px (#2F80ED) with 2 px offset for clear keyboard nav.

---

## 5 Future polish ideas

1. **Command-K** palette → quick switch project / jump to issue by key.
2. **Contextual insights** panel (Charts tab) with mini trends of new-vs-closed bugs.
3. **Theme tokens per tenant** so customers can match their own brand colours.

---

This narrative should let a designer or front-end engineer build high-fidelity mock-ups—or we can iterate on any section (e.g., mobile nav patterns, dark-mode nuances) as you prefer!

### “Monsoon Kasavu” — a subtle Kerala-inspired theme for your SaaS UI

> **Design goal:** evoke Kerala’s lush backwaters, monsoon rain, and *kasavu* hand-loom borders **without** resorting to clichés (no coconut trees, no Kathakali masks).
> **Mood words:** calm • fluid • organic • quietly luxurious.

---

#### 1. Colour system

| Token                                                                                       | Hex       | Visual cue                          | Usage                       |
| ------------------------------------------------------------------------------------------- | --------- | ----------------------------------- | --------------------------- |
| `--mk-sea-blue-500`                                                                         | `#2079A6` | backwater lagoon at dusk            | App-bar, primary buttons    |
| `--mk-monsoon-green-500`                                                                    | `#2E8F6E` | fresh banana-leaf / monsoon foliage | Success, active states      |
| `--mk-kasavu-gold-400`                                                                      | `#C9A85D` | hand-loom sari border thread        | Accent strokes, focus rings |
| `--mk-mullappoo-white`                                                                      | `#F7F9F4` | jasmine blossom                     | Page background             |
| `--mk-arecanut-brown-600`                                                                   | `#754C24` | spice markets / polished wood       | Text headings               |
| `--mk-shadow-100`                                                                           | `#E8ECEA` | diffused monsoon overcast           | Card surfaces               |
| Dark-mode simply inverts the lightness while **keeping hue** (e.g. `#0D2E3E` for blue-700). |           |                                     |                             |

---

#### 2. Shape language – “Kasavu-cut rectangles”

* **Kasavu-cut:** every UI container is a rounded rectangle **except the top-right corner, which stays perfectly square**
  → reminiscent of the crisp gold border ending on a Kerala sari.
* Radius: `12 px` on three corners (`border-radius: 12px 0 12px 12px;`).
  Easy to implement, recognizable at a glance, works at all sizes.

> Buttons, cards, modals, dropdown menus—everything shares the same asymmetric radius so the theme feels coherent.

---

#### 3. Micro-textures & patterns (optional but delightful)

| Element                      | Treatment                                                                                                |
| ---------------------------- | -------------------------------------------------------------------------------------------------------- |
| **Header bar**               | ultra-subtle repeating diagonal wave (1 px wide, 4 px spacing, 4 % opacity) → abstract backwater ripples |
| **Card & panel backgrounds** | radial noise mask (2 % opacity) in `--mk-shadow-100` → evokes monsoon mist                               |
| **Dividers**                 | 1 px `--mk-kasavu-gold-400` at 16 % opacity — a whisper of the sari border                               |

All textures are **SVG data-URIs** so they add zero HTTP requests.

---

#### 4. Component styling examples (Material-UI or Tailwind)

```scss
/* Kasavu-cut container mixin */
@mixin kasavu-cut($radius: 12px) {
  border-radius: $radius 0 $radius $radius;
}

/* Example: primary button */
.monsoon-btn-primary {
  @include kasavu-cut;
  background: var(--mk-sea-blue-500);
  color: white;
  padding: 8px 20px;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(#000, .08);
  transition: background .15s ease-out;

  &:hover { background: shade(var(--mk-sea-blue-500), 8%); }
  &:focus-visible {
    outline: 2px solid var(--mk-kasavu-gold-400);
    outline-offset: 2px;
  }
}
```

*Cards*\\

```scss
.monsoon-card {
  @include kasavu-cut;
  background: var(--mk-shadow-100);
  padding: 24px;
}
```

*Sidebar project item*\\

```scss
.sidebar-item.active {
  background: linear-gradient(90deg,
              var(--mk-monsoon-green-500) 0%,
              transparent 100%);
}
```

---

#### 5. Motion & interaction

* **Slide & fade** panels with a gentle **overshoot 1.05→1** (Framer Motion, 200 ms).
* **Ripple effect** on buttons uses `--mk-kasavu-gold-400` tint at 28 % opacity.
* **Skeleton loaders**: shifting `--mk-shadow-100` → `#DDE3E1` gradient = “rain sweep”.

---

#### 6. Accessibility

* Colour pairs all ≥ 4.5:1 contrast.
* Focus indicators: kasavu-gold ring.
* Asymmetric radius never traps focus outlines because the square corner is always outside the ring.

---

#### 7. Why it still feels Kerala-ish (but subtle)

1. **Hue selection** mirrors Kerala’s dominant landscapes—sea-blue, foliage-green, hand-loom gold—yet avoids loud saturation.
2. **Kasavu-cut corner** is a geometric metaphor of the sari border, instantly unique but not tourist-postcard.
3. **Wave & mist textures** recall water and monsoon without depicting them.
4. Overall restraint matches the calm, slow-living aesthetic of the state.

---

#### 8. Implementation checklist

1. Define CSS variables (or Tailwind plugin) for the palette.
2. Create a reusable `KasavuBox` component (Wrapper that applies asymmetric radius).
3. Extend Material-UI theme:

   ```ts
   shape: { borderRadius: 12 },        // base
   components: {
     MuiPaper: { styleOverrides: { root: { borderRadius: '12px 0 12px 12px' } } },
     MuiButton: { styleOverrides: { root: { borderRadius: '12px 0 12px 12px' } } },
   }
   ```
4. Add tiny SVG backgrounds via CSS `mask-image`.
5. Supply dark-mode map: CSS `@media (prefers-color-scheme: dark)` overrides.

---

With these tokens, asymmetrical-radius mixin, and two SVG textures, the look is **distinctive, brandable, low-effort to code**, and a quiet nod to Kerala’s heritage. Feel free to iterate colours or corner-radius values to match your precise brand guidelines!
