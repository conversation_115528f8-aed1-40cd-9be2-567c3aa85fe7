#!/bin/bash
# Script to run the Flask backend server

# Navigate to the backend directory
# The script assumes it's being run from the workspace root
cd "$(pwd)/backend"

# Activate virtual environment if you have one (optional)
# For example, if your venv is in backend/venv:
# if [ -d "venv" ]; then
#   echo "Activating virtual environment..."
#   source venv/bin/activate
# else
#   echo "Virtual environment not found at backend/venv. Skipping activation."
# fi

# Set environment variables for debugging if not already set
if [ -z "$LOG_LEVEL" ]; then
  export LOG_LEVEL=DEBUG
  echo "Setting LOG_LEVEL=DEBUG for detailed logging"
fi

# if [ -z "$SQLALCHEMY_ECHO" ]; then
#   export SQLALCHEMY_ECHO=True
#   echo "Setting SQLALCHEMY_ECHO=True to log SQL queries"
# fi

# Validate AES key with our diagnostic tool
echo "Checking AES key configuration..."
python tools/check_aes_key.py

# Set the required encryption key (for development)
if [ -z "$APPLICATION_AES_KEY" ]; then
  export APPLICATION_AES_KEY="00112233445566778899aabbccddeeff00112233445566778899aabbccddeeff"
  echo "Setting APPLICATION_AES_KEY for development"
fi

# Check if the database exists
if [ ! -f "instance/jira_browser_dev.db" ]; then
  echo "Database file not found. Creating database with reset_database.py..."
  # Create a fresh database using our reset script
  python reset_database.py

  # Seed the database with initial data
  echo "Seeding database with initial data..."
  python seed.py
else
  echo "Database file found. Skipping database initialization."
  # If you need to reset the database, run: python reset_database.py
fi

# Run the Flask application with socketio.run
echo "Starting Flask backend server with SocketIO on port 5045 in debug mode..."
export FLASK_DEBUG=1
export PORT=5045
# Using python app.py instead of flask run to utilize socketio.run() in app.py
python app.py