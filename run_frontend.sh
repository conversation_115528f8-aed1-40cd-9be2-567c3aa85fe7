#!/bin/bash

# Frontend Development Server Script for JIRA Bug Browser
# This script starts the React development server

set -e  # Exit on any error

echo "🚀 Starting JIRA Bug Browser Frontend Development Server..."

# Check if we're in the correct directory
if [ ! -f "package.json" ]; then
    echo "❌ Error: No package.json found in current directory"
    echo "   Please run this script from the project root directory"
    exit 1
fi

# Check if frontend directory exists
if [ ! -d "frontend" ]; then
    echo "❌ Error: Frontend directory not found"
    echo "   Please ensure you're in the correct project directory"
    exit 1
fi

# Navigate to frontend directory
cd frontend

echo "📁 Changed to frontend directory: $(pwd)"

# Check if package.json exists in frontend
if [ ! -f "package.json" ]; then
    echo "❌ Error: No package.json found in frontend directory"
    echo "   Frontend may not be properly initialized"
    exit 1
fi

# Check if node_modules exists, if not install dependencies
if [ ! -d "node_modules" ]; then
    echo "📦 Node modules not found. Installing dependencies..."
    npm install
    echo "✅ Dependencies installed successfully"
else
    echo "✅ Node modules found"
fi

# Check if .env.local exists and create if needed
if [ ! -f ".env.local" ]; then
    echo "⚙️  Creating .env.local file..."
    cat > .env.local << EOF
# Frontend Environment Configuration
REACT_APP_API_BASE_URL=http://localhost:5045
REACT_APP_ENVIRONMENT=development

# Optional: Enable React strict mode warnings
REACT_APP_STRICT_MODE=true
EOF
    echo "✅ Created .env.local with default configuration"
else
    echo "✅ Environment configuration found"
fi

# Display configuration
echo ""
echo "🔧 Frontend Configuration:"
echo "   • React App will run on: http://localhost:3000"
echo "   • API Backend expected at: http://localhost:5045"
echo "   • Environment: development"
echo ""

# Check if backend is running (optional check)
echo "🔍 Checking if backend is running..."
if curl -s -f http://localhost:5045/health > /dev/null 2>&1; then
    echo "✅ Backend is running at http://localhost:5045"
else
    echo "⚠️  Warning: Backend does not appear to be running at http://localhost:5045"
    echo "   You may need to start the backend server with: ./run_backend.sh"
    echo "   Frontend will still start, but API calls will fail until backend is running"
fi

echo ""
echo "🎯 Starting React development server..."
echo "   • Press Ctrl+C to stop the server"
echo "   • The browser should automatically open to http://localhost:3000"
echo "   • Hot reloading is enabled for development"
echo ""

# Start the React development server
# Using exec to replace the shell process with npm start
# This ensures that Ctrl+C properly terminates the React server
exec npm start