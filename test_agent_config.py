#!/usr/bin/env python3
"""
Test script for repository agent configuration functionality.
"""

import requests
import json
import sys

# API configuration
BASE_URL = "http://localhost:5045"
TENANT_ID = "550e8400-e29b-41d4-a716-446655440000"  # Example tenant ID

def test_repositories_endpoint():
    """Test that repositories endpoint returns agent configuration fields."""
    print("Testing repositories endpoint...")
    
    url = f"{BASE_URL}/tenants/{TENANT_ID}/repositories"
    
    try:
        response = requests.get(url)
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            repositories = data.get('repositories', [])
            print(f"Found {len(repositories)} repositories")
            
            if repositories:
                repo = repositories[0]
                print("\nFirst repository fields:")
                for key, value in repo.items():
                    print(f"  {key}: {value}")
                
                # Check if agent fields are present
                if 'agent_instruction' in repo and 'agent_description' in repo:
                    print("\n✅ Agent configuration fields are present in the response!")
                    return True
                else:
                    print("\n❌ Agent configuration fields are missing from the response!")
                    return False
            else:
                print("No repositories found to test")
                return True
        else:
            print(f"Error response: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to backend server")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_agent_config_endpoint():
    """Test the agent configuration update endpoint."""
    print("\nTesting agent configuration endpoint...")
    
    # This would require authentication, so we'll just check if the endpoint exists
    url = f"{BASE_URL}/tenants/{TENANT_ID}/repositories/00000000-0000-0000-0000-000000000000/agent-config"
    
    try:
        response = requests.put(url, json={
            "agent_instruction": "Test instruction",
            "agent_description": "Test description"
        })
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 403:
            print("✅ Endpoint exists and requires authentication (expected)")
            return True
        elif response.status_code == 401:
            print("✅ Endpoint exists and requires authentication (expected)")
            return True
        elif response.status_code == 404:
            print("✅ Endpoint exists but repository not found (expected)")
            return True
        else:
            print(f"Unexpected response: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to backend server")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("Repository Agent Configuration Test")
    print("=" * 50)
    
    success = True
    
    # Test repositories endpoint
    success &= test_repositories_endpoint()
    
    # Test agent config endpoint
    success &= test_agent_config_endpoint()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ All tests passed! Implementation appears to be working.")
    else:
        print("❌ Some tests failed. Check the implementation.")
    
    sys.exit(0 if success else 1)