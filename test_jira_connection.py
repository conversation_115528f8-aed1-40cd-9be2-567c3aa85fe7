#!/usr/bin/env python3
"""
Test script to check JIRA API connection and list projects.
"""

import requests
import json
from requests.auth import HTTPBasicAuth
import sys

def test_jira_connection(base_url, api_token, email=None):
    """
    Test JIRA API connection and list projects.
    
    Args:
        base_url: JIRA instance URL (e.g., https://your-domain.atlassian.net)
        api_token: JIRA API token
        email: Email associated with the API token (if using email-based auth)
    """
    # Remove trailing slash if present
    if base_url.endswith('/'):
        base_url = base_url[:-1]
    
    # If email is not provided, assume API token is the full token
    auth = HTTPBasicAuth(email, api_token) if email else None
    headers = {
        "Accept": "application/json",
        "Content-Type": "application/json"
    }
    
    # If using API token without email, add Authorization header
    if not email:
        headers["Authorization"] = f"Bearer {api_token}"
    
    # Test connection endpoints
    endpoints = [
        # Current user info
        {"url": "/rest/api/3/myself", "name": "Current User"},
        # List projects
        {"url": "/rest/api/3/project", "name": "Projects"},
        # List project types
        {"url": "/rest/api/3/project/type", "name": "Project Types"},
        # Search for issues (limit to 1)
        {"url": "/rest/api/3/search?maxResults=1", "name": "Issues Search"},
    ]
    
    print(f"Testing connection to {base_url}...")
    print("-" * 50)
    
    success = True
    
    for endpoint in endpoints:
        url = f"{base_url}{endpoint['url']}"
        print(f"\nTesting endpoint: {endpoint['name']} ({url})")
        
        try:
            response = requests.get(
                url,
                headers=headers,
                auth=auth
            )
            
            if response.status_code == 200:
                print(f"✅ Success! Status code: {response.status_code}")
                try:
                    data = response.json()
                    
                    # Print specific information based on endpoint
                    if endpoint["name"] == "Current User":
                        print(f"   Logged in as: {data.get('displayName')} ({data.get('emailAddress')})")
                        print(f"   Account ID: {data.get('accountId')}")
                    
                    elif endpoint["name"] == "Projects":
                        projects = data
                        print(f"   Found {len(projects)} projects:")
                        for idx, project in enumerate(projects[:5], 1):  # Show first 5 projects
                            print(f"   {idx}. {project.get('name')} (Key: {project.get('key')})")
                        
                        if len(projects) > 5:
                            print(f"   ... and {len(projects) - 5} more")
                        elif len(projects) == 0:
                            print("   No projects found. The user might not have access to any projects.")
                    
                    elif endpoint["name"] == "Issues Search":
                        total = data.get('total', 0)
                        print(f"   Total issues: {total}")
                        if total > 0 and 'issues' in data and len(data['issues']) > 0:
                            issue = data['issues'][0]
                            print(f"   Sample issue: {issue.get('key')} - {issue.get('fields', {}).get('summary')}")
                
                except json.JSONDecodeError:
                    print("   ⚠️ Response not in JSON format")
                    print(f"   Response text: {response.text[:100]}...")
            else:
                success = False
                print(f"❌ Failed with status code: {response.status_code}")
                print(f"   Response: {response.text}")
        
        except requests.RequestException as e:
            success = False
            print(f"❌ Request failed: {str(e)}")
    
    print("\n" + "-" * 50)
    if success:
        print("✅ All endpoint tests completed successfully!")
    else:
        print("❌ Some endpoint tests failed. Check the logs above for details.")

if __name__ == "__main__":
    if len(sys.argv) < 3:
        print("Usage: python test_jira_connection.py <base_url> <api_token> [email]")
        print("Example with token: python test_jira_connection.py https://your-domain.atlassian.net ATATT3xFfGF...")
        print("Example with email/token: python test_jira_connection.py https://your-domain.atlassian.net my_token <EMAIL>")
        sys.exit(1)
    
    base_url = sys.argv[1]
    api_token = sys.argv[2]
    email = sys.argv[3] if len(sys.argv) > 3 else None
    
    test_jira_connection(base_url, api_token, email)